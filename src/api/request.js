import { timeout, noToastUrl, getBaseUrl, app_id } from './httpRequestConfig'
import $xh from '../utlis/index'
let showLoading = function (loadingtext) {
  uni.showLoading({
    title: loadingtext ? loadingtext : '加载中..'
  })
  // store.state.loading = true
}
let hideLoading = function () {
  uni.hideLoading()
  // store.state.loading = false
}
function request({
  url,
  _secret,
  _baseUrl = getBaseUrl(),
  data = {},
  method = 'POST',
  hideloading = false,
  header = {}
}) {
  if (!noToastUrl.includes(url)) {
    !hideloading && showLoading(data.loadingtext)
  }
  return new Promise((resolve, reject) => {
    let _url
    let _data = JSON.parse(JSON.stringify(data))
    // 加入秘钥
    // _data.app_id = app_id
    // _data.func_identy = '/OperationsManagement/lectureInvitationRecord'
    // console.log(_data, _secret, $xh.getSin(_data, _secret))
    // _data.sign = $xh.getSin(_data, _secret)
    // F4X0EA2X359Q035697NWDSSFNE10G6LL
    // _data.sign = 'F4X0EA2X359Q035697NWDSSFNE10G6LL'
    _url = _baseUrl + url
    let _header = {
      // authorization: '7340dbea521d46c591d01b3c38e377bb',
      ...header
    }
    if (!_header['content-type']) {
      if (method === 'POST' || method === 'PUT') {
        _header['content-type'] = 'application/x-www-form-urlencoded'
      } else {
        _header['content-type'] = 'application/text'
      }
    }

    uni.request({
      url: _url,
      method,
      header: _header,
      data: _data,
      dataType: 'json', // 返回数据格式
      timeout,
      success(res) {
        hideLoading()
        // if (!res.data.status) {
        //   let msg =
        //     res.data && res.data.errmsg ? res.data.errmsg : '接口发生未知错误！'
        //   $xh.Toast(msg)
        //   reject(res)
        // } else {
        //   hideLoading()
        //   resolve(res.data)
        // }
        resolve(res.data)
      },
      fail(err) {
        hideLoading()
        if (!noToastUrl.includes(url)) {
          $xh.Toast('哎呦，出错啦~~')
        }
        reject(err)
      }
    })
  })
}
// 函数柯里化
export default function (_baseUrl, secret) {
  return ({ url, method, hideloading, header, data }) => {
    return request({
      url,
      method,
      hideloading,
      header,
      data,
      _secret: secret,
      _baseUrl: _baseUrl
    })
  }
}
