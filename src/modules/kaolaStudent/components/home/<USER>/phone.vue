<template>
  <view class="phone">
    <view class="close" @click="close" v-if="false">
      <image src="/static/imgs/kaolaStudent/close.png"></image>
    </view>

    <view class="topBack"></view>
    <image class="topimg2" :style="top" :src="img2"></image>
    <view class="chatting-body">
      <chatting padding="padding: 0px 12px 20px 12px;"></chatting>
    </view>
    <view class="result" v-if="false">
      <view v-if="!result && recording">
        Hi，我是考拉，可以帮你解答作业中的各种问题，咱们一起攻克难题吧</view
      >
      <view v-if="result"> {{ result }}</view>
    </view>

    <view class="phone-content">
      <view class="icon">
        <view class="wave"></view>
        <view class="wave"></view>
        <view class="wave"></view>
      </view>
      <view class="tips" v-if="recording">通话中</view>
      <view class="tips" v-if="!recording">连接中</view>
      <image
        @click="close"
        class="phone-icon"
        src="/static/imgs/kaolaStudent/phone.svg"
      ></image>
    </view>
  </view>
</template>

<script>
import chatting from '../chatting/index.vue'
import { QCloudAIVoice } from '../../../config.js'
const plugin = requirePlugin('QCloudAIVoice')
let resultText = ''
export default {
  components: {
    chatting
  },
  data() {
    return {
      top: '',
      img2: 'https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/5613174479339904841767_BgSub_%E7%94%B7%E5%AD%A92.pic%402x.png',
      img1: 'https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/7524174479334697112551_BgSub_20701744698010_.pic%201%402x.png',
      speechRecognizerManager: null,
      result: '',
      recording: false
    }
  },
  created() {
    this.$store.commit('kaolaStudent/setOriginalType', 1)
    this.init()
    this.startLy()
  },
  computed: {
    backgroup_img() {
      return `background-image: url(${this.$store.getters['kaolaStudent/getAiAvatar'].backgroup_img})`
    }
  },
  methods: {
    init() {
      const self = this
      this.speechRecognizerManager = plugin.speechRecognizerManager()

      // 开始识别
      this.speechRecognizerManager.OnRecognitionStart = res => {
        console.log('开始识别', res)
        this.recording = true
        this.result = ''
      }
      // 一句话开始
      this.speechRecognizerManager.OnSentenceBegin = res => {
        console.log('一句话开始', res)
      }
      // 识别变化时
      this.speechRecognizerManager.OnRecognitionResultChange = res => {
        console.log('识别变化时', res)
        this.result = `${resultText || ''}${res.result.voice_text_str}`
      }
      // 一句话结束
      this.speechRecognizerManager.OnSentenceEnd = res => {
        console.log('一句话结束', res)
        resultText += res.result.voice_text_str
        this.result = resultText
        self.$emit('sentenceRecognition', res.result.voice_text_str)
      }
      // 识别结束
      this.speechRecognizerManager.OnRecognitionComplete = res => {
        console.log('识别结束', res)
        wx.showToast({ title: '通话结束', icon: 'none' })

        this.recording = false
      }
      // 识别错误
      this.speechRecognizerManager.OnError = res => {
        console.log('识别错误', res)
        this.recording = false
      }
      this.speechRecognizerManager.OnRecorderStop = () => {
        console.log('超过录音时长')
        // this.startLy()
      }
    },
    startLy: async function (e) {
      const self = this
      wx.getSetting({
        success(res) {
          if (!res.authSetting['scope.record']) {
            wx.authorize({
              scope: 'scope.record',
              success() {
                // 用户已经同意小程序使用录音功能，后续调用 record 接口不会弹窗询问
                self.startAsr()
              },
              fail() {
                wx.showToast({ title: '未获取录音权限', icon: 'none' })
                wx.openSetting({
                  success: function (res) {
                    if (!res.authSetting['scope.record']) {
                      wx.showModal({
                        title: '提示',
                        content: '需要您授权录音权限',
                        showCancel: false,
                        success: function (modalSuccess) {
                          wx.openSetting({
                            success: function (settingData) {
                              if (settingData.authSetting['scope.record']) {
                                self.startAsr()
                              }
                            }
                          })
                        }
                      })
                    } else {
                      self.startAsr()
                    }
                  }
                })
                // console.log("fail auth")
              }
            })
          } else {
            self.startAsr()
            // console.log("record has been authed")
          }
        },
        fail(res) {
          // console.log("fail",res)
        }
      })
    },
    startAsr: function () {
      resultText = ''
      const params = {
        // 用户参数
        appid: QCloudAIVoice.appid,
        secretid: QCloudAIVoice.secretid,
        secretkey: QCloudAIVoice.secretkey,
        // 录音参数
        duration: 600 * 1000,
        // frameSize: 1.28,  //单位:k
        // 实时识别接口参数
        engine_model_type: '16k_zh',
        // 以下为非必填参数，可跟据业务自行修改
        // hotword_id : '08003a00000000000000000000000000',
        needvad: 1,
        // filter_dirty: 1,
        // filter_modal: 2,
        filter_punc: 1,
        // convert_num_mode : 1,
        word_info: 2,
        vad_silence_time: 200
      }
      this.speechRecognizerManager.start(params)
      wx.vibrateShort()
    },
    endLy: function (e) {
      this.recording = false
      this.speechRecognizerManager.stop()
    },
    close() {
      this.endLy()
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
.phone {
  position: fixed;
  z-index: 10;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background-color: #f3f5f8;
  background-size: 100vw 216.53vw;
  .topBack {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 64.533vw;
    // background: linear-gradient(180deg, #d4ff00 0%, #f7f9fb 100%),
    //   linear-gradient(180deg, #d4ff00 0%, #f3f5f8 100%), #eceef1;
    border-radius: 0px 0px 0px 0px;
    background-size: 100% 100%;
    background-image: url(https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/626f174562773210963083_Group%20272%402x.png);
  }
  .topimg2 {
    position: absolute;
    top: calc(69px + 25px);
    left: 50%;
    transform: translateX(-50%);
    width: 149px;
    height: 143px;
  }
  .chatting-body {
    position: absolute;
    top: 237px;
    width: 100%;
    height: calc(100% - 300px - 237px);
  }
  .close {
    position: fixed;
    left: 0;
    top: 45px;
    padding: 16px;
    z-index: 1;
    image {
      width: 24px;
      height: 24px;
    }
  }
  .result {
    position: absolute;
    bottom: 289px;
    width: 100%;
    z-index: 1;
    padding: 0 40px;
    view {
      font-weight: 500;
      font-size: 16px;
      color: #ffffff;
      line-height: 22px;
      text-align: center;
    }
  }
  .phone-content {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 300px;
    // background: linear-gradient(
    //   180deg,
    //   rgba(107, 115, 157, 0) 0%,
    //   rgba(107, 115, 157, 0.35) 26%,
    //   #6b739d 100%
    // );
    background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0) 0%,

      #ffffff 60%,
      #ffffff 100%
    );
    // background-color: #fff;
    border-radius: 0px 0px 0px 0px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;

    .phone-icon {
      width: 67px;
      height: 67px;
      margin-bottom: 60px;
    }
    .tips {
      font-weight: 400;
      font-size: 12px;
      color: #222333;
      line-height: 18px;
      margin-bottom: 36px;
      margin-top: 10px;
    }
    .icon {
      height: 19px;
      display: flex;
      align-items: center;
      .wave {
        width: 11px;
        height: 11px;
        background: #222333;
        border-radius: 13px;
        margin: 0 3px;
        animation: audio-wave 1s ease-in-out infinite;
        &:nth-child(1) {
          animation-delay: 0s;
        }

        &:nth-child(2) {
          animation-delay: 0.2s;
        }

        &:nth-child(3) {
          animation-delay: 0.4s;
        }
      }

      @keyframes audio-wave {
        0% {
          height: 11px;
        }
        50% {
          height: 19px;
        }
        100% {
          height: 11px;
        }
      }
    }
  }
}
</style>
