<template>
  <view class="course-detail">
    <view class="course-info">

    </view>
    <view class="title">课次内容</view>
    <view class="list">
      <view class="list-item"></view>
      <view class="list-item"></view>
    </view>
  </view>
</template>

<script> 
export default {
  name: 'courseDetail',
  data() {
    return {
      searchText: ""
      
    }
  }
}
</script>

<style lang="scss" scoped>
.course-detail{ 

  .title {
    font-size: 38rpx;
    font-weight: 600;
  }
}
</style>
