import store from '../store/index'
import { config } from '../api/index'
import Base64 from '@utlis/base64'
import $xh from '@utlis/index'
import { avatar, homeShareImage, jobDetailShareImage } from '../config'
import parse from './json5_parse.js'
export function logout() {
  store.state.token = ''
  store.state.userinfo = ''
  uni.clearStorageSync()
  console.log('退出')
  uni.switchTab({
    url: '/modules/kaolaStudent/pages/tabBar/home'
  })
}
// 遍历可用的菜单数据
export function filterUsabled(arr) {
  return arr.filter(item => {
    return item.is_shelf == '1'
  })
}
// 遍历菜单类型的数据
export function filterIsMenu(arr) {
  return arr.filter(item => {
    return item.type == '1'
  })
}
// 发起支付
export function pay(data, success, fail) {
  uni.getProvider({
    service: 'payment',
    success(e) {
      const { provider } = e
      console.log(provider, 'e')
      uni.requestPayment({
        provider,
        ...data,
        success(e) {
          console.log('成功')
          success && success(e)
        },
        fail(err) {
          console.log('失败', err)
          fail && fail(err)
        }
      })
    }
  })
}

// 微信分享
export const wxConfig = (config, cardInfo) => {
  wx.config(config)
  const { title, link, desc, imgUrl } = cardInfo
  wx.ready(function (e) {
    // 朋友圈分享功能
    wx.updateTimelineShareData({
      title: title,
      link: link,
      desc: desc,
      imgUrl: imgUrl,
      // 配置自定义参数
      success: function () {
        console.log('成功')
      },
      cancel: function () {
        console.log('失败')
      }
    })
    wx.onMenuShareTimeline({
      // 配置自定义参数
      title: title,
      link: link,
      desc: desc,
      imgUrl: imgUrl,
      // 配置自定义参数
      success: function () {
        console.log('成功')
      },
      cancel: function () {
        console.log('失败')
      }
    })
    // 朋友分享功能
    wx.updateAppMessageShareData({
      // 配置自定义参数
      // info
      title: title,
      link: link,
      desc: desc,
      imgUrl: imgUrl,
      // 配置自定义参数
      success: function () {
        console.log('成功')
      },
      cancel: function () {
        console.log('失败')
      }
    })
    wx.onMenuShareAppMessage({
      // 配置自定义参数
      title: title,
      link: link,
      desc: desc,
      imgUrl: imgUrl,
      // 配置自定义参数
      success: function () {
        console.log('成功')
      },
      cancel: function () {
        console.log('失败')
      }
    })
  })
}

// 文件上传
export const upLoad = tempfileurl => {
  return new Promise((resolve, reject) => {
    let formData = {}
    function success(uploadFileRes) {
      if (!uploadFileRes) {
        return reject({
          status: false,
          code_msg: '啥都没有返回！'
        })
      }
      console.log(uploadFileRes, 'jj')
      let data
      try {
        data = JSON.parse(uploadFileRes.data)
      } catch (e) {
        return reject(e)
      }

      return resolve(data.data[0])
    }
    function fail(err) {
      return reject(err)
    }
    let baseUrl = process.env.VUE_APP_BASE_API
    // 认证变量
    const biseKey = process.env.VUE_APP_BASICKEY
    const biseValue = process.env.VUE_APP_BASICVALUE
    let base64 = Base64.encode(`${biseKey}:${biseValue}`)
    uni.uploadFile({
      header: {
        // Authorization: 'Basic ' + base64
        Authorization:
          'Basic czJiMmMyYjpxT0VmZk5sTDFINVFoIXdBQGVrTiVyeTd2NTIxTno5Nw=='
      },
      url: baseUrl + '/c/base/uploadfiles',
      filePath: tempfileurl,
      name: 'file',
      formData,
      success: success,
      fail: fail
    })
  })
}
export function isLogin() {
  return !!store.state.token
}
let isRouting = false // 是否在跳转中
export function goToLogin() {
  if (isRouting) {
    return false
  }
  if (!!store.state.token) {
    return false
  }
  isRouting = true
  setTimeout(() => {
    isRouting = false
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentRoute = currentPage.route
    if (currentRoute == 'modules/kaolaStudent/pages/login/index') {
      return
    }
    
    // 获取当前页面的完整路径，包括参数
    let fullPath = `/${currentRoute}`
    if (currentPage.options) {
      const query = Object.keys(currentPage.options)
        .map(key => `${key}=${currentPage.options[key]}`)
        .join('&')
      if (query) {
        fullPath += `?${query}`
      }
    }
    
    // 直接使用uni.navigateTo而不是$xh.push，避免权限检查
    uni.navigateTo({
      url: `/modules/kaolaStudent/pages/login/index?redirect=${encodeURIComponent(fullPath)}`,
      fail: err => {
        console.error('Failed to navigate to login page:', err)
      }
    })
  }, 200)

  return true
}

export function generateKoalaName() {
  // 定义可能的字符（数字和小写字母）
  const chars = '0123456789abcdefghijklmnopqrstuvwxyz'
  let result = '考拉'
  // 生成4位随机字符
  for (let i = 0; i < 4; i++) {
    const randomIndex = Math.floor(Math.random() * chars.length)
    result += chars[randomIndex]
  }
  return result
}
export function json_parse(str) {
  let obj = {}

  try {
    let reg = new RegExp('``````json')
    if (reg.test(str)) {
      obj = str
        .split('``````json')
        .filter(s => s)
        .map(s => {
          return parse(s)
        })
    } else {
      obj = parse(str)
    }
  } catch (e) {
    $xh.Toast('JSON解析错误')
  }
  return obj
}
