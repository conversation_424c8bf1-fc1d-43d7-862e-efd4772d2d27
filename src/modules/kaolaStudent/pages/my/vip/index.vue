<template>
  <view class="vip-page">
    <navbar class="navbar" title="会员中心"></navbar>

    <!-- 会员卡片区域 -->
    <view class="vip-cards">
      <view class="scroll-container">
        <!-- 左切换按钮 -->
        <view
          class="scroll-btn left-btn"
          @click="scrollToPrev"
          v-if="vipCards.length > 1 && currentIndex > 0"
        >
          <text class="btn-icon">◀</text>
        </view>

        <!-- 右切换按钮 -->
        <view
          class="scroll-btn right-btn"
          @click="scrollToNext"
          v-if="vipCards.length > 1 && currentIndex < vipCards.length - 1"
        >
          <text class="btn-icon">▶</text>
        </view>

        <!-- 只显示当前选中的会员卡片 -->
        <view class="single-card-container" v-if="currentVipCard">
          <view class="card-item active" :style="{ width: cardWidth + 'px' }">
            <view class="card-content">
              <view class="vip-level">{{ currentVipCard.name }}</view>
              <view class="vip-status">{{
                currentVipCard.isUnlocked
                  ? currentVipCard.vip_expire_time
                    ? '已解锁  有效期至: ' + currentVipCard.vip_expire_time
                    : '已解锁'
                  : '待解锁'
              }}</view>
              <view class="vip-desc">{{ currentVipCard.description }}</view>
              <view class="vip-price">¥{{ currentVipCard.price }}/年</view>
              <view class="vip-badge">{{ currentVipCard.badge }}</view>
            </view>
          </view>
        </view>

        <!-- 卡片指示器 -->
        <view class="card-indicators" v-if="vipCards.length > 1">
          <view
            v-for="(card, index) in vipCards"
            :key="index"
            class="indicator"
            :class="{ active: index === currentIndex }"
            @click="selectVipByIndex(index)"
          ></view>
        </view>
      </view>
    </view>

    <!-- 会员权益对比表格 -->
    <view class="vip-benefits">
      <view class="benefits-title">会员权益</view>

      <view class="benefits-table">
        <view class="table-header">
          <view class="header-item">会员类型</view>
          <view
            v-for="vipCard in vipCards"
            :key="vipCard.level"
            class="header-item"
            >{{ vipCard.name }}</view
          >
        </view>

        <view class="table-row">
          <view class="row-item">直播</view>
          <view
            v-for="vipCard in vipCards"
            :key="vipCard.level"
            class="row-item"
          >
            <text :class="vipCard.benefits.live ? 'check' : 'no-check'">
              {{ vipCard.benefits.live ? '✓' : '×' }}
            </text>
          </view>
        </view>

        <view class="table-row">
          <view class="row-item">录播</view>
          <view
            v-for="vipCard in vipCards"
            :key="vipCard.level"
            class="row-item"
          >
            <text :class="vipCard.benefits.record ? 'check' : 'no-check'">
              {{ vipCard.benefits.record ? '✓' : '×' }}
            </text>
          </view>
        </view>

        <view class="table-row">
          <view class="row-item">老师资历</view>
          <view
            v-for="vipCard in vipCards"
            :key="vipCard.level"
            class="row-item"
            >{{ vipCard.teacherLevel }}</view
          >
        </view>

        <view class="table-row">
          <view class="row-item">服务</view>
          <view
            v-for="vipCard in vipCards"
            :key="vipCard.level"
            class="row-item"
            >{{ vipCard.serviceLevel }}</view
          >
        </view>
      </view>
    </view>

    <!-- 协议提示 -->
    <view class="agreement-tip">
      <text @click="viewAgreement">开通前请阅读《会员服务协议》</text>
    </view>

    <!-- 底部支付区域 -->
    <view class="bottom-payment">
      <view class="current-price">¥{{ currentPrice }}</view>
      <button
        class="pay-button"
        :class="{ disabled: isVipUnlocked }"
        @click="confirmPayment"
        :disabled="isVipUnlocked"
      >
        {{ isVipUnlocked ? '已解锁' : '确认协议并支付' }}
      </button>
    </view>
  </view>
</template>

<script>
import navbar from '../../../components/commen/navbar.vue'
import { student } from '../../../api/index.js'

export default {
  components: {
    navbar
  },
  data() {
    return {
      currentVipLevel: 1, // 当前选中的会员等级
      currentPrice: 0, // 当前选中的价格
      vipCards: [], // 会员卡片数据
      isVipUnlocked: false, // 当前选中的会员是否已解锁
      loading: false, // 加载状态
      currentIndex: 0, // 当前选中的索引
      cardWidth: 0, // 卡片宽度
      currentGoodsId: 0 // 当前选中的商品ID
    }
  },
  computed: {
    // 计算属性：当前选中的会员卡片
    currentVipCard() {
      return this.vipCards[this.currentIndex] || null
    }
  },
  mounted() {
    // 页面加载时获取会员信息
    this.getVipCards()
    // 计算卡片宽度
    this.calculateCardWidth()
    // 监听窗口大小变化，重新计算卡片宽度
    uni.getSystemInfo({
      success: res => {
        this.windowWidth = res.windowWidth
      }
    })
    uni.onWindowResize(() => {
      this.calculateCardWidth()
    })
  },
  methods: {
    // 计算卡片宽度
    calculateCardWidth() {
      uni.getSystemInfo({
        success: res => {
          // 卡片宽度为屏幕宽度的80%，留出一些边距
          this.cardWidth = res.windowWidth * 0.8
        }
      })
    },

    // 滚动到上一个卡片
    scrollToPrev() {
      if (this.currentIndex > 0) {
        this.selectVipByIndex(this.currentIndex - 1)
      }
    },

    // 滚动到下一个卡片
    scrollToNext() {
      if (this.currentIndex < this.vipCards.length - 1) {
        this.selectVipByIndex(this.currentIndex + 1)
      }
    },

    // 通过索引选择会员
    selectVipByIndex(index) {
      if (index >= 0 && index < this.vipCards.length) {
        this.currentIndex = index
        this.currentVipLevel = this.vipCards[index].level
        this.currentPrice = this.vipCards[index].price
        this.isVipUnlocked = this.vipCards[index].isUnlocked
        // 新增：获取当前选中卡片的goods_id
        this.currentGoodsId = this.vipCards[index].goods_id || 0
      }
    },

    // 获取会员卡片信息
    getVipCards() {
      this.loading = true
      student
        .getVipList()
        .then(res => {
          // 假设接口返回的数据结构如下
          // 在实际项目中，根据后端返回的数据结构进行调整
          if (res && res.data && res.data) {
            this.vipCards = res.data
            // 设置默认选中的会员等级
            if (this.vipCards.length > 0) {
              this.selectVipByIndex(0)
              this.calculateCardWidth()
            }
          } else {
            // 如果接口调用失败，使用模拟数据
            this.setMockVipCards()
          }
        })
        .catch(err => {
          console.error('获取会员信息失败:', err)
          // 接口调用失败时，使用模拟数据
          this.setMockVipCards()
        })
        .finally(() => {
          this.loading = false
        })
    },

    // 设置模拟的会员卡片数据
    setMockVipCards() {
      this.vipCards = [
        {
          level: 1,
          name: '初级会员',
          description: '可查看部分直播内容',
          price: 0.1,
          badge: 'V1',
          isUnlocked: false,
          benefits: {
            live: true,
            record: true
          },
          goods_id: 0,
          teacherLevel: '初级',
          serviceLevel: '教务'
        }
      ]

      // 默认选中第一个会员等级
      if (this.vipCards.length > 0) {
        this.selectVipByIndex(0)
        this.calculateCardWidth()
      }
    },

    // 切换会员等级
    selectVipLevel(level, index) {
      this.selectVipByIndex(index)
    },

    // 查看会员服务协议
    viewAgreement() {
      this.$xh.push('kaolaStudent', 'pages/agreement/vipServiceAgreement')
    },

    // 确认支付
    confirmPayment() {
      if (this.isVipUnlocked) {
        return // 如果已解锁，不执行支付
      }

      // 创建VIP订单
      this.createVipOrder()
    },

    // 创建VIP订单
    createVipOrder() {
      // 这里调用API创建VIP订单
      // 实际项目中需要根据后端接口调整参数
      student
        .buyvip({
          goods_id: this.currentGoodsId,
          goods_num: 1,
          vip_level: this.currentVipLevel // 添加会员等级参数
        })
        .then(res => {
          console.log('buyvip成功:', res)
          console.log('pay_amount值:', res.data.OrderResult.pay_amount)
          console.log('完整的响应数据结构:', res.data.OrderResult)

          this.$xh.push(
            'kaolaStudent',
            `pages/my/vip/pay?order_id=${res.data.OrderResult.order_id}&flow_id=${res.data.OrderResult.flow_id}&pay_amount=${res.data.OrderResult.pay_amount}`
          )
        })
        .catch(err => {
          console.error('buyvip失败:', err)
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.vip-page {
  background-color: #f7f9fb;
  min-height: 100vh;
  position: relative;
}

/* 会员卡片区域 */
.vip-cards {
  padding: 16px 12px;
}

.scroll-container {
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.scroll-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.left-btn {
  left: 10px;
}

.right-btn {
  right: 10px;
}

.btn-icon {
  font-size: 16px;
  color: #333;
}

.single-card-container {
  display: flex;
  justify-content: center;
  width: 100%;
}

.card-item {
  flex-shrink: 0;
  margin: 0;
  margin-right: 12px;
  width: 200px;
  background: linear-gradient(135deg, #eaff00 0%, #b9e200 100%);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;

  // 添加装饰性元素
  &::before {
    content: '';
    position: absolute;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    right: -60px;
    bottom: -60px;
  }

  &::after {
    content: '';
    position: absolute;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    right: 10px;
    bottom: 40px;
  }

  &.active {
    border: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(185, 226, 0, 0.2);
  }
}

.card-content {
  padding: 20px;
  position: relative;
  z-index: 1;
}

.vip-level {
  font-weight: 600;
  font-size: 18px;
  color: #222333;
  margin-bottom: 4px;
}

.vip-status {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.vip-desc {
  font-size: 14px;
  color: #222333;
  margin-bottom: 12px;
}

.vip-price {
  font-weight: 600;
  font-size: 16px;
  color: #222333;
}

.vip-badge {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 24px;
  color: rgba(34, 35, 51, 0.7);
  font-weight: 700;
}

/* 会员权益对比表格 */
.vip-benefits {
  margin: 0 12px 24px;
  background: #fff;
  border-radius: 12px;
  padding: 16px;
}

.benefits-title {
  font-weight: 600;
  font-size: 16px;
  color: #222333;
  margin-bottom: 16px;
}

.benefits-table {
  width: 100%;
  overflow-x: auto;
}

.table-header {
  display: flex;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
  margin-bottom: 8px;
}

.header-item {
  flex: 1;
  font-size: 14px;
  color: #666;
  text-align: center;
  padding: 0 4px;
}

.table-row {
  display: flex;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;

  &:last-child {
    border-bottom: none;
  }
}

.row-item {
  flex: 1;
  font-size: 14px;
  color: #222333;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
}

.check {
  color: #4ade80;
  font-size: 16px;
  font-weight: 600;
}

.no-check {
  color: #d1d5db;
  font-size: 16px;
}

/* 协议提示 */
.agreement-tip {
  text-align: center;
  font-size: 12px;
  color: #666;
  margin-bottom: 24px;
  padding: 0 12px;
}

.agreement-tip text {
  text-decoration: underline;
}

/* 底部支付区域 */
.bottom-payment {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.current-price {
  font-weight: 600;
  font-size: 24px;
  color: #ff6111;
}

.pay-button {
  width: 240px;
  height: 48px;
  background: #ff6111;
  color: #fff;
  border: none;
  border-radius: 24px;
  font-size: 16px;
  font-weight: 600;
  line-height: 48px;
  transition: all 0.3s ease;

  &.disabled {
    background: #ccc;
    color: #fff;
    cursor: not-allowed;
  }
}

/* 导航栏样式 */
.navbar {
  background: linear-gradient(135deg, #eaff00, #b9e200);
}

.navbar >>> .nav-bar-title {
  color: #222333 !important;
  font-weight: 600;
}

.navbar >>> .back-btn {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23222333"><path d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"/></svg>') !important;
}

.navbar >>> .nav-bar-right {
  color: #222333 !important;
}
</style>