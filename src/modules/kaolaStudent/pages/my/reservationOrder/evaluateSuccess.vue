<template>
  <div class="success-page">
    <navbar class="navbar" title="课程评价 "></navbar>
    <view class="success">
      <image
        src="http://xy-kaola.oss-cn-beijing.aliyuncs.com/public/6b6917471421923894492_Frame%402x.png"
      ></image>
      <view> 谢谢你的支持 </view>
      <view>学业顺利 天天进步哦～</view>
    </view>

    <view class="page-bottom-but-fixed">
      <view class="page-bottom-but pay-but" @click="back()">知道了</view>

      <view v-if="false" class="tips" @click="downloadImage">分享好友</view>
    </view>
  </div>
</template>

<script>
import { activity } from '../../../api'
import navbar from '../../../components/commen/navbar.vue'
export default {
  components: {
    navbar
  },
  data() {
    return {
      query: {
        amount: 0
      },
      qrCodeUrl: ''
    }
  },
  onLoad(query) {
    this.query = query
    this.wxQrCode()
  },
  methods: {
    wxQrCode() {
      // ?activity_id=562223936276794609&promotion_plan_id=562223936897551601&is_student=1&student_id=sfsfs
      activity
        .wxQrCode({
          activity_id: '562223936276794609',
          promotion_plan_id: '562223936897551601',
          is_student: '1',
          student_id: this.$store.state.kaolaStudent.userinfo.student_id
        })
        .then(res => {
          console.log('分享接口数据', res.data)
          if(res.data && res.data.length > 0){
            const e = res.data[0]
            this.qrCodeUrl = this.$xh.completepath(e.poster_url)
          }
        })
    },
    downloadImage() {
      const that = this
      wx.downloadFile({
        url: this.qrCodeUrl, // 替换为你的图片URL
        success(res) {
          if (res.statusCode === 200) {
            wx.showShareImageMenu({
              path: res.tempFilePath,
              success: () => {},
              fail(err) {
                console.log('分享图片报错', err)
              }
            })
          }
        },
        fail(err) {
          console.log('图片点击报错', err)
        }
      })
    },
    back() {
      uni.navigateBack({
        delta: 2
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.page-bottom-but-fixed {
  display: flex;
  flex-direction: column;
  align-items: center;

  .tips {
    margin-top: 20px;
    font-weight: 400;
    font-size: 13px;
    color: #708600;
    line-height: 20px;
    margin-bottom: 32px;
    white-space: pre;
  }
}
.success-page {
  height: 100vh;
  overflow-y: auto;
  background: #f3f5f8;
}
.success {
  display: flex;
  flex-direction: column;
  margin-top: 143px;
  align-items: center;
  view {
    font-weight: 400;
    font-size: 14px;
    color: rgba(34, 35, 51, 0.85);
    line-height: 23px;
  }
  image {
    width: 187px;
    height: 140px;
    margin-bottom: 20px;
  }
}
.bottom-but {
  position: fixed;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  .tips {
    font-weight: 400;
    font-size: 14px;
    color: rgba(34, 35, 51, 0.85);
    line-height: 20px;
    margin-bottom: 18px;
    view {
      text-align: center;
    }
  }
}
.pay-but {
  z-index: 1;
  width: 231px;
  text-align: center;
  height: 44px;
  line-height: 44px;
  background: #222333;
  border-radius: 33px 33px 33px 33px;
  font-weight: bold;
  font-size: 14px;
  color: #ffffff;
}
</style>
