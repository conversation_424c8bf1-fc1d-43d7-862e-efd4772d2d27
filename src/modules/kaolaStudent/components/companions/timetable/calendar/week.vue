<template>
  <swiper
    class="week-swiper"
    circular
    :current="current"
    @change="swiperChange"
  >
    <swiper-item v-for="(item, index) of weekList" :key="index">
      <view class="table-row">
        <view
          v-for="(day, i) in item.days"
          :key="i"
          class="day-cell"
          :class="{
            'selected-day': isSelected(day.date, value),
            status1: isData(day.date)
          }"
          @click="selectDate(day.date)"
        >
          <view class="text">
            {{ day.date.getDate() }}
          </view>
        </view>
      </view>
    </swiper-item>
  </swiper>
</template>

<script>
import dayjs from 'dayjs'
export default {
  props: {
    data: {
      type: Array,
      default: []
    },
    value: {
      type: String,
      defaul: ''
    },
    initDate: {
      type: String,
      defaul: ''
    }
  },
  data() {
    return {
      weekList: [{}, {}, {}],
      page: 0,
      lastCurrent: 0,
      current: 0
    }
  },
  created() {
    this.init(0)

    this.change(0)
  },
  methods: {
    last() {
      this.current--
      if (this.current < 0) {
        this.current = 2
      }
    },
    next() {
      this.current++
      if (this.current > 2) {
        this.current = 0
      }
    },
    // 获取周数据
    getWeekData(date) {
      const days = []
      const day = date.getDay()
      const diff = day === 0 ? -6 : 1 - day
      const start = new Date(date.getTime())
      for (let i = 0; i < 7; i++) {
        const currentDate = new Date(start.getTime() + (i + diff) * 86400000)
        days.push({
          date: currentDate
        })
      }
      return { days }
    },
    swiperChange({ detail: { current } }) {
      let num = this.lastCurrent - current
      let direction = true
      if (num == -1 || num == 2) {
        this.page++
        direction = true
      } else {
        this.page--
        direction = false
      }
      if (current == 0 && direction) {
        this.init(0)
      }
      if (current == 2 && !direction) {
        this.init(-2)
      }
      this.change(current)
      this.lastCurrent = current
    },
    init(val) {
      this.weekList.forEach((e, index) => {
        let dd = dayjs(this.initDate).toDate()
        let d = new Date(dd.getTime() + 604800000 * (this.page + index + val))
        e.days = this.getWeekData(d).days
      })
    },
    change(val) {
      const current = this.weekList[val]
      this.current = val
      this.$emit('change', {
        start: dayjs(current.days[0].date).format('YYYY-MM-DD'),
        end: dayjs(current.days[current.days.length - 1].date).format(
          'YYYY-MM-DD'
        )
      })
    },
    // 选择日期
    selectDate(date) {
      this.$emit('select', dayjs(date).format('YYYY-MM-DD'))
      this.$emit('input', dayjs(date).format('YYYY-MM-DD'))
    },
    isData(d1) {
      return !!this.data.find(d2 => dayjs(d1).format('YYYY-MM-DD') == d2)
    },
    isSelected(d1, d2) {
      return dayjs(d1).format('YYYY-MM-DD') == dayjs(d2).format('YYYY-MM-DD')
    }
  }
}
</script>

<style lang="scss" scoped>
.week-swiper {
  height: 36px;
}
.table-row {
  width: 100%;
  display: flex;
  padding-top: 4px;
  padding-bottom: 6px;
  flex-wrap: wrap;
  .day-cell {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    // flex: 1;
    width: calc(100% / 7);

    .text {
      font-weight: 800;
      font-size: 16px;
      color: rgba(34, 35, 51, 0.85);
      width: 32px;
      height: 32px;
      // background: #dbf46b;
      border-radius: 8px 8px 8px 8px;
      line-height: 32px;
      text-align: center;
    }
  }
  .status1 {
    .text {
      position: relative;
      &::before {
        content: ' ';
        position: absolute;
        bottom: 0;
        left: calc(50%);
        transform: translateX(-50%);
        width: 7px;
        height: 4px;
        background: #9ab71b;
        border-radius: 8px 8px 3px 3px;
      }
    }
  }
  .selected-day {
    .text {
      background: #dbf46b;
      // border-radius: 50%;
    }
  }
  .other-month {
    .text {
      color: #ccc;
    }
  }
}
</style>
