<template>
  <div>
    <navbar class="navbar" title=" "></navbar>
    <view class="teacher-box">
      <image
        class="avatar"
        :src="
          $xh.completepath(
            info.teacher_avatar ||
              'public/5ef917440086508568967_laoshitouxiang.png'
          )
        "
      ></image>
      <view class="teacher-info">
        <view class="name">
          <view>{{ info.teacher_name }}</view>
          <!-- <image
            src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/8cea17455524882824282_Group%20190%402x.png"
          ></image> -->

          <!-- <view v-if="info.teacher_grade_name" style="margin-left: 12px">{{
            info.teacher_grade_name
          }}</view> -->

          <image
            class="grade"
            :src="$xh.teacher_grade_img(info.teacher_grade_name)"
          ></image>
        </view>
        <view class="data">
          <view class="xing">
            <image
              src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/38a1174555253755312367_Star%2011%402x.png"
            ></image>
            <view>{{ info.teacher_score }}</view>
          </view>
          <view class="fen">｜</view>
          <view class="num">
            <view style="margin-right: 2px">答疑</view>
            <view>{{ info.teacher_question_num }}次</view>
          </view>
        </view>
      </view>
      <!-- teacher_school_logo -->
      <!--   src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/36e2174555257039919651_Group%20261%402x.png" -->
      <image
        class="xueixao"
        :src="$xh.completepath(info.teacher_school_logo)"
      ></image>
    </view>

    <view class="order-info">
      <view
        class="text-item"
        v-for="item of info.goods"
        v-if="item.goods_type == 19"
      >
        <view class="label">
          <view> {{ item.goods_name }} </view>
          <view class="tips">默认时长{{ item.minutes }}分钟</view></view
        >
        <view class="value">¥{{ item.payable_amount }}</view>
      </view>
      <view
        class="text-item"
        v-for="item of info.goods"
        v-if="item.goods_type == 20"
      >
        <view class="label">
          <view> {{ item.goods_name }} ({{ item.effective_time }}分钟) </view>
          <view class="tips"
            >超时{{ item.min_times }}至{{ item.max_times }}分钟，费用{{
              item.payable_amount
            }}元</view
          >
        </view>
        <view class="value">¥{{ item.payable_amount }}</view>
      </view>
      <view class="text-item">
        <view class="label"> <view> 优惠券 </view></view>
        <view class="value empty">无可用</view>
      </view>
      <view class="text-item">
        <view class="label"> <view> 总计 </view> </view>
        <view class="value">¥{{ info.payable_amount }}</view>
      </view>
    </view>
    <view>
      <!-- {{ info }} -->
    </view>
    <view class="pay">
      <view class="price">
        <view class="actualPayment"
          ><text>总计</text><text style="margin: 0 2px 0 4px">¥</text
          ><text>{{ info.payable_amount }}</text></view
        >
        <view class="discount">已优惠0.00元</view>
      </view>
      <view
        class="but"
        @click="
          $xh.push(
            'kaolaStudent',
            'pages/my/order/pay?order_id=' +
              query.order_id +
              '&flow_id=' +
              query.flow_id
          )
        "
        >支付</view
      >
    </view>
    <askAbout v-if="false"></askAbout>
  </div>
</template>

<script>
import { appoint, account } from '../../../api'
import askAbout from '../../../components/home/<USER>/askAbout.vue'
import navbar from '../../../components/commen/navbar.vue'
export default {
  components: {
    askAbout,
    navbar
  },
  data() {
    return {
      info: {
        list: []
      },
      query: {}
    }
  },
  onLoad(query) {
    this.query = query
    this.detail(query.order_id)
  },
  computed: {
    systemMessage() {
      return this.$store.state.kaolaStudent.systemMessage
    }
  },
  methods: {
    detail(id) {
      appoint.order.DayiDetail({ order_id: id }).then(res => {
        this.info = res.data
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.pay {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  background-color: #fff;
  padding: 12px 16px 32px 16px;
  display: flex;
  justify-content: space-between;
  .price {
    .actualPayment {
      display: flex;
      align-items: baseline;
      text:nth-child(1) {
        font-weight: 400;
        font-size: 14px;
        color: #222333;
      }
      text:nth-child(2) {
        font-weight: 500;
        font-size: 14px;
        color: #ff6111;
      }
      text:nth-child(3) {
        font-weight: 500;
        font-size: 20px;
        color: #ff6111;
      }
    }
    .discount {
      font-weight: 400;
      font-size: 12px;
      color: rgba(34, 35, 51, 0.65);
      line-height: 20px;
    }
  }
  .but {
    width: 188px;
    text-align: center;
    height: 44px;
    line-height: 44px;
    background: #ff6111;
    border-radius: 33px 33px 33px 33px;
    font-weight: bold;
    font-size: 14px;
    color: #ffffff;
  }
}
.order-info {
  padding: 6px 16px;
  margin: 0 12px;
  background: #ffffff;
  border-radius: 12px 12px 12px 12px;
  .text-item {
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .label {
      view {
        font-weight: 400;
        font-size: 15px;
        color: #222333;
        line-height: 20px;
      }
      .tips {
        margin-top: 4px;
        font-weight: 400;
        font-size: 12px;
        color: rgba(34, 35, 51, 0.35);
        line-height: 14px;
      }
    }
    .value {
      font-weight: 400;
      font-size: 15px;
      color: #222333;
      line-height: 20px;
    }
    .empty {
      color: rgba(34, 35, 51, 0.35);
    }
  }
}

.teacher-box {
  margin: 0 12px 8px 12px;
  padding: 15px 16px;
  background: #ffffff;
  border-radius: 12px 12px 12px 12px;
  display: flex;
  align-items: center;
  .avatar {
    width: 38px;
    height: 38px;
    border-radius: 50%;
    margin-right: 10px;
    flex-shrink: 0;
  }
  .xueixao {
    margin-left: auto;
    width: 77px;
    height: 23px;
  }
  .teacher-info {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .name {
      display: flex;
      align-items: center;
      view {
        font-weight: 500;
        font-size: 16px;
        color: #222333;
        line-height: 20px;
        margin-right: 4px;
      }
      image {
        width: 79px;
        height: 20px;
      }
    }
    .data {
      margin-top: 8px;
      display: flex;
      align-items: center;
      .xing {
        display: flex;
        align-items: center;
        image {
          width: 16.17px;
          height: 15px;
        }
        view {
          font-weight: 500;
          font-size: 15px;
          color: #fb8105;
          line-height: 20px;
        }
      }
      .fen {
        margin: 0 8px;
        font-size: 13px;
        color: rgba(34, 35, 51, 0.35);
        line-height: 15px;
      }
      .num {
        display: flex;
        align-items: center;
        view {
          font-weight: 400;
          font-size: 13px;
          color: rgba(34, 35, 51, 0.6);
          line-height: 16px;
        }
        view:nth-child(1) {
          color: #222333;
        }
      }
    }
  }
}
</style>
