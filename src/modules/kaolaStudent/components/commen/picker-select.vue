<template>
  <view>
    <picker
      mode="selector"
      :range="options"
      range-key="name"
      confirmColor="#ff6616"
      :value="defaultIndex"
      @change="change"
    >
      <slot></slot>
    </picker>
  </view>
</template>

<script>
export default {
  props: {
    value: {
      type: String,
      default: ''
    },
    options: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      defaultIndex: 0
    }
  },
  mounted() {
    this.init()
  },
  watch: {
    options() {
      this.init()
    },
    value() {
      this.init()
    }
  },
  methods: {
    init() {
      if (this.options.length) {
        if (this.value.length) {
          let index1 = this.options.findIndex(item => item.id == this.value)
          if (index1 == -1) {
            index1 = 0
          }
          this.defaultIndex = index1
        }
      }
    },
    change(el) {
      let value = el.detail.value
      let one = this.options[value]
      this.defaultIndex = value
      this.$emit('input', one.id)
      this.$emit('update:name', one.name)
      this.$emit('change', {
        id: one.id,
        name: one.name
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
