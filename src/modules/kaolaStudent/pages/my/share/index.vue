<template>
  <view class="share-page">
    <pageList
      class="scrollpageList"
      :key="list.length"
      :loading="search.loading"
      @nextPage="getList"
      @refresh="refresh"
    >
      <view class="page-content">
        <navbar class="navbar" title=" "></navbar>
        <view class="head">
          <image
            class="title"
            src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/840c174462003396387635_Group%20158%402x.png"
          ></image>
          <image
            class="img1"
            src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/6c96174462017655547451_Group%20207%402x.png"
          ></image>

          <image
            @click="rule = true"
            class="img2"
            src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/378b174462040459964616_Group%20218%402x.png"
          ></image>
        </view>
        <view class="body">
          <view class="box">
            <image
              class="tips"
              src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/4148174462107027651369_Group%20161%402x.png"
            ></image>
            <view class="item">
              <view class="num"
                ><view>{{ success.invite_num }}</view
                ><view>人</view></view
              >
              <view class="text">成功邀请</view>
            </view>
            <view class="item">
              <view class="num"
                ><view>{{ success.invite_amount }}</view
                ><view>元</view></view
              >
              <view class="text">已获得奖励</view>
            </view>
          </view>

          <view class="box2">
            <image
              class="tips"
              src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/956b174462126990466971_Group%20164%402x.png"
            ></image>
            <view class="box2-back">
              <view class="box2-row">
                <view class="item" style="width: 149px">
                  <image
                    class="img"
                    src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/31cf174462163732026403_Group%20213%402x.png"
                  ></image>
                  <view class="step">
                    <view>STEP 1</view><view>分享链接给好友</view>
                  </view>
                </view>
                <view class="item">
                  <image
                    class="img"
                    src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/91c3174462171690770110_Group%20215%402x.png"
                  ></image>
                  <view class="step">
                    <view>STEP 2</view><view>好友注册获得新人专属福利</view>
                  </view>
                </view>
              </view>
              <view class="box2-row">
                <view class="item" style="width: 149px">
                  <image
                    class="img"
                    src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/8c63174462173867165922_Group%20214%402x.png"
                  ></image>
                  <view class="step">
                    <view>STEP 3</view><view>好友完成首单</view>
                  </view>
                </view>
                <view class="item">
                  <image
                    class="img"
                    src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/8173174462175101326601_Group%20216%402x.png"
                  ></image>
                  <view class="step">
                    <view>STEP 4</view
                    ><view
                      >邀请达成
                      <text style="color: #ff8119">获得奖励</text></view
                    >
                  </view>
                </view>
              </view>
            </view>
          </view>

          <view class="share-bug">
            <image
              @click="downloadImage(2)"
              src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/21dd174462204761442247_Group%20198%402x.png"
            ></image>

            <image
              @click="downloadImage(1)"
              src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/b2f3174462209534680327_Group%20199%402x.png"
            ></image>
          </view>

          <view class="ranking">
            <image
              class="tips"
              src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/8456174617765925316524_Group%20172%402x.png"
            ></image>

            <view class="list" :key="list.length">
              <noData v-if="list.length == 0"></noData>
              <view class="item" v-for="item of list">
                <image
                  class="avatar"
                  :src="$xh.completepath(item.avatar)"
                ></image>
                <view class="l">
                  <view class="user">
                    <view class="phone">{{ item.phone }}</view>
                    <image
                      v-if="item.student_type == 2"
                      src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/1f54174462247448413836_Group%20165%402x.png"
                    ></image>
                  </view>
                  <view class="date">{{ item.participate_time }}</view>
                </view>
                <view class="status">{{ item.invite_status_name }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="rule" v-if="rule" @click="rule = false">
        <view class="rule-body" @click.stop="() => {}">
          <image
            @click="rule = false"
            class="close"
            src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/3486174462380092020021_Frame%402x.png"
          ></image>
          <view class="title">邀请好友获赠规则</view>

          <view class="text">
            <view
              >1.您邀请的好友为新用户，好友即可获赠新人专享大券包。（特别提示：如您邀请的好友已经在考拉Al注册，则不属于新用户）
            </view>
            <view
              >2.您邀请的新用户只要完成首单消费即为邀请成功，您即可获得邀请奖励——8折优惠券。
            </view>
            <view>
              3.您的好友同一手机设备、同一手机号码仅可领取一次新人专享大券包。
            </view>
            <view
              >4.您邀请好友所获赠的抵用金仅限本人使用，用于商业牟利将有封号风险。
            </view>
            <view
              >5.用户不得以不正当、不诚信的方式参与活动，否则考拉AI有权撤销用户活动参与资格并收回用户已经获得的奖励权益（包括追偿已使用部分）
            </view>
            <view> 6.如有疑问，可咨询考拉Al在线客服。 </view>
          </view>
          <view class="page-bottom-but" @click="rule = false">知道了</view>
        </view>
      </view>

      <view class="share">
        <snapshot mode="view" id="targetShare">
          <view class="back">
            <view class="qrcode" :src="qrCodeUrl"></view>
          </view>
        </snapshot>
      </view>
    </pageList>
  </view>
</template>

<script>
import navbar from '../../../components/commen/navbar.vue'
import { student, activity } from '../../../api/index'
import pageList from '../../../components/commen/page-list.vue'
import noData from '../../../components/commen/no-data.vue'
export default {
  components: {
    navbar,
    pageList,
    noData
  },
  data() {
    return {
      rule: false,
      success: {
        invite_num: 0,
        invite_amount: 0
      },
      list: [],
      search: {
        page: 1,
        size: 10,
        loading: false
      },
      qrCodeUrl: ''
    }
  },
  onLoad() {
    this.refresh()
    this.statistics()
    this.wxQrCode()
  },
  methods: {
    wxQrCode() {
      // ?activity_id=562223936276794609&promotion_plan_id=562223936897551601&is_student=1&student_id=sfsfs
      activity
        .wxQrCode({
          activity_id: '562223936276794609',
          promotion_plan_id: '562223936897551601',
          is_student: '1',
          student_id: this.$store.state.kaolaStudent.userinfo.student_id
        })
        .then(res => {
          console.log('分享接口数据', res.data)
          if(res.data && res.data.length > 0) {
            const e = res.data[0]
            this.qrCodeUrl = this.$xh.completepath(e.poster_url)
            console.log('分享图片url', this.qrCodeUrl)
          }
          this.$store.commit('kaolaStudent/setXyppid', e.xyppid)
        })
    },
    // 在页面的onLoad或需要时调用
    downloadImage(type) {
      const that = this
      wx.downloadFile({
        url: this.qrCodeUrl, // 替换为你的图片URL
        success(res) {
          if (res.statusCode === 200) {
            if (type == 1) {
              const save = () => {
                wx.saveImageToPhotosAlbum({
                  filePath: res.tempFilePath,
                  success: () => {
                    uni.showToast({
                      title: '图片保存成功！',
                      icon: 'none'
                    })
                  },
                  fail(err) {
                    console.log('图片保存报错', err)

                    wx.openSetting({
                      success: function (res) {
                        if (!res.authSetting['scope.writePhotosAlbum']) {
                          wx.showModal({
                            title: '提示',
                            content: '需要您授权保存相册',
                            showCancel: false,
                            success: function (modalSuccess) {
                              wx.openSetting({
                                success: function (settingData) {
                                  if (
                                    settingData.authSetting[
                                      'scope.writePhotosAlbum'
                                    ]
                                  ) {
                                    // 重新尝试保存图片
                                    save()
                                  }
                                }
                              })
                            }
                          })
                        } else {
                          save()
                        }
                      }
                    })
                  }
                })
              }

              save()
            }
            if (type == 2) {
              wx.showShareImageMenu({
                path: res.tempFilePath,
                success: () => {},
                fail(err) {
                  console.log('分享图片报错', err)
                }
              })
            }
          }
        },
        fail(err) {
          console.log('图片点击报错', err)
        }
      })
    },
    share(type = 2) {
      this.createSelectorQuery()
        .select('#targetShare')
        .node()
        .exec(res => {
          const node = res[0].node
          node.takeSnapshot({
            type: 'arraybuffer', // 截图类型，可选值为'arraybuffer'或'base64'
            format: 'png', // 图片格式，可选值为'jpg'或'png'
            success: res => {
              // 成功回调函数，res中包含生成的图片数据
              // console.log('生成海报成功', res)

              const f = `${wx.env.USER_DATA_PATH}/share.png`
              const fs = wx.getFileSystemManager()
              console.log(f, 'f')
              // 将海报数据写入本地文件
              fs.writeFileSync(f, res.data, 'binary')
              // console.log(f)
              if (type == 1) {
                wx.saveImageToPhotosAlbum({
                  filePath: f,
                  success: () => {
                    uni.showToast({
                      title: '图片保存成功！',
                      icon: 'none'
                    })
                  }
                })
              }
              if (type == 2) {
                console.log(f)
                wx.showShareImageMenu({
                  path: f,
                  success: () => {}
                })
              }
            },
            fail: res => {
              // 失败回调函数，res中包含错误信息
              console.error('生成海报失败', res)
            }
          })
        })
    },
    statistics() {
      activity.statistics({}).then(res => {
        // this.list = res.data.list
        this.success.invite_num = res.data.invite_num
        this.success.invite_amount = res.data.invite_amount
      })
    },
    refresh() {
      this.search.page = 1
      this.list = []
      this.getList()
    },
    getList() {
      this.search.loading = true
      activity
        .record({
          page: this.search.page++,
          size: this.search.size
        })
        .then(res => {
          if (res.data?.list?.length) {
            this.list = this.list.concat(res.data.list)
          }
          this.search.loading = false
        })
        .catch(() => {
          this.search.loading = false
        })
    }
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>

<style lang="scss" scoped>
.share {
  position: fixed;
  top: 100px;
  left: -100vw;
  z-index: 10;
  .back {
    width: 311px;
    height: 528px;
    background-image: url(https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/6fe4174462439118731183_%E5%88%86%E4%BA%AB%E6%B5%B7%E6%8A%A5.png);
    background-size: 100% 100%;
    position: relative;
    .qrcode {
      position: absolute;
      right: 12px;
      bottom: 12px;
      width: 72px;
      height: 72px;
      background: #ffffff;
      border-radius: 4px 4px 4px 4px;
      z-index: 1;
    }
  }
}
.rule {
  position: fixed;
  z-index: 1;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  .rule-body {
    background-color: #fff;
    width: 311px;
    background-image: url(https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/17cb174462368259072194_Mask%20group%402x.png);
    background-size: 311px 128px;
    background-repeat: no-repeat;
    position: relative;
    border-radius: 16px 16px 16px 16px;
    overflow: hidden;
    .page-bottom-but {
      margin-bottom: 26px;
      margin-left: auto;
      margin-right: auto;
      margin-top: 8px;
    }
    .close {
      position: absolute;
      top: 0;
      right: 0;
      padding: 12px;
      width: 20px;
      height: 20px;
      z-index: 1;
    }
    .text {
      padding: 0 19px;
      view {
        font-family: Alibaba PuHuiTi, Alibaba PuHuiTi;
        font-weight: 400;
        font-size: 12px;
        color: #222333;
        line-height: 20px;
        margin-bottom: 16px;
      }
    }
    .title {
      padding-top: 24px;
      text-align: center;
      font-family: 江城圆体, 江城圆体;
      font-weight: normal;
      font-size: 20px;
      color: #713d09;
      line-height: 20px;
      padding-bottom: 20px;
    }
  }
}
.ranking {
  width: 100%;
  background: linear-gradient(
      180deg,
      #fce189 0%,
      rgba(252, 225, 137, 0.35) 100%
    ),
    #ffffff;
  border-radius: 20px 20px 20px 20px;
  border: 1px solid #ffffff;
  position: relative;
  .tips {
    position: absolute;
    width: 122.82px;
    height: 41px;
    left: -4px;
    top: -25px;
    z-index: 1;
  }
  .list {
    padding: 8px 8px 4px 8px;
    .item {
      background: #ffffff;
      border-radius: 12px 12px 12px 12px;
      border: 1px solid #ffffff;
      display: flex;
      align-items: center;
      padding: 14px 12px;
      margin-bottom: 4px;
      .avatar {
        width: 40px;
        height: 40px;
        background: #687487;
        border-radius: 50%;
        margin-right: 8px;
      }
      .l {
        .user {
          display: flex;
          align-items: center;
          margin-bottom: 2px;
          .phone {
            font-weight: 500;
            font-size: 16px;
            color: #222333;
            line-height: 19px;
          }
          image {
            margin-left: 4px;
            width: 48px;
            height: 20px;
          }
        }
        .date {
          font-weight: 400;
          font-size: 12px;
          color: rgba(34, 35, 51, 0.75);
          line-height: 14px;
        }
      }
      .status {
        margin-left: auto;

        width: 72px;
        text-align: center;
        height: 32px;
        line-height: 32px;
        background: #fef4d6;
        border-radius: 54px 54px 54px 54px;

        font-weight: 400;
        font-size: 13px;
        color: #6b3906;
      }
    }
  }
}
.share-bug {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16px;
  margin-bottom: 32px;
  image {
    width: 160px;
    height: 54px;
  }
  image:nth-child(1) {
    margin-right: 11px;
  }
}
.box2 {
  margin-top: 22px;
  display: flex;
  justify-content: space-between;
  padding: 8px;
  background: rgba(255, 255, 255, 0.54);
  border-radius: 20px 20px 20px 20px;
  border: 1px solid #ffffff;
  position: relative;
  .box2-back {
    padding: 2px 0;
    background-size: 100% 100%;
    background-image: url(https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/420b174462155428525670_Group%20217%402x.png);
    .box2-row {
      display: flex;
      align-items: center;
      padding-left: 12px;
      padding-top: 12px;
      padding-bottom: 12px;
    }
  }
  .tips {
    position: absolute;
    width: 122.82px;
    height: 41px;
    left: -4px;
    top: -25px;
    z-index: 1;
  }
  .item {
    display: flex;
    align-items: center;
    .img {
      width: 24px;
      height: 24px;
      margin-right: 8px;
    }
    .step {
      flex-shrink: 0;
      view:nth-child(1) {
        color: #713d09;
        font-weight: normal;
        font-size: 14px;
        line-height: 16px;
        margin-bottom: 2px;
      }
      view:nth-child(2) {
        font-weight: 500;
        font-size: 12px;
        color: #222333;
        line-height: 16px;
      }
    }
  }
}
.box {
  display: flex;
  justify-content: space-between;
  padding: 8px;
  background: rgba(255, 255, 255, 0.54);
  border-radius: 20px 20px 20px 20px;
  border: 1px solid #ffffff;
  position: relative;
  .tips {
    position: absolute;
    width: 250.14px;
    height: 50.93px;
    left: -4px;
    top: -30px;
    z-index: 1;
  }
  .item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: calc(50% - 3.5px);
    background-image: url(https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/7e8f174462069692976186_Mask%20group%402x.png);
    background-size: 100% 100%;
    padding-bottom: 16px;
    .num {
      padding-top: 15px;
      padding-bottom: 2px;
      display: flex;
      align-items: baseline;
      view:nth-child(1) {
        font-weight: 800;
        font-size: 28px;
        color: #222333;
      }
      view:nth-child(2) {
        font-weight: 400;
        font-size: 14px;
        color: #222333;
      }
    }
    .text {
      font-weight: 400;
      font-size: 14px;
      color: #713d09;
      line-height: 20px;
    }
  }
}
.share-page {
  height: 100vh;
  overflow-y: auto;
  .page-content {
    background: linear-gradient(180deg, rgba(192, 217, 68, 0) 0%, #c0d944 100%);
    border-radius: 0px 0px 0px 0px;
  }

  .navbar {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1;
  }
  .body {
    margin-top: -162px;
    padding: 0 12px 24px 12px;
    position: relative;
    z-index: 1;
  }
  .head {
    width: 100vw;
    height: 152.8vw;
    background-image: url(https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/206d174461984005598073_Group%20163%402x.png);
    background-size: 100% 100%;
    position: relative;
    .title {
      position: absolute;
      top: 87px;
      left: 50%;
      transform: translateX(-50%);
      width: 82.9333333vw;
      height: 23.466vw;
    }
    .img1 {
      position: absolute;
      top: 160px;
      left: 50%;
      transform: translateX(-50%);
      width: 95.73333333333334vw;
      height: 76.2666vw;
    }
    .img2 {
      position: absolute;
      top: 98px;
      right: 0;
      width: 22px;
      height: 44px;
    }
  }
}
</style>
