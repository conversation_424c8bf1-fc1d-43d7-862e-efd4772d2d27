<template>
  <div class="success-page">
    <view class="success">
      <view> 支付成功 </view>
      <view>¥ {{ query.amount }}</view>
    </view>
    <view class="bottom-but">
      <view class="tips">
        <view>你已完成支付</view>
        <view>感谢您对考拉AI的支持</view>
      </view>

      <view class="pay-but" @click="back()">知道了</view>
    </view>
  </div>
</template>

<script>
export default {
  data() {
    return {
      query: {
        amount: 0
      }
    }
  },
  onLoad(query) {
    this.query = query
  },
  methods: {
    back() {
      this.$xh.back()
    }
  }
}
</script>

<style lang="scss" scoped>
.success-page {
  height: 100vh;
  overflow-y: auto;
  background-color: #fff;
}
.success {
  view:nth-child(1) {
    text-align: center;
    font-weight: 500;
    font-size: 18px;
    color: #222333;
    line-height: 28px;
    margin-top: 82px;
    margin-bottom: 12px;
  }
  view:nth-child(2) {
    text-align: center;
    font-weight: 500;
    font-size: 32px;
    color: #222333;
    line-height: 38px;
  }
}
.bottom-but {
  position: fixed;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  .tips {
    font-weight: 400;
    font-size: 14px;
    color: rgba(34, 35, 51, 0.85);
    line-height: 20px;
    margin-bottom: 18px;
    view {
      text-align: center;
    }
  }
}
.pay-but {
  z-index: 1;
  width: 231px;
  text-align: center;
  height: 44px;
  line-height: 44px;
  background: #222333;
  border-radius: 33px 33px 33px 33px;
  font-weight: bold;
  font-size: 14px;
  color: #ffffff;
}
</style>
