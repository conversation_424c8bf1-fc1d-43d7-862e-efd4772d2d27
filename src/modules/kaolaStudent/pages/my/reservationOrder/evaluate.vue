<template>
  <div>
    <navbar class="navbar" title="课程评价 "></navbar>

    <view style="height: 13px"> </view>
    <view class="evaluate">
      <view class="head">
        <image
          class="icon"
          src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/9865174557288632586109_Group%20248%402x.png"
        ></image>
        <view>对{{ systemMessage.teacher_name }}老师的答疑满意吗？</view>
      </view>
      <view class="evaluate-box">
        <view
          class="item"
          :class="{
            select: evaluateIndex == item.id
          }"
          v-for="item of evaluate"
          @click="evaluateIndex = item.id"
        >
          <image
            v-if="evaluateIndex != item.id"
            class="icon"
            :src="item.icon"
          ></image>
          <image v-else class="active_icon" :src="item.active_icon"></image>
          <view class="name">{{ item.name }}</view>
        </view>
      </view>
      <view class="style-box">
        <view
          class="item"
          :class="{
            select: item.select
          }"
          v-for="item of style"
          @click="styleChange(item)"
        >
          <view>{{ item.name }}</view>
          <image
            src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/4e5b174557291472018807_Frame%402x.png"
          ></image>
        </view>
      </view>
    </view>
    <view class="page-bottom-but-fixed">
      <view class="tips">当前评价为匿名评价，且会延迟同步给对方</view>
      <view class="page-bottom-but pay-but" @click="comment()">提交</view>
    </view>
  </div>
</template>

<script>
import { appoint, config, student } from '../../../api'
import { app_id } from '../../../config'
import navbar from '../../../components/commen/navbar.vue'
export default {
  components: {
    navbar
  },
  data() {
    return {
      qrCodeUrl: '',
      style: [],
      query: {},
      evaluateIndex: '',
      evaluate: [
        {
          icon: 'https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/fd76174557257059443979_dfea32a5424f4ded934be2c4abe7927c%402x(1).png',
          active_icon:
            'https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/1ea0174557259322782972_dfea32a5424f4ded934be2c4abe7927c%402x.png',
          name: '不满意',
          id: '1'
        },
        {
          icon: 'https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/4265174557265309568033_217ec295214a427f80cd9bef002126bb%402x(1).png',
          active_icon:
            'https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/5f09174557267142076381_217ec295214a427f80cd9bef002126bb%402x.png',
          name: '一般',
          id: '2'
        },
        {
          icon: 'https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/75b817455726891101584_eee169b54e19448db171335e87d0e20a%402x(1).png',
          active_icon:
            'https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/10ed174557270433646644_eee169b54e19448db171335e87d0e20a%402x.png',
          name: '满意',
          id: '3'
        },
        {
          icon: 'https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/3678174557272241162081_Mask%20group%402x(1).png',
          active_icon:
            'https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/278f174557274946749692_Mask%20group%402x.png',
          name: '非常满意',
          id: '4'
        }
      ]
    }
  },
  onLoad(query) {
    this.query = query
    this.getData()
  },
  computed: {
    systemMessage() {
      return this.$store.state.kaolaStudent.systemMessage
    }
  },
  methods: {
    comment() {
      let label_ids = this.style.filter(e => e.select).map(e => e.id)
      if (this.evaluateIndex === '') {
        this.$xh.Toast('请选择满意度')
        return
      }
      if (label_ids.length == 0) {
        this.$xh.Toast('请选择标签')
        return
      }
      student
        .comment({
          employee_id: this.systemMessage.teacher_id,
          order_id: this.systemMessage.order_id,
          lesson_id: this.systemMessage.lesson_id,
          star_rating: Number(this.evaluateIndex) + 1,
          label_ids: label_ids.join(','),
          remark: '',
          data_from: 1 //评级来源（1答疑 2伴学）
        })
        .then(res => {
          this.$xh.Toast('评价成功！')
          uni.redirectTo({
            url: `/modules/kaolaStudent/pages/my/reservationOrder/evaluateSuccess`
          })
        })
    },
    styleChange(item) {
      item.select = !item.select
    },
    getData() {
      config.base
        .ykmap({ code: 'teach_style', is_usable: 1, size: 200 })
        .then(res => {
          this.style = res.data.list.map(item => {
            item.select = false
            return item
          })
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.evaluate {
  margin: 0 12px;
  background: #ffffff;
  border-radius: 12px 12px 12px 12px;
  padding-bottom: 12px;
  // padding: 12px;
  .evaluate-box {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding: 4px 0 32px 0;
    .item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      // justify-content: center;
      .icon {
        width: 48px;
        height: 48px;
        margin-top: 8px;
        border-radius: 0px 0px 0px 0px;
      }
      .active_icon {
        width: 52px;
        height: 56px;
        border-radius: 0px 0px 0px 0px;
      }
      .name {
        margin-top: 6px;
        font-weight: 400;
        font-size: 12px;
        color: rgba(34, 35, 51, 0.65);
        line-height: 20px;
      }
    }
    .select {
      .name {
        font-size: 14px;
        color: #222333;
        line-height: 20px;
      }
    }
  }
  .style-box {
    padding-left: 12px;
    display: flex;
    flex-wrap: wrap;
    .item {
      position: relative;
      width: 103px;
      text-align: center;
      height: 40px;
      line-height: 40px;
      background: #f7f9fb;
      border-radius: 8px 8px 8px 8px;
      margin-bottom: 10px;
      margin-right: 10px;
      view {
        font-weight: 400;
        font-size: 14px;
        color: rgba(34, 35, 51, 0.85);
      }
      image {
        display: none;
      }
    }
    .select {
      border: 1px solid #222333;
      image {
        position: absolute;
        display: block;
        right: 0;
        bottom: 0;
        width: 18px;
        height: 18px;
      }
    }
  }
  .head {
    padding: 19px 16px;
    display: flex;
    align-items: center;
    .icon {
      width: 22px;
      height: 22px;
      margin-right: 4px;
    }
    view {
      font-weight: 500;
      font-size: 16px;
      color: #222333;
    }
  }
}
.page-bottom-but-fixed {
  .tips {
    font-weight: 400;
    font-size: 12px;
    color: rgba(34, 35, 51, 0.65);
    margin-bottom: 16px;
  }
}
.price {
  text-align: center;
  padding-top: 12px;
  padding-bottom: 24px;
  .name {
    margin-bottom: 8px;
    font-weight: 400;
    font-size: 16px;
    color: #222333;
  }
  .num {
    font-weight: 500;
    font-size: 28px;
    color: #222333;
  }
}
</style>
