<template>
  <div class="askAbout">
    <div class="askAbout-body">
      <view class="title">
        <view>温馨提示</view>
        <image
          src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/3b34174548334920816804_Vector%203%402x.png"
        ></image>
      </view>
      <view class="text"
        >您即将体验的【清北答疑】为平台付费项目，是否继续连接老师？</view
      >

      <view class="but" @click="success">继续连接</view>
      <view class="but2" @click="hide">我再想想</view>
    </div>
  </div>
</template>

<script>
export default {
  methods: {
    success() {
      uni.setStorageSync('isOriginalTypeTips', '1')
      this.$emit('success')
    },
    hide() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
.askAbout {
  position: fixed;
  z-index: 10;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 0px 0px 0px 0px;
  display: flex;
  align-items: center;
  justify-content: center;
  .askAbout-body {
    width: 311px;
    // height: 220px;
    background: linear-gradient(180deg, #eafe80 0%, #ffffff 100%), #ffffff;
    background-image: url(https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/58a417454833674607463_Mask%20group%402x.png);
    background-size: 100% 96px;
    background-repeat: no-repeat;
    border-radius: 16px 16px 16px 16px;
    padding: 28px 26px 0 26px;
    display: flex;
    flex-direction: column;
    .title {
      margin: 0 auto;
      position: relative;
      view {
        font-weight: bold;
        font-size: 20px;
        color: #222333;
        line-height: 20px;
        position: relative;
        z-index: 1;
      }
      image {
        position: absolute;
        bottom: -5px;
        left: 0;
        width: 77px;
        height: 10px;
      }
    }
    .text {
      font-weight: 400;
      font-size: 14px;
      color: rgba(34, 35, 51, 0.85);
      line-height: 20px;
      margin-top: 16px;
      margin-bottom: 20px;
    }
    .but {
      width: 259px;
      line-height: 44px;
      height: 44px;
      background: #222333;
      border-radius: 33px 33px 33px 33px;
      font-weight: bold;
      font-size: 14px;
      color: #ffffff;
      text-align: center;
    }
    .but2 {
      margin: 0 auto;
      text-align: center;
      padding: 16px;
      font-weight: 400;
      font-size: 14px;
      color: rgba(34, 35, 51, 0.85);
      line-height: 20px;
    }
  }
}
</style>
