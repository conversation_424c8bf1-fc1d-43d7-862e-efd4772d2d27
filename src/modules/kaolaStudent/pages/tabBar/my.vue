<template>
  <view class="my-page">
    <tabBar
      current="my"
      position="relative"
      color="rgba(34,35,51,0.8)"
      activeColor="#222333"
      tabBarActive="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/1a5017441831167812547_Vector%202%402x%20(1).png"
    ></tabBar>
    <scroll-view scroll-y class="page-content">
      <view class="scroll-content">
        <view class="user-info" v-if="!userinfo2.phone">
          <login :enableWechatAuth="false">
            <view class="left">
              <image
                class="avater"
                mode="aspectFill"
                :src="$xh.completepath(avatar.student)"
              ></image>
              <view
                class="row"
                style="align-items: center; justify-content: center"
                @click="push('pages/login/index')"
              >
                <view class="phone">未登录</view>
              </view>
            </view>
          </login>
        </view>
        <view class="user-info" v-if="userinfo2.phone">
          <view class="left">
            <view style="position: relative">
              <image
                class="avater"
                :src="$xh.completepath(userinfo2.avatar)"
              ></image>
              <!-- 移除头像上的VIP标识 -->
            </view>
            <view class="row">
              <view class="phone">
                {{ userinfo2.name || '未登录' }}
                <!-- 在名字右侧添加VIP标识图标 -->
                <view class="vip-icon-name-container">
                  <image
                    class="vip-icon"
                    :class="{ 'vip-icon-grey': userinfo2.vip_level == 0 }"
                    src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/VIP_1757647125004.png"
                  ></image>
                </view>
              </view>
              <view
                class="stage"
                style="align-items: center; justify-content: center"
              >
                <text>{{ userinfo2.grade_id_name }}</text>
              </view>
            </view>
          </view>
          <view class="right-but" @click="push('pages/my/information')">
            <text class="text">编辑资料</text>
            <image
              class="img1"
              src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/3de6174418175342815481_Union%402x.png"
            ></image>
            <image
              class="img2"
              src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/8d51174418176805263630_Frame%402x.png"
            ></image>
          </view>
        </view>

        <!-- 会员卡片区域 - 修改为图片中的样式 -->
        <view
          class="vip-card"
          v-if="userinfo2.phone && userinfo2.vip_level == 0"
        >
          <view class="vip-content">
            <view class="vip-text">
              <view class="vip-title">升级会员</view>
              <view class="vip-desc">会员课程免费学,一对一课后辅导</view>
            </view>
            <view class="vip-button" @click="push('pages/my/vip/index')"
              >立即开通</view
            >
          </view>
        </view>

        <view
          class="vip-card"
          v-if="userinfo2.phone && userinfo2.vip_level > 0"
        >
          <view class="vip-content">
            <view class="vip-text">
              <view class="vip-title">{{ userinfo2.vip_level_name }}</view>
              <view class="vip-desc">会员课程免费学,一对一课后辅导</view>
            </view>
            <view class="vip-button" @click="push('pages/my/vip/index')"
              >立即查看</view
            >
          </view>
        </view>

        <!-- 数据统计区域保持不变 -->
        <view class="set-box">
          <login @success="push('pages/my/account/index')">
            <view class="box box-back1">
              <view class="h">
                <view class="n"> 账户 </view>
                <view class="l">余额</view>
              </view>
              <view class="num"> {{ userinfo2.balance_amount || 0 }} </view>
              <view class="but">
                <text>去充值</text>
                <image
                  src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/e61b174418217509992336_Frame%402x(2).png"
                ></image>
              </view>
            </view>
          </login>
          <login @success="push('pages/my/footprint/bookshelf')">
            <view class="box box-back2">
              <view class="h">
                <view class="n"> 足迹 </view>
                <view class="l">提问</view>
              </view>
              <view class="num"> {{ userinfo2.question_num || 0 }} </view>
              <view class="but">
                <text>去看看</text>
                <image
                  src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/e61b174418217509992336_Frame%402x(2).png"
                ></image>
              </view>
            </view>
          </login>
          <login @success="push('pages/my/share/index')">
            <view class="box box-back3">
              <view class="h">
                <view class="n"> 分享 </view>
                <view class="l">邀请好友</view>
              </view>
              <view class="num"> {{ userinfo2.invite_num || 0 }}</view>
              <view class="but">
                <text>邀好友</text>
                <image
                  src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/e61b174418217509992336_Frame%402x(2).png"
                ></image>
              </view>
            </view>
          </login>
          <login @success="push('pages/my/opinion')">
            <view class="box box-back4">
              <view class="h">
                <view class="n"> 吐槽 </view>
                <view class="l">反馈问题</view>
              </view>
              <image
                class="icon"
                src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/923f174562717004611292_Frame%402x%20(1).png"
              ></image>
              <view class="but">
                <text>吐个槽</text>
                <image
                  src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/e61b174418217509992336_Frame%402x(2).png"
                ></image>
              </view>
            </view>
          </login>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import tabBar from '../../components/commen/tabBar.vue'
import { goToLogin } from '../../utils/index'
import { student } from '../../api/index.js'
import login from '../../components/commen/login.vue'

export default {
  components: {
    tabBar,
    login
  },
  data() {
    return {
      avatar: {
        student: ''
      }
    }
  },
  computed: {
    userinfo2() {
      return this.$store.state.kaolaStudent.userinfo2
    }
  },
  onShow() {
    student.detailv2()
  },
  methods: {
    push(url) {
      // 当跳转到登录页面时，添加redirect参数
      if (url === 'pages/login/index') {
        // 获取当前页面的完整路径
        const pages = getCurrentPages()
        const currentPage = pages[pages.length - 1]
        let fullPath = `/${currentPage.route}`

        // 构造带redirect参数的URL
        const loginUrl = `${url}?redirect=${encodeURIComponent(fullPath)}`

        // 直接使用uni.navigateTo而不是$xh.push，避免权限检查
        uni.navigateTo({
          url: `/modules/kaolaStudent/${loginUrl}`,
          fail: err => {
            console.error('Failed to navigate to login page:', err)
          }
        })
      } else {
        // 其他页面跳转保持不变
        uni.navigateTo({
          url: `/modules/kaolaStudent/${url}`
        })
      }
    }
  },
  onShareAppMessage() {
    return this.$xh ? this.$xh.shareAppMessage() : {}
  }
}
</script>

<style lang="scss" scoped>
.set-box {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  .box {
    margin-bottom: 5px;
    width: 46.13vw;
    height: 38.4vw;
    background-size: 46.13vw 38.4vw;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding-top: 14px;
    padding-bottom: 18px;
    padding-left: 12px;
    .num {
      margin: 0 auto;
      font-weight: bold;
      font-size: 24px;
      color: #222333;
      line-height: 28px;
    }
    .icon {
      margin: 0 auto;
      width: 25px;
      height: 25px;
    }
    .tips {
      display: flex;
      justify-content: center;
      align-items: center;
      image {
        width: 20px;
        height: 20px;
        margin-right: 4px;
      }
      text {
        font-weight: 400;
        font-size: 13px;
        color: rgba(34, 35, 51, 0.85);
        line-height: 20px;
      }
    }
    .but {
      margin: 0 auto;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 70px;
      height: 24px;
      background: linear-gradient(90deg, #ffe9f7 0%, #fff2c2 100%);
      border-radius: 23px 23px 23px 23px;
      image {
        width: 12px;
        height: 12px;
      }
      text {
        font-weight: 400;
        font-size: 14px;
        color: #222333;
      }
    }
    .h {
      display: flex;
      align-items: center;
      .n {
        font-weight: 500;
        font-size: 16px;
        color: #222333;
        line-height: 19px;
        background: linear-gradient(to right, #eaff00, #eaff00) no-repeat;
        background-size: 100% 9px;
        background-position: 0 10px;
        margin-right: 4px;
      }
      .l {
        text-align: center;
        min-width: 34px;
        background: #222333;
        border-radius: 8px 8px 8px 8px;
        padding: 2px 5px;
        font-weight: 400;
        font-size: 12px;
        color: #ffffff;
      }
    }
  }
  .box-back1 {
    background-image: url(https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/92b4174418254734376440_Group%20103%402x.png);
  }
  .box-back2 {
    background-image: url(https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/ec34174562733312685167_Group%20102%402x.png);
  }
  .box-back3 {
    background-image: url(https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/1f85174562735501146410_Group%20104%402x.png);
  }
  .box-back4 {
    background-image: url(https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/5540174418263151258922_Group%20105%402x.png);
  }
}
.user-info {
  display: flex;
  justify-content: space-between;
  margin-top: 13px;
  margin-bottom: 20px;
  .left {
    display: flex;
    .avater {
      width: 64px;
      height: 64px;
      background: #999faa;
      border-radius: 50%;
      margin-right: 12px;
      position: relative;
    }

    // VIP图标样式
    .vip-icon-container {
      position: absolute;
      top: -5px;
      right: -5px;
      z-index: 1;
      .vip-icon {
        width: 24px;
        height: 24px;
      }
      .vip-icon-grey {
        filter: grayscale(100%);
        opacity: 0.6;
      }
      .vip-level-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 10px;
        font-weight: bold;
        color: white;
        text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5);
      }
    }

    .row {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 4px 0;
      .phone {
        font-weight: 500;
        font-size: 22px;
        color: #222333;
        line-height: 24px;
        // 添加以下样式确保文字和图标正确对齐
        display: flex;
        align-items: center;
        // 在名字右侧添加VIP图标样式
        .vip-icon-name-container {
          margin-left: 6px;
          .vip-icon {
            width: 20px;
            height: 20px;
            // 确保图标有默认的显示效果
          }
          .vip-icon-grey {
            // 当vip_level为0时应用以下样式使其置灰
            filter: grayscale(100%); // 将图像转换为灰度
            opacity: 0.6; // 降低透明度，增强置灰效果
          }
        }
      }
      .stage {
        display: flex;
        align-items: center;
        text {
          font-weight: 400;
          font-size: 12px;
          color: #222333;
          line-height: 20px;
        }
      }
    }
  }
  .right-but {
    width: 96px;
    height: 36px;
    background: #edeff2;
    border-radius: 8px 8px 8px 8px;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    .text {
      font-weight: 400;
      font-size: 14px;
      color: #222333;
      line-height: 20px;
    }
    .img1 {
      position: absolute;
      left: 0;
      top: 0;
      width: 32px;
      height: 26px;
    }
    .img2 {
      width: 24px;
      height: 24px;
      margin-left: 2px;
    }
  }
}

// 修改会员卡片样式，匹配图片效果
.vip-card {
  margin: 0 0 20px 0;
  .vip-content {
    background: #eaff00;
    border-radius: 16px;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .vip-text {
    flex: 1;
  }
  .vip-title {
    font-weight: bold;
    font-size: 18px;
    color: #222333;
    margin-bottom: 4px;
  }
  .vip-desc {
    font-weight: 400;
    font-size: 14px;
    color: #222333;
    line-height: 18px;
  }
  .vip-button {
    background: #222333;
    color: #ffffff;
    font-size: 14px; /* 原15px，减小1px */
    font-weight: bold;
    padding: 8px 16px; /* 原10px 24px，减小内边距 */
    border-radius: 16px; /* 原24px，相应减小圆角 */
    white-space: nowrap;
  }
}

.my-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow-y: hidden;
  background-color: #fff;
  .page-content {
    flex: 1;
    height: calc(100% - 90px);

    .scroll-content {
      padding: 0 12px;
    }
  }
}
</style>
