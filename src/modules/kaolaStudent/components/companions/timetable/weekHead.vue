<template>
  <swiper class="weekHead" circular @change="swiperChange" :current="current">
    <swiper-item v-for="(item, index) of list" :key="index">
      <view class="week-head">
        <view class="item" v-for="(ee, eIndex) of item.week" :key="eIndex">
          <view class="name">{{ ee.name }}</view>
          <view
            class="day"
            :class="{
              select: isSelected(new Date(), ee.date)
            }"
            >{{ ee.day }}</view
          >
        </view>
      </view>
    </swiper-item>
  </swiper>
</template>

<script>
import dayjs from 'dayjs'
export default {
  props: {
    date: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: [{}, {}, {}],
      page: 0,
      lastCurrent: 0,
      current: 0
    }
  },
  created() {
    this.init(0)
    this.change(0)
  },
  methods: {
    last() {
      this.current--
      if (this.current < 0) {
        this.current = 2
      }
    },
    next() {
      this.current++
      if (this.current > 2) {
        this.current = 0
      }
    },
    isSelected(d1, d2) {
      return dayjs(d1).format('YYYY-MM-DD') == dayjs(d2).format('YYYY-MM-DD')
    },
    getWeek(d) {
      function getWeekDates() {
        const today = d
        const dayOfWeek = today.day() // 0（周日）到 6（周六）
        const diff = dayOfWeek === 0 ? -6 : 1 - dayOfWeek // 计算到周一的天数差
        const monday = today.add(diff, 'day')

        return Array.from({ length: 7 }, (_, i) =>
          monday.add(i, 'day').format('YYYY-MM-DD')
        )
      }
      let options = [
        {
          name: '周一',
          day: ''
        },
        {
          name: '周二',
          day: ''
        },
        {
          name: '周三',
          day: ''
        },
        {
          name: '周四',
          day: ''
        },
        {
          name: '周五',
          day: ''
        },
        {
          name: '周六',
          day: ''
        },
        {
          name: '周日',
          day: ''
        }
      ]
      return getWeekDates().map((val, index) => {
        options[index].day = val
        return {
          name: options[index].name,
          day: dayjs(val).format('MM/DD'),
          date: val
        }
      })
    },
    swiperChange({ detail: { current } }) {
      let num = this.lastCurrent - current
      let direction = true
      if (num == -1 || num == 2) {
        this.page++
        direction = true
      } else {
        this.page--
        direction = false
      }
      if (current == 0 && direction) {
        this.init(0)
      }
      if (current == 2 && !direction) {
        this.init(-2)
      }
      this.change(current)
      this.lastCurrent = current
    },
    init(val) {
      this.list.forEach((e, index) => {
        e.week = this.getWeek(
          dayjs(this.date || '').add((this.page + index + val) * 7, 'day')
        )
      })
      // this.change(0)
    },
    change(val) {
      this.$emit('change', this.list[val].week)
    }
  }
}
</script>

<style lang="scss" scoped>
.weekHead {
  height: 72px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 0px 0px 16px 16px;
}

.week-head {
  height: 72px;

  display: flex;
  align-items: center;
  .item {
    width: calc((100%) / 7);
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .name {
      padding-top: 12px;
      padding-bottom: 4px;
      font-weight: 500;
      font-size: 12px;
      color: #222333;
      line-height: 16px;
    }
    .day {
      text-align: center;
      width: 32px;
      height: 32px;
      line-height: 32px;
      // background: #EBCBFF;
      border-radius: 8px 8px 8px 8px;
      font-weight: 500;
      font-size: 12px;
      color: #222333;
    }
    .select {
      background: #dbf46b;
    }
  }
}
</style>
