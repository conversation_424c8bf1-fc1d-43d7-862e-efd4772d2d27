<template>
  <view class="box">
    <view class="no-data">
      <image
        src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/90fb17438216266856979_168248267299334c7168248267299574184_nodata.png"
      />
      <text>{{ tip }}</text>
    </view>
  </view>
</template>
<script>
export default {
  props: {
    tip: {
      type: String,
      default: '暂无数据~'
    }
  }
}
</script>

<style scoped lang="less">
.box {
  width: 100%;
  min-height: 100%;
  // background-color: #ffffff;
}
.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  height: 100%;
  padding-top: 352rpx;
  image {
    width: 286rpx;
    height: 234rpx;
    margin-bottom: 24rpx;
    flex-shrink: 0;
  }
  text {
    text-align: center;
    font-size: 24rpx;
    color: rgba(3, 32, 61, 0.45);
    line-height: 34rpx;
  }
}
</style>
