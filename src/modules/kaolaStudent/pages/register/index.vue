<template>
  <view class="page">
    <view class="head">
      <image
        class="registerBack"
        src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/7c2d174382047468671821_registerBack.png"
      ></image>
      <navbar class="navbar" title="完善资料"></navbar>
    </view>
    <view class="step-box" v-if="step == 1">
      <view class="tree" v-for="item of tree">
        <view class="name">{{ item.name }}</view>
        <view class="subs">
          <view
            class="but"
            :class="{
              select: ee.id == form.treeIndex
            }"
            v-for="ee of item.subs"
            @click="form.treeIndex = ee.id"
          >
            {{ ee.name }}
          </view>
        </view>
      </view>
      <view class="next-but" @click="stepTap()">下一步</view>
    </view>
    <view class="step-box" v-if="step == 2">
      <view class="tree2">
        <view class="name"> 期望哪种教学风格的老师? </view>
        <view class="subs">
          <view
            class="but"
            :class="{
              select: item.select
            }"
            v-for="item of style"
            @click="styleChange(item)"
          >
            {{ item.name }}
          </view>
        </view>
      </view>
      <view class="tree2" style="padding-right: 16px">
        <view class="name">自我评价</view>
        <view class="register-textarea">
          <textarea
            class="textarea"
            v-model="form.introduce"
            placeholder="填写自己在学习过程中遇到的困惑，让老师更好的了解你并帮助你。"
            placeholder-style="font-size: 14px;color: rgba(34,35,51,0.45);"
            maxlength="140"
          ></textarea>
          <view class="count"
            ><text class="length">{{ form.introduce.length }}</text
            ><text class="total">/140</text></view
          >
        </view>
      </view>
      <view class="submit" @click="stepTap()">保存</view>
    </view>
  </view>
</template>

<script>
import navbar from '../../components/commen/navbar.vue'
import { config, student } from '../../api/index.js'
import { generateKoalaName } from '../../utils/index.js'
import { avatar } from '../../config.js'
export default {
  components: {
    navbar
  },
  data() {
    return {
      step: 1,

      tree: [],
      style: [
        {
          name: '幽默风趣',
          id: '1'
        },
        {
          name: '幽默风趣2',
          id: '2'
        }
      ],
      form: {
        introduce: '',
        treeIndex: ''
      }
    }
  },
  onLoad(query) {
    if (query.step) {
      this.step = query.step
    }
    this.getData()
    this.init()
  },
  computed: {
    userinfo2() {
      return this.$store.state.kaolaStudent.userinfo2
    }
  },
  methods: {
    init() {
      this.form.treeIndex = this.userinfo2.grade_id
      this.form.introduce = this.userinfo2.introduce
    },
    styleChange(item) {
      item.select = !item.select
    },
    getData() {
      config.stageGrade({}).then(res => {
        this.tree = res.data
      })
      config.base
        .ykmap({ code: 'teach_style', is_usable: 1, size: 200 })
        .then(res => {
          this.style = res.data.list.map(item => {
            let reg = new RegExp(item.id)
            item.select = reg.test(this.userinfo2?.teach_style_ids || '')
            return item
          })
        })
    },
    stepTap() {
      if (this.step == 1) {
        if (!this.form.treeIndex) {
          this.$xh.Toast('请选择年级')
          return
        }
        this.step = 2
        return
      }
      if (this.step == 2) {
        this.step = 2
        let stage_id = ''
        let grade_id = ''
        for (let item of this.tree) {
          for (let ee of item.subs) {
            if (ee.id == this.form.treeIndex) {
              stage_id = ee.pid
              grade_id = ee.id
            }
          }
        }
        let teach_style = this.style.filter(e => e.select)
        if (teach_style.length == 0) {
          this.$xh.Toast('请选择期望教学风格')
          return
        }
        if (!this.form.introduce) {
          this.$xh.Toast('请填写自我评价')
          return
        }
        student
          .studentPost({
            name: this.userinfo2.name || generateKoalaName(),
            stage_id,
            grade_id,
            introduce: this.form.introduce,
            teach_style_ids: teach_style.map(e => e.id).join(','),
            avatar: this.userinfo2.avatar || avatar.student
          })
          .then(res => {
            this.$xh.back()
            student.detailv2()
          })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.register-textarea {
  width: 100%;
  padding: 14px 16px;
  background: #ffffff;
  border-radius: 10px 10px 10px 10px;
  height: 140px;
  position: relative;
  .textarea {
    width: 100%;
    height: 100%;
    font-weight: 400;
    font-size: 14px;
    color: rgba(34, 35, 51, 1);
  }
  .count {
    position: absolute;
    right: 16px;
    bottom: 14px;
    .total {
      font-weight: 400;
      font-size: 14px;
      color: rgba(34, 35, 51, 1);
    }
    .length {
      font-weight: 400;
      font-size: 14px;
      color: rgba(34, 35, 51, 0.6);
    }
  }
}
.next-but {
  width: 203px;
  text-align: center;
  height: 44px;
  line-height: 44px;
  background: #ffffff;
  border-radius: 22px;
  border: 1px solid #222333;
  font-weight: 500;
  font-size: 14px;
  color: #222333;
  position: fixed;
  bottom: 50px;
  left: calc(50% - 101px);
}
.submit {
  width: 203px;
  text-align: center;
  height: 44px;
  line-height: 44px;
  background: #222333;
  border-radius: 22px;
  border: 1px solid #222333;
  font-weight: 500;
  font-size: 14px;
  color: #ffffff;
  position: fixed;
  bottom: 50px;
  left: calc(50% - 101px);
}
.step-box {
  position: relative;
  margin-top: -98px;
  z-index: 1;
}
.tree2 {
  padding: 0 7px 0 16px;
  .name {
    font-weight: 400;
    font-size: 16px;
    color: #222333;
    padding: 12px 4px;
  }
  .subs {
    display: flex;
    flex-wrap: wrap;
    .but {
      // width: 107px;
      padding: 0 26px;
      text-align: center;
      height: 44px;
      line-height: 44px;
      background: #ffffff;
      border-radius: 22px;

      font-weight: bold;
      font-size: 14px;
      color: #222333;
      margin-bottom: 12px;
      margin-right: 9px;
    }
    .select {
      background: #222333;
      color: #fff;
    }
  }
}
.tree {
  padding: 0 4px 0 16px;
  .name {
    font-weight: 400;
    font-size: 16px;
    color: #222333;
    padding: 12px 4px;
  }
  .subs {
    display: flex;
    flex-wrap: wrap;
    .but {
      max-width: calc((100% - 36px) / 3);
      width: 106px;
      text-align: center;
      height: 44px;
      line-height: 44px;
      background: #ffffff;
      border-radius: 22px;
      font-weight: 400;
      font-size: 14px;
      color: #222333;
      margin-bottom: 12px;
      margin-right: 12px;
    }
    .select {
      background: #222333;
      color: #fff;
    }
  }
}
.head {
  .registerBack {
    height: 93.3vw;
    width: 100vw;
  }
  height: 93.3vw;
  width: 100vw;
  position: relative;
  .navbar {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
  }
}
.page {
  background: linear-gradient(180deg, #dfe9ff 0%, #f7f9fb 100%);
  overflow-y: auto;
}
</style>
