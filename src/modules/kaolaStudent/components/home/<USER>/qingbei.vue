<template>
  <view class="qingbei">
    <view class="title" @click="tap()">
      <image src="/static/imgs/kaolaStudent/business2.svg"></image>
      <text>清北答疑</text>
    </view>
    <view class="tips">想逆袭？要私人定制？您可以呼叫清北学霸帮帮团</view>
  </view>
</template>

<script>
export default {
  created() {},
  computed: {
    original_type() {
      return this.$store.state.kaolaStudent.original_type
    }
  },
  methods: {
    tap() {
      if (this.original_type == '1') {
        this.$xh.Toast('切换清北答疑成功！')
        this.$store.commit('kaolaStudent/setOriginalType', '2')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.qingbei {
  width: calc(100vw - 24px);
  padding: 28px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .title {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 97px;
    height: 36px;
    background: #ffffff;
    border-radius: 12px 12px 12px 12px;
    image {
      width: 16px;
      height: 16px;
      margin-right: 4px;
    }
    text {
      font-weight: 400;
      font-size: 13px;
      color: #222333;
    }
  }
  .tips {
    margin-top: 12px;
    font-weight: 400;
    font-size: 12px;
    color: rgba(34, 35, 51, 0.45);
  }
}
</style>
