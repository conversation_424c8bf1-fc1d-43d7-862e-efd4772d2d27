import getrequest from '@api/request.js'
import http from './request'
import sse from './sse.js'
import store from '../../../store/index'
import { app_id } from '../config'

// 基础请求函数
// secretConfig.crmSeret()
// const http = getrequest('api')
// 获取code
export const getCode = function (data = {}) {
  console.log(data, 'data')
  return http({
    url: '/c/student/openid',
    method: 'GET',
    data
  })
}

export const Login = function (data = {}) {
  return http({
    url: '/c/student/login',
    method: 'POST',
    data
  })
}
export const shareRecord = function (data = {}) {
  return http({
    url: '/c/goods/v2/share/record',
    method: 'POST',
    data
  })
}
export const getPhone = function (data = {}) {
  return http({
    url: '/c/student/mobile',
    method: 'GET',
    data
  })
}
// 获取验证码
export const getSendcode = function (data = {}) {
  return http({
    url: '/b/base/sms/sendcode',
    method: 'POST',
    data
  })
}
export const ykmap = {
  setCodeData: (data = {}) => {
    return http({
      url: '/b/base/ykmap',
      method: 'POST',
      data
    })
  }
}
export const addressFun = {
  // 获取城市
  getCity: (data = {}) => {
    return http({
      url: '/c/base/area',
      method: 'GET',
      data
    })
  },
  // 热门城市
  getHotCity: () => {
    return http({
      url: '/c/base/popularcity',
      method: 'GET'
    })
  }
}
export const config = {
  back: {
    getList: (data = {}) => {
      return http({
        url: '/c/config/ai/avatar/list',
        method: 'GET',
        data
      })
    },
    my: (data = {}) => {
      return http({
        url: '/c/config/ai/avatar/my',
        method: 'GET',
        data
      })
    },
    post: (data = {}) => {
      return http({
        url: '/c/config/ai/avatar/my',
        method: 'POST',
        data
      })
    }
  },
  stageGrade: (data = {}) => {
    return http({
      url: '/c/base/stageGrade',
      method: 'GET',
      data
    })
  },
  base: {
    ykmap: (data = {}) => {
      return http({
        url: '/c/base/ykmap',
        method: 'GET',
        data: {
          ...data,
          is_usable: 1,
          size: 200,
          noloading: true
        }
      })
    }
  },
  message: (data = {}) => {
    return http({
      url: '/c/student/message',
      method: 'GET',
      data
    })
  },
  tencent: {
    get: (data = {}) => {
      return http({
        url: '/cb/live/tencent',
        method: 'GET',
        data
      })
    },
    POST: (data = {}) => {
      return http({
        url: '/cb/live/tencent',
        method: 'POST',
        data
      })
    }
  },
  feedback: (data = {}) => {
    return http({
      url: '/c/user/feedback',
      method: 'POST',
      data,
      header: {
        // 'content-type': 'application/json'
      }
    })
  }
}
export const student = {
  studentPost: (data = {}) => {
    return http({
      url: '/c/student/v2',
      method: 'PUT',
      data
    })
  },
  detailv2: (data = {}) => {
    let phone = uni.getStorageSync('__xingyun_userPhone__')
    let student_id = uni.getStorageSync('__xingyun_userinfo__').student_id
    if (!phone || !student_id) {
      return
    }
    return http({
      url: '/c/student/detailv2',
      method: 'GET',
      data: {
        id: student_id,
        phone: phone,
        noloading: true
      }
    }).then(res => {
      try {
        store.commit('kaolaStudent/setUserinfo2', res.data)
      } catch (e) {
        console.log(e, 'detailv2')
      }
      return res
    })
  },
  comment: (data = {}) => {
    return http({
      url: '/c/student/comment/teacher',
      method: 'POST',
      data,
      header: {
        'content-type': 'application/json'
      }
    })
  },
  // 新增buyvip方法
  buyvip: (data = {}) => {
    return http({
      url: '/c/order/vip',
      method: 'POST',
      data
    }).then(res => {
      // 返回完整的响应对象，调用者可以从中提取FlowID、OrderID和PayAmount
      return res
    }).catch(err => {
      console.error('buyvip请求失败:', err)
      // 确保在出错时也返回一个有意义的错误对象
      throw err
    })
  },
  // 新增获取会员信息的接口
  getVipList: (data = {}) => {
    return http({
      url: '/c/account/vip/list',
      method: 'GET',
      data
    })
  },
  question: {
    created: (data = {}) => {
      return http({
        url: '/c/student/question',
        method: 'POST',
        data
      })
    },
    lastai: (data = {}) => {
      return http({
        url: '/c/student/question/lastai',
        method: 'GET',
        data
      })
    },
    lastunpaidorder: (data = {}) => {
      return http({
        url: '/c/student/question/lastunpaidorder',
        method: 'GET',
        data
      })
    },
    teachermessage: (data = {}) => {
      return http({
        url: '/c/student/question/teachermessage',
        method: 'POST',
        data
      })
    },
    aimessage: (data = {}) => {
      return http({
        url: '/c/student/question/aimessage',
        method: 'POST',
        data
      })
    },
    relationdata: (data = {}) => {
      return http({
        url: '/c/student/question/relationdata',
        method: 'GET',
        data
      })
    },
    report: (data = {}) => {
      return http({
        url: '/c/student/question/report',
        method: 'PUT',
        data
      })
    },
    end: (data = {}) => {
      return http({
        url: '/c/student/question/end',
        method: 'PUT',
        data
      })
    },
    getList: (data = {}) => {
      return http({
        url: '/c/student/question',
        method: 'GET',
        data
      })
    },
    subject: (data = {}) => {
      return http({
        url: '/c/student/question/subject',
        method: 'GET',
        data
      })
    }
  }
}

export const account = {
  withdraw: {
    withdraw: (data = {}) => {
      return http({
        url: '/c/account/withdraw',
        method: 'POST',
        data,
        header: {
          'content-type': 'application/json'
        }
      })
    },
    detail: data => {
      return http({
        url: '/c/account/withdraw/detail',
        method: 'GET',
        data
      })
    },
    applylist: data => {
      return http({
        url: '/c/account/withdraw/applylist',
        method: 'GET',
        data
      })
    }
  },
  goods: data => {
    return http({
      url: '/c/goods/v2',
      method: 'GET',
      data
    })
  },
  detail: data => {
    return http({
      url: '/c/student/account/detail',
      method: 'GET',
      data
    })
  },
  topup: (data = {}) => {
    return http({
      url: '/c/account/topup',
      method: 'POST',
      data,
      header: {
        'content-type': 'application/json'
      }
    })
  },
  payorder: data => {
    return http({
      url: '/c/pay/account/balance/payorder',
      method: 'post',
      data,
      header: {
        'content-type': 'application/json'
      }
    })
  },
  payParams: (data = {}) => {
    console.log(data, 'payParams')
    return http({
      url: '/c/config/finance/account',
      method: 'GET',
      data: {
        account_use: 1, // 1收款 2付款
        is_match: 1,
        is_usable: 1,
        page: 1,
        size: 100,
        account_type: 1, // 1:线上支付 2:线下支付
        order_id: data.order_id,
        goods_ids: data.goods_ids,
        brand_id: process.env.VUE_APP_BRANDID,
        merchant_id: process.env.VUE_APP_MERCHANTID,
        collection_scene: 2,
        collection_terminal: 8
      }
    }).then(res => {
      if (res?.data?.list?.length > 0) {
        let flag_row = res?.data?.list?.find(item => {
          return item?.pay_method == '6' && app_id == item?.wechat_pay_app_id
        })
        if (flag_row) {
          return http({
            url: '/c/pay/wechatpay/jsapi',
            method: 'post',
            data: {
              flow_id: data.flow_id,
              wechat_app_id: app_id,
              open_id: uni.getStorageSync('__xingyun_weixinInfo__').openid,
              finance_body_id: flag_row?.id
            },
            header: {
              'content-type': 'application/json'
            }
          }).then(res => {
            return {
              data: {
                ...res.data,
                finance_body_id: flag_row?.id
              }
            }
          })
        }
      }
    })
  }
}
export const appoint = {
  student: {
    my: (data = {}) => {
      return http({
        url: '/c/appoint/student/my',
        method: 'GET',
        data
      })
    }
  },
  teacher: {
    worktimeList: (data = {}) => {
      return http({
        url: '/c/appoint/teacher/worktime/list',
        method: 'GET',
        data
      })
    },
    getList: (data = {}) => {
      return http({
        url: '/c/appoint/teacher/list',
        method: 'GET',
        data
      })
    },
    detail: (data = {}) => {
      return http({
        url: '/c/appoint/teacher/detail',
        method: 'GET',
        data
      })
    },
    applyJoinDetail: (data = {}) => {
      return http({
        url: '/c/teacher/detail',
        method: 'GET',
        data
      })
    },
    post: (data = {}) => {
      return http({
        url: '/c/appoint/teacher',
        method: 'POST',
        data
      })
    },
    remark: (data = {}) => {
      return http({
        url: '/c/appoint/teacher/remark',
        method: 'PUT',
        data
      })
    },
    getRemark: (data = {}) => {
      return http({
        url: '/c/appoint/teacher/remark',
        method: 'GET',
        data
      })
    },
    cancel: (data = {}) => {
      return http({
        url: '/c/appoint/teacher/cancel',
        method: 'PUT',
        data
      })
    }
  },
  order: {
    getList: (data = {}) => {
      return http({
        url: '/c/appoint/teacher/order',
        method: 'GET',
        data
      })
    },
    detail: (data = {}) => {
      return http({
        url: '/c/appoint/teacher/order/detail',
        method: 'GET',
        data
      })
    },
    DayiDetail: (data = {}) => {
      return http({
        url: '/c/order/question/detail',
        method: 'GET',
        data
      })
    }
  }
}
export const aimessageStream = function (data = {}, chunkReceived) {
  return sse({
    url: '/c/student/question/aimessage/stream',
    method: 'POST',
    data,
    chunkReceived
  })
}
export const activity = {
  record: data => {
    return http({
      url: '/c/marketing/activity/student/record',
      method: 'GET',
      data
    })
  },
  statistics: data => {
    return http({
      url: '/c/marketing/activity/student/statistics',
      method: 'GET',
      data
    })
  },
  wxQrCode: data => {
    return http({
      url: '/c/marketing/activity/material/listv2',
      method: 'GET',
      data
    })
  },
  add: (data = {}) => {
    return http({
      url: '/c/marketing/activity/student/add',
      method: 'POST',
      data,
      header: {
        'content-type': 'application/json'
      }
    })
  }
}


export const teaching = {
  getLiveList: (data = {}) => {
    return http({
      url: '/c/square/live/resource/list',
      method: 'GET',
      data
    })
  },
  getCallbackList: (data = {}) => {
    
    return http({
      url: '/c/square/playback/resource/list',
      method: 'GET',
      data
    })
  },

  // 专题接口
  getTeachingList: (data = {}) => {
    return http({
      url: '/c/teaching/list',
      method: 'GET',
      data
    })
  },

  getTeachingTree: (data = {}) => {

    return http({
      url: '/c/teaching/tree',
      method: 'GET',
      data
    })
  },
  getTeachingSystemTree: (data = {}) => {

    return http({
      url: '/c/teaching/system/tree',
      method: 'GET',
      data
    })
  },

  getLiveDetail: (data = {}) => {
    return new Promise((resolve, reject) => {
      const mockRes = {
        "msg": [
          "操作成功"
        ],
        "code": 100000,
        "data": {
          "id": "583465409181656214",
          "name": "fasf",
          "material_cover_path": "385164383640140890/2025/09/08/175730207815754d7-1757302078157-33972.png",
          "description": "",
          "vip_level": "0",
          "pv": "0",
          "resource_video_id": "583233404695219949",
          "path": "385164383640140890/2025/09/06/17571637985615ae6-1757163798561-79308.mp4"
        }
      }

      setTimeout(() => {
        resolve(mockRes);
      }, 100)
    })
  },

  appoint: (data = {}) => {
    return http({
      url: '/c/square/live/appoint',
      method: 'GET',
      data
    })
  },

  teachingTree: (data = {}) => {
    return http({
      url: '/c/teaching/system/tree',
      method: 'GET',
      data
    })
  }
}


export const square = {
  getAppointList: (data = {}) => {
    return http({
      url: '/c/square/live/appoint/list',
      method: 'GET',
      data
    })
  },
  getLiveDetail: (data = {}) => {
    return http({
      url: '/c/square/live/resource/detail',
      method: 'GET',
      data
    })
  },
  getCallbackDetail: (data = {}) => {
    return http({
      url: '/c/square/playback/resource/detail',
      method: 'GET',
      data
    })
  },
  getLiveUrl: (data = {}) => { 
    return http({
      url: '/c/square/student/live/detail',
      method: 'GET',
      data
    })
  },
  getReplayUrl: (data = {}) => { 
    return http({
      url: '/c/square/live/replay_url',
      method: 'GET',
      data
    })
  },
  getSwiperList () { 
    return http({
      url: '/c/square/carousel/list',
      method: 'GET'
    })
  },
  addPv(data = {}) {
    return http({
      url: '/c/square/resource/pv',
      method: 'GET',
      data
    })
  }
}
