<template>
  <view>
    <canvas
      canvas-id="myCanvas"
      :style="{
        width: width + 'px',
        height: width + 'px'
      }"
    ></canvas>
  </view>
</template>

<script>
export default {
  props: {
    url: {
      type: String,
      default: ''
    }
  },
  components: {},
  data() {
    return {
      width: 375,
      rotation: 0,
      tempFilePath: '',
      imgWidth: 375, // 新增图片原始尺寸
      imgHeight: 555,
      isVerticalRotation: false
    }
  },
  created() {
    this.getImageSize(this.url)
  },
  methods: {
    getImageSize(path) {
      wx.getImageInfo({
        src: path,
        success: res => {
          this.imgWidth = res.width
          this.imgHeight = res.height
        }
      })
    },
    // 绘制图片到画布
    drawImage() {
      const ctx = uni.createCanvasContext('myCanvas', this)
      const rotation = this.rotation
      let canvasWidth = (this.imgWidth / this.imgHeight) * this.width
      let canvasHeight = this.width
      if (this.imgHeight < this.imgWidth) {
        canvasWidth = this.width
        canvasHeight = (this.imgHeight / this.imgWidth) * this.width
      }
      // 清空画布
      ctx.clearRect(0, 0, this.width, this.width)
      // 设置旋转中心点
      ctx.translate(this.width / 2, this.width / 2)
      ctx.rotate((rotation * Math.PI) / 180)
      // 绘制图片（根据旋转角度调整位置）
      ctx.drawImage(
        this.url,
        -canvasWidth / 2,
        -canvasHeight / 2,
        canvasWidth,
        canvasHeight
      )
      ctx.draw(false, () => {
        this.canvasToTempFilePath()
      })
    },
    canvasToTempFilePath() {
      let canvasWidth = (this.imgWidth / this.imgHeight) * this.width
      let canvasHeight = this.width
      if (this.imgHeight < this.imgWidth) {
        canvasWidth = this.width
        canvasHeight = (this.imgHeight / this.imgWidth) * this.width
      }
      let obj = {}
      if ((this.rotation / 90) % 2 == 1) {
        obj = {
          x: (this.width - canvasHeight) / 2,
          y: (this.width - canvasWidth) / 2,
          width: canvasHeight,
          height: canvasWidth
        }
      } else {
        obj = {
          x: (this.width - canvasWidth) / 2,
          y: (this.width - canvasHeight) / 2,
          width: canvasWidth,
          height: canvasHeight
        }
      }

      wx.canvasToTempFilePath(
        {
          ...obj,
          canvasId: 'myCanvas',
          success: res => {
            this.$emit('success', res.tempFilePath)
          }
        },
        this
      )
    },
    // 旋转图片（每次点击旋转90度）
    rotateImage() {
      this.rotation = (this.rotation + 90) % 360
      this.$nextTick(() => {
        this.drawImage()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
canvas,
image {
  margin: 100px auto;
  margin-bottom: 0;
  border: 1px solid red;
}
</style>
