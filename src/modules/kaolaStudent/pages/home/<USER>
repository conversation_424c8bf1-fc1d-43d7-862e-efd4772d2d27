<template>
  <view class="switchBackground">
    <navbar></navbar>
    <view class="swiper-content">
      <image
        class="selectT"
        mode="aspectFit"
        src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/6b85174382123560194171_selectT.png"
      ></image>
      <view class="swiper-but l" @click="currentChange(1)">
        <image src="/static/imgs/kaolaStudent/nextBut.png"></image>
      </view>
      <view class="swiper-but r" @click="currentChange(2)">
        <image src="/static/imgs/kaolaStudent/nextBut.png"></image>
      </view>
      <swiper
        class="swiper"
        :current="current"
        @change="swiperChange"
        :duration="duration"
      >
        <swiper-item v-for="(item, index) of list" class="swiper-item" :key="index">
          <!-- {{ item.name }} -->
          <!-- backgroubImage -->
          <!-- item.backgroup_img -->
           <view class="url">
            <image class="url-c" :src="item.backgroup_img" mode="aspectFit"></image>
           </view>
          
        </swiper-item>
      </swiper>
    </view>
    <view class="teacher-info">
      <view class="top">
        <view class="name">{{ currentData.name }}</view>
        <image class="sex" :src="currentData.sex == 1 ? '/static/imgs/kaolaStudent/sex-1.png' : '/static/imgs/kaolaStudent/sex-2.png'" mode="aspectFit"></image>
      </view>
     
      <view class="tips">{{ currentData.tag }}</view>
      <view class="but" @click="change">
        <image src="/static/imgs/kaolaStudent/icons11212.png"></image>
        <text>和TA对话</text>
      </view>
    </view>

    <!-- <view v-for="item of list" @click="change(item.id)">
      <button>切换{{ item.name }}</button>
    </view> -->
  </view>
</template>

<script>
import { config } from '../../api/index.js'
import { backgroubImage } from '../../config.js'
import navbar from '../../components/commen/navbar.vue'
export default {
  components: {
    navbar
  },
  data() {
    return {
      list: [],
      current: 0,
      duration: 0,
      backgroubImage
    }
  },
  onLoad() {
    this.getList()
  },
  computed: {
    currentData() {
      let data = this.list[this.current]
      return (
        data || {
          name: '',
          tag: '',
          sex: 1,
          backgroup_img: backgroubImage,
        }
      )
    },
    AiAvatar() {
      return this.$store.getters['kaolaStudent/getAiAvatar']
    }
  },
  methods: {
    getList() {
      config.back.getList({}).then(res => {
        this.list = res.data
        console.log(" this.list")
        this.my()
      })
    },
    change() {
      // console.log(" this.list[this.current]", this.list[this.current])
      config.back
        .post({
          id: this.currentData.id
        })
        .then(res => {
          this.$store.commit(
            'kaolaStudent/setAiAvatar',
            this.list[this.current]
          )
          // console.log(" this.list[this.current]", this.list[this.current])
          this.$xh.back()
        })
    },
    my() {
      let current = this.list.findIndex(e => e.id == this.AiAvatar.id)
      this.current = current < 0 ? 0 : current
      this.duration = 500
    },
    swiperChange({ detail }) {
      this.current = detail.current
      this.$store.commit(
            'kaolaStudent/setAiAvatar',
            this.list[this.current]
          )
          // console.log(" this.list[this.current]", this.list[this.current])
    },
    currentChange(type) {
      if (type == '1' && this.current > 0) {
        this.current--
      }
      if (type == '2' && this.current < this.list.length - 1) {
        this.current++
      }
    }
  }
}
</script>

<style lang="scss" scoped>

.teacher-info {
  background-size: 100vw 58.133vw;
  background-image: url(https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/7ede174382153284071425_1901174357480703676937_Ellipse%2024%402x.png);
  width: 100vw;
  height: 58.133vw;
  padding-top: 46px;
  display: flex;
  flex-direction: column;
  // justify-items: center;
  align-items: center;
  margin-top: -20px;
  .top{
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 8px;
    // height: 40px;
    .sex{
      width: 24px;
      height: 24px;
    }
  }
  .name {
    font-weight: bold;
    font-size: 20px;
    color: #222333;
    line-height: 24px;
    height: 24px;
    // margin-bottom: 8px;
  }
  .tips {
    font-weight: 400;
    font-size: 14px;
    color: rgba(34, 35, 51, 0.8);
    line-height: 18px;
    margin-bottom: 32px;
  }
  .but {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 163px;
    height: 50px;
    background: #222333;
    border-radius: 30px 30px 30px 30px;
    //border: 2px solid rgba(255, 255, 255, 0.8);
    border: 2px solid;
    image {
      width: 20px;
      height: 20px;
      margin-right: 5px;
    }
    text {
      font-weight: 500;
      font-size: 16px;
      color: #ffffff;
    }
  }
}
.swiper {
}
.switchBackground {
  position: relative;
  background-image: url(https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/9156174382158802767595_71d917435746138658383_Group%2062%402x.png);
  background-size: 100vw 216.53vw;
  // height: 100vh;
}
.back {
  position: absolute;
  left: 0;
  top: 0;
  .page-back {
    width: 100vw;
    height: 216.53vw;
  }
  .end-back {
    position: absolute;
    left: 0;
    top: 158.4vw;
    width: 100vw;
    height: 58.133vw;
  }
}
.swiper-content {
  margin-top: 30px;
  display: flex;
  justify-content: center;
  position: relative;
  .selectT {
    position: absolute;
    // left: calc(50% - 64.5px);
    top: -46px;
    // top: -50px;
    // width: 129px;
    // height: 38px;
    // background-color: red;
    // width: 375px;
    height: 58px;
  }
  .swiper-but {
    position: absolute;
    top: 108px;
    height: 32px;
    width: 32px;
    background-size: 37px 329px;
    z-index: 1;
    // background-image: url(https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/3981174382126587968681_nextButBack.png);
    image {
      margin-left: 5px;
      margin-top: 123px;
      width: 24px;
      height: 24px;
    }
  }
  .l {
    //transform: rotateY(-180deg);
    left: 26px;
  }
  .r {
    transform: rotateY(-180deg);
    right: 26px;
  }
  .swiper {
    // width: 269px;
    height: 486px;
    width: 100vw;
    // background: rgba(34, 35, 51, 0.65);
    // box-shadow: 0px 29px 45px -13px rgba(111, 133, 5, 0.2);
    // border-radius: 16px 16px 16px 16px;
    // border: 4px solid #ffffff;
    // .swiper-item {
    //   border-radius: 16px 16px 16px 16px;
    // }
    .url {
      display: flex;
      justify-content: center;
      width: 100%;
      height: 100%;
      // border-radius: 16px 16px 16px 16px;
      // background-color: red;
    }
    .url-c {
      width: 259px;
      height: 100%;
    }
  }
}
</style>
