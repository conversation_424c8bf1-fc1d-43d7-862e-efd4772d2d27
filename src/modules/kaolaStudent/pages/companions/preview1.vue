<template>
  <view class="preview-page">
    <chatting :list="chattingList"></chatting>
    <informationInput
      v-if="chattingList.length == 0"
      ref="informationInput"
      :question_id="query.id"
      @change="arr => (chattingList = arr)"
    ></informationInput>
  </view>
</template>

<script>
import { config, appoint } from '../../api/index'
import chatting from '../../components/companions/chatting/index.vue'
import informationInput from '../../components/companions/chatting/information-input.vue'
export default {
  components: {
    chatting,
    informationInput
  },
  data() {
    return {
      chattingList: [],
      query: {
        id: ''
      }
    }
  },
  onLoad(query) {
    this.query = query
    appoint.teacher
      .getRemark({
        id: this.query.id
      })
      .then(res => {
        this.chattingList = res.data?.remark?.map(ee => {
          return {
            type: ee.message_type,
            source: 'user',
            content: {
              content: ee.content,
              message_type: ee.message_type,
              message: ee.content
            }
          }
        })

        this.$refs.informationInput.initList(this.chattingList)
      })
  },

  methods: {}
}
</script>

<style lang="scss" scoped></style>
