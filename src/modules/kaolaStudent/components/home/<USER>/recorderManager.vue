<template>
  <view class="recorderManager" @touchstart="start" @touchend="end">
    <view v-if="!press" @touchend.stop="end">按住说话</view>
    <view class="audio" v-if="press" @touchend.stop="end">
      <view class="tips">松手取消</view>
      <audioRippleAnimation></audioRippleAnimation>
    </view>
    <view class="audio-over" v-if="false">
      <view class="set">
        <view
          class="but-div"
          :class="{
            hover: region == 2
          }"
          @touchend.stop="end"
          @touchmove="move"
          ><view class="but">取消</view>
        </view>
        <view
          class="but-div"
          :class="{
            hover: region == 3
          }"
          @touchend.stop="end"
          @touchmove="move"
          ><view class="but">转文字</view>
        </view>
      </view>
      <view
        class="bottom"
        :class="{
          hover: region == 1
        }"
        @touchend.stop="end"
        @touchmove="move"
      >
        松手取消
      </view>
    </view>
  </view>
</template>

<script>
import { QCloudAIVoice } from '../../../config.js'
import audioRippleAnimation from './audioRippleAnimation.vue'
const plugin = requirePlugin('QCloudAIVoice')
let resultText = ''
export default {
  components: {
    audioRippleAnimation
  },
  data() {
    return {
      timeout: null,
      status: 0,
      plugin: null,
      press: false,

      speechRecognizerManager: null,
      result: '',
      recording: false,
      //
      windowWidth: 375,
      windowHeight: 667,
      region: 1
    }
  },
  created() {
    let { windowWidth, windowHeight } = wx.getSystemInfoSync()
    this.windowWidth = windowWidth
    this.windowHeight = windowHeight
    this.init()
  },
  methods: {
    //     that.$emit('sentenceRecognition', data)
    init() {
      const self = this
      this.speechRecognizerManager = plugin.speechRecognizerManager()

      // 开始识别
      this.speechRecognizerManager.OnRecognitionStart = res => {
        console.log('开始识别', res)
        this.recording = true
        this.result = ''
      }
      // 一句话开始
      this.speechRecognizerManager.OnSentenceBegin = res => {
        console.log('一句话开始', res)
      }
      // 识别变化时
      this.speechRecognizerManager.OnRecognitionResultChange = res => {
        console.log('识别变化时', res)
        this.result = `${resultText || ''}${res.result.voice_text_str}`
      }
      // 一句话结束
      this.speechRecognizerManager.OnSentenceEnd = res => {
        console.log('一句话结束', res)
        resultText += res.result.voice_text_str
        this.result = resultText
      }
      // 识别结束
      this.speechRecognizerManager.OnRecognitionComplete = res => {
        console.log('识别结束', this.result, res)
        if (this.result) {
          self.$emit('sentenceRecognition', this.result)
        } else {
          wx.showToast({ title: '未识别到语音', icon: 'none' })
        }
        this.recording = false
      }
      // 识别错误
      this.speechRecognizerManager.OnError = res => {
        console.log('识别错误', res)
        this.recording = false
      }
      this.speechRecognizerManager.OnRecorderStop = () => {
        console.log('超过录音时长')
      }
    },
    startLy: async function (e) {
      const self = this
      wx.getSetting({
        success(res) {
          if (!res.authSetting['scope.record']) {
            wx.authorize({
              scope: 'scope.record',
              success() {
                // 用户已经同意小程序使用录音功能，后续调用 record 接口不会弹窗询问
                self.startAsr()
              },
              fail() {
                wx.showToast({ title: '未获取录音权限', icon: 'none' })
                wx.openSetting({
                  success: function (res) {
                    if (!res.authSetting['scope.record']) {
                      wx.showModal({
                        title: '提示',
                        content: '需要您授权录音权限',
                        showCancel: false,
                        success: function (modalSuccess) {
                          wx.openSetting({
                            success: function (settingData) {
                              if (settingData.authSetting['scope.record']) {
                                self.startAsr()
                              }
                            }
                          })
                        }
                      })
                    } else {
                      self.startAsr()
                    }
                  }
                })
                // console.log("fail auth")
              }
            })
          } else {
            self.startAsr()
            // console.log("record has been authed")
          }
        },
        fail(res) {
          // console.log("fail",res)
        }
      })
    },
    startAsr: function () {
      wx.showToast({
        title: '建立连接中',
        icon: 'none'
      })
      resultText = ''
      const params = {
        // 用户参数
        appid: QCloudAIVoice.appid,
        secretid: QCloudAIVoice.secretid,
        secretkey: QCloudAIVoice.secretkey,
        // 录音参数
        duration: 60 * 1000,
        // frameSize: 1.28,  //单位:k
        // 实时识别接口参数
        engine_model_type: '16k_zh',
        // 以下为非必填参数，可跟据业务自行修改
        // hotword_id : '08003a00000000000000000000000000',
        needvad: 1,
        // filter_dirty: 1,
        // filter_modal: 2,
        filter_punc: 1,
        // convert_num_mode : 1,
        word_info: 2,
        vad_silence_time: 200
      }
      this.speechRecognizerManager.start(params)
      wx.vibrateShort()
    },
    endLy: function (e) {
      this.recording = false
      this.speechRecognizerManager.stop()
    },
    start() {
      this.timeout = setTimeout(() => {
        this.timeout = null
        this.press = true
        this.startLy()
      }, 500)
    },
    end() {
      console.log(this.region)
      if (this.timeout) {
        this.press = false
        clearTimeout(this.timeout)
      } else {
        this.press = false
        this.endLy()
      }
    },
    move(ev) {
      let data = ev.touches[0]
      let type = 1
      if (data.pageY > this.windowHeight - 150) {
        type = 1
      } else {
        if (data.pageX < this.windowWidth / 2) {
          type = 2
        } else {
          type = 3
        }
      }
      this.region = type
    }
  }
}
</script>

<style lang="less" scoped>
.recorderManager {
  width: 100%;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 40px;
  .audio-over {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 1;
    background-color: #000;
    .set {
      height: calc(100% - 150px);
      display: flex;
      // align-items: center;
      .but-div {
        display: flex;
        justify-content: center;
        align-items: flex-end;
        width: 50%;
        border-right: 1px solid #fff;
        padding-bottom: 20px;
        &:nth-child(2) {
          border-right: none;
        }
        .but {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          text-align: center;
          line-height: 50px;
          color: #000;
          background-color: #ffffff;
        }
      }
      .hover {
        .but {
          background-color: #f2f4f7;
        }
      }
    }
    .bottom {
      background-color: #000;
      height: 150px;
      width: 100%;
      text-align: center;
      line-height: 150px;
      border-top: 1px solid #fff;
    }
    .hover {
      background-color: #999;
    }
  }
  view {
    font-weight: 400;
    // font-size: 16px;
    color: #ffffff;
  }

  .audio {
    display: flex;
    position: absolute;
    left: 0;
    top: 0;
    background-color: #222333;
    height: 50px;
    width: 100%;
    border-radius: 12px;
    justify-content: center;
    align-items: center;
    z-index: 1;
    .tips {
      position: absolute;
      top: -21px;
      left: calc(50% - 26px);
      font-weight: 400;
      font-size: 13px;
      color: #ffffff;
      line-height: 15px;
    }
  }
}
</style>
