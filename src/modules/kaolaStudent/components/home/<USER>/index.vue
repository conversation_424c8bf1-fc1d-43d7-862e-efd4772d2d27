<template>
  <view class="pictures">
    <view class="topNavbar">
      <view :style="statusBarHeight"></view>
      <view class="navbar">
        <view class="back" @click="back">
          <image
            class="navbarback"
            src="/static/imgs/kaolaStudent/close.png"
          ></image>
        </view>
        <view class="title">{{ title }}</view>
      </view>
    </view>
    <view class="body">
      <camer v-if="show" @success="camerSuccess"></camer>
      <cropping
        v-if="!show"
        :imgUrl="url"
        @success="croppingSuccess"
        @hide="show = true"
      ></cropping>
    </view>
  </view>
</template>

<script>
import camer from './camer.vue'
import cropping from './cropping.vue'
export default {
  components: {
    camer,
    cropping
  },
  data() {
    return {
      show: true,
      url: '',
      statusBarHeight: 'height:25px;'
    }
  },
  created() {
    let { statusBarHeight } = uni.getSystemInfoSync()
    this.statusBarHeight = `height:${statusBarHeight}px;`
  },
  methods: {
    croppingSuccess(url) {
      console.log(url)
      this.$emit('success', url)
    },
    camerSuccess(url) {
      this.url = url
      this.show = false
    },
    back() {
      // 直接使用uni-app的返回API
      uni.navigateBack()
      // 或者使用项目中封装的返回方法（如果有的话）
      // this.$xh.back()
    }
  }
}
</script>

<style lang="scss" scoped>
.pictures {
  position: relative;
  top: 0;
  left: 0;
  z-index: 12;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  .body {
    flex: 1;
    height: 50px;
  }
}
.topNavbar {
  width: 100%;
  z-index: 1;
  background-color: #000;
}
.navbar {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50px;
  position: relative;
  .title {
    font-weight: 400;
    font-size: 16px;
    color: #000000;
  }
  .back {
    position: absolute;
    left: 0;
    padding-left: 16px;
    height: 100%;
    display: flex;
    align-items: center;
  }
  .navbarback {
    width: 24px;
    height: 24px;
  }
}
</style>
