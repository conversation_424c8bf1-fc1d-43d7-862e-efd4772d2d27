import { mapState } from 'vuex'
import $xh from '../../../utlis/index'
import { getCode, Login, getPhone, config } from '../api/index'
import { app_id, backgroubImage } from '../config'
export default {
  state: {
    token: uni.getStorageSync('__xingyun_token__') || '',
    userinfo: uni.getStorageSync('__xingyun_userinfo__') || {},
    aiAvatar: uni.getStorageSync('__xingyun_aiAvatar__') || {
      backgroup_img: backgroubImage
    },
    userinfo2: uni.getStorageSync('__xingyun_userinfo2__') || {},
    original_type: uni.getStorageSync('original_type') || '1',
    systemMessage: uni.getStorageSync('__system_message') || {
      isNavigate: false
    },
    xyppid: uni.getStorageSync('__xingyun_xyppid__') || '',

    qingBeiAnswerin: {
      show: false,
      data: {}
    },
    chatting: {
      list: [],
      stop_num: 0,
      scrollTop: 0
    }
  },
  mutations: {
    updata(store) {
      store.token = uni.getStorageSync('__xingyun_token__') || ''
      store.userinfo = uni.getStorageSync('__xingyun_userinfo__') || {}
      store.aiAvatar = uni.getStorageSync('__xingyun_aiAvatar__') || {
        backgroup_img: backgroubImage
      }
    },
    setToken(store, token) {
      store.token = token
      // $xh.setStorageSync_('__xingyun_token__', token)
      uni.setStorageSync('__xingyun_token__', token)
    },
    setUserinfo(store, userinfo) {
      store.userinfo = userinfo
      uni.setStorageSync('__xingyun_userinfo__', userinfo)
    },
    setUserinfo2(store, userinfo) {
      store.userinfo2 = userinfo
      uni.setStorageSync('__xingyun_userinfo2__', userinfo)
    },
    scrollTopChange(store) {
      store.chatting.scrollTop++
    },
    stopChange(store) {
      store.chatting.stop_num++
    },
    pushChatting(store, data) {
      if (typeof data == 'object') {
        if (data.original_type == undefined) {
          data.original_type = store.original_type
        }
        store.chatting.list.push(data)
      }
    },
    clearChatting(store) {
      store.chatting.list = []
    },
    setAiAvatar(store, data) {
      // data.backgroup_img = backgroubImage
      store.aiAvatar = data
      uni.setStorageSync('__xingyun_aiAvatar__', data)
    },
    setOriginalType(store, data) {
      store.original_type = data
      uni.setStorageSync('original_type', data)
    },
    setSystemMessage(store, data) {
      console.log('__system_message')
      store.systemMessage = data
      uni.setStorageSync('__system_message', data)
    },
    setXyppid(store, xyppid) {
      store.xyppid = xyppid
      uni.setStorageSync('__xingyun_xyppid__', xyppid)
    },
    setQingBeiAnswerin(store, data) {
      store.qingBeiAnswerin.show = data.show
      store.qingBeiAnswerin.data = data.query
    }
  },
  actions: {
    LOGIN({ commit }, code) {
      return new Promise((resolve, reject) => {
        wx.login({
          success(e) {
            let login_code = e.code
            getCode({
              app_id,
              code: login_code
            })
              .then(data => {
                // $xh.setStorageSync_('__xingyun_weixinInfo__', data.data)
                uni.setStorageSync('__xingyun_weixinInfo__', data.data)
                getPhone({
                  app_id,
                  code
                })
                  .then(response => {
                    let phone = response.data.phone
                    uni.setStorageSync('__xingyun_userPhone__', phone)
                    let xyppid = uni.getStorageSync('shareUserXyppid') || ''
                    let objInfo = {
                      wxopenid: data.data.openid,
                      account: phone,
                      activity_id: '',
                      extendu_id: '',
                      brand_id: process.env.VUE_APP_BRANDID,
                      merchant_id: process.env.VUE_APP_MERCHANTID,
                      app_id,
                      xyppid
                    }
                    let shareInfo = uni.getStorageSync('__xingyun_share__')
                    if (shareInfo.employee_id) {
                      objInfo.employee_id = shareInfo.employee_id
                      // objInfo.promoter_id = shareInfo.promoter_id
                      objInfo.promoter_type = shareInfo.promoter_type
                    }
                    Login(objInfo)
                      .then(res => {
                        let token = res.data.token
                        let info = res.data
                        commit('setUserinfo', info)
                        commit('setToken', token)
                        if (shareInfo.employee_id) {
                        }
                        //获取AI主题
                        config.back.my({}).then(res => {
                          commit('setAiAvatar', res.data)
                        })
                        resolve(info)
                      })
                      .catch(err => {
                        reject(err)
                      })
                  })
                  .catch(err => {
                    reject(err)
                  })
              })
              .catch(err => {
                reject(err)
              })
          }
        })
      })
    },
    LOGINH5({ commit }, code) {
      return new Promise((resolve, reject) => {
        wx.login({
          success(e) {
            let login_code = e.code
            getCode({
              app_id,
              code: login_code
            })
              .then(data => {
                // $xh.setStorageSync_('__xingyun_weixinInfo__', data.data)
                uni.setStorageSync('__xingyun_weixinInfo__', data.data)
                getPhone({
                  app_id,
                  code
                })
                  .then(response => {
                    let phone = response.data.phone
                    uni.setStorageSync('__xingyun_userPhone__', phone)
                    let objInfo = {
                      wxopenid: data.data.openid,
                      account: phone,
                      activity_id: '',
                      extendu_id: '',
                      brand_id: process.env.VUE_APP_BRANDID,
                      merchant_id: process.env.VUE_APP_MERCHANTID,
                      app_id
                    }
                    let shareInfo = uni.getStorageSync('__xingyun_share__')
                    if (shareInfo.employee_id) {
                      objInfo.employee_id = shareInfo.employee_id
                      // objInfo.promoter_id = shareInfo.promoter_id
                      objInfo.promoter_type = shareInfo.promoter_type
                    }
                    Login(objInfo)
                      .then(res => {
                        let token = res.data.token
                        let info = res.data
                        commit('setUserinfo', info)
                        commit('setToken', token)
                        if (shareInfo.employee_id) {
                          shareRecord({
                            ...shareInfo,
                            student_id: info.student_id,
                            promoter_id: shareInfo.student_id,
                            brand_id: process.env.VUE_APP_BRANDID,
                            merchant_id: process.env.VUE_APP_MERCHANTID,
                            type: '1'
                          }).then(resTwo => {
                            console.log(resTwo)
                          })
                        }
                        resolve(info)
                      })
                      .catch(err => {
                        reject(err)
                      })
                  })
                  .catch(err => {
                    reject(err)
                  })
              })
              .catch(err => {
                reject(err)
              })
          }
        })
      })
    }
  },
  getters: {
    getChattingList(state) {
      return state.chatting.list
    },
    getAiAvatar(state) {
      return state.aiAvatar
    }
  }
}
