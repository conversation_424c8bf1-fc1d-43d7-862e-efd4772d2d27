<template>
  <view>
    <picker
      mode="multiSelector"
      :range="columns"
      range-key="name"
      confirmColor="#ff6616"
      :value="defaultIndex"
      @change="change"
      @columnchange="columnchange"
    >
      <slot></slot>
    </picker>
  </view>
</template>

<script>
export default {
  props: {
    value: {
      type: Array,
      default: []
    },
    options: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      columns: [[], []],
      defaultIndex: [0, 0]
    }
  },
  mounted() {
    this.init()
  },
  watch: {
    options() {
      this.init()
    },
    value() {
      this.init()
    }
  },
  methods: {
    init() {
      if (this.options.length) {
        if (this.value.length) {
          let index1 = this.options.findIndex(item => item.id == this.value[0])
          if (index1 == -1) {
            index1 = 0
          }
          let index2 = this.options[index1].subs.findIndex(
            item => item.id == this.value[1]
          )
          if (index2 == -1) {
            index2 = 0
          }
          this.defaultIndex[0] = index1
          this.defaultIndex[1] = index2
        }
        this.updata(0)
        this.updata(1)
      }
    },
    change(e) {
      this.defaultIndex[0]
      let one = this.options[this.defaultIndex[0]]
      let two = one.subs[this.defaultIndex[1]]
      this.$emit('input', [one.id, two.id])
      this.$emit('update:name', [one.name, two.name])
      this.$emit('change', {
        id: [one.id, two.id],
        name: [one.name, two.name]
      })
    },
    columnchange(el) {
      let column = el.detail.column
      let value = el.detail.value
      this.defaultIndex[column] = value
      if (column == 0) {
        this.updata(1)
      }
    },
    updata(index) {
      if (index === 0) {
        this.$set(this.columns, 0, this.options)
      }
      if (index === 1) {
        this.$set(this.columns, 1, this.options[this.defaultIndex[0]].subs)
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
