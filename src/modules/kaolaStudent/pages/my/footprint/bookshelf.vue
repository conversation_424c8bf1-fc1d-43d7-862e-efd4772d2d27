<template>
  <view class="bookshelf-page">
    <view class="head">
      <navbar class="navbar" title=" "></navbar>
      <view class="search">
        <pickerSelectMulti2
          :options="tree"
          v-model="stageGrade"
          @change="stageGradeChage"
          :multicolumn="2"
        >
          <view class="search-item">
            <view>{{ grade_id_name }}</view>
            <image
              class="img3"
              src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/6ace174418173457834915_Frame%2012%402x.png"
            ></image>
          </view>
        </pickerSelectMulti2>
      </view>
    </view>
    <view class="bookshelf">
      <view class="list">
        <template v-for="(item, index) of subject">
          <view
            class="item"
            :style="{
              backgroundImage: 'url(' + item.img + ')'
            }"
            @click="details(item)"
          >
            <view class="num"> {{ item.num }}</view>
          </view>

          <image
            class="fenge"
            v-if="(index + 1) % 3 === 0 || index == subject.length - 1"
            src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/6e8417443771512288054_image%402x.png"
          ></image>
        </template>
      </view>
    </view>
  </view>
</template>

<script>
import navbar from '../../../components/commen/navbar.vue'
import pickerSelectMulti2 from '../../../components/commen/picker-select-multi2.vue'
import { config, student } from '../../../api/index.js'
export default {
  components: {
    navbar,
    pickerSelectMulti2
  },
  data() {
    return {
      subject: [],
      subjectIcon: [
        {
          name: '数学',
          num: 0,
          img: 'https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/8738174437656917679998_Group%20127%402x.png'
        },
        {
          name: '物理',
          num: 0,
          img: 'https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/6ab3174437666652360180_Group%20135%402x.png'
        },
        {
          name: '化学',
          num: 0,
          img: 'https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/578d174437668827794206_Group%20130%402x.png'
        },
        {
          name: '英语',
          num: 0,
          img: 'https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/4749174437670796343160_Group%20133%402x.png'
        },
        {
          name: '语文',
          num: 0,
          img: 'https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/3be2174437672415249355_Group%20132%402x.png'
        },
        {
          name: '历史',
          num: 0,
          img: 'https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/1e80174437673767897948_Group%20131%402x.png'
        },
        {
          name: '地理',
          num: 0,
          img: 'https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/3b13174437675502467469_Group%20134%402x.png'
        },
        {
          name: '生物',
          num: 0,
          img: 'https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/1176174437676892856045_Group%20129%402x.png'
        },
        {
          name: '政治',
          num: 0,
          img: 'https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/8a89174437678105032297_Group%20136%402x.png'
        }
      ],
      tree: [],
      stageGrade: [],
      grade_id_name: ''
    }
  },
  computed: {
    userinfo2() {
      return this.$store.state.kaolaStudent.userinfo2
    }
  },
  created() {
    // Default to "all" stages and grades
    this.stageGrade = ['0', '0'] 
    this.grade_id_name = "全部"
    this.getData()
    this.getSubject()
  },
  methods: {
    getSubject() {
      student.question
        .subject({
          state_id: this.stageGrade[0],
          grade_id: this.stageGrade[1],
          subject_id: ''
        })
        .then(res => {
          this.subject = res.data.map(item => {
            let data = this.subjectIcon.find(e => e.name == item.name)
            if (data) {
              item.img = data.img
            }
            return item
          })
        })
    },
    details(item) {
      this.$xh.push(
        'kaolaStudent',
        `pages/my/footprint/index?subject_id=${item.id}&title=${item.name}&state_id=${this.stageGrade[0]}&grade_id=${this.stageGrade[1]}`
      )
    },
    stageGradeChage(e) {
      console.log(e)
      this.grade_id_name = e.name[1]
      this.getSubject()
    },
    getData() {
      config.stageGrade({}).then(res => {
        this.tree = [
          {
            name: '全部',
            id: '0',
            subs: [
              {
                name: '全部',
                id: '0'
              }
            ]
          }
        ].concat(res.data)
        // console.log("this.tree+++++", this.tree)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.bookshelf-page {
  width: 100vw;
  height: 100vh;
  overflow-y: auto;
  background-size: 100vw 812px;
  background: linear-gradient(180deg, #d4ff00 0%, #ffffff 100%);
  border-radius: 0px 0px 0px 0px;
  .bookshelf {
    width: 100%;
    height: 170.66vw;
    background-size: 100% 100%;
    background-image: url(https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/1bb8174437640705941888_Group%20151%402x.png);
    .list {
      display: flex;
      flex-wrap: wrap;
      padding-top: 32px;
      padding-left: 12px;
      .item {
        max-width: calc((100% - 36px) / 3);
        height: 128px;
        width: 109px;
        background-size: 100% 100%;
        margin-right: 12px;
        // margin-bottom: 29px;
        .num {
          padding-top: 60px;
          text-align: center;
          font-weight: bold;
          font-size: 22px;
          color: #222333;
          line-height: 20px;
        }
      }
      .fenge {
        height: 33px;
        width: calc(100vw);
        margin-left: -12px;
      }
    }
  }
  .head {
    position: relative;
    height: 49.86vw;
    width: 100%;
    background-size: 100% 100%;
    background-image: url(https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/6e23174437625906111762_Group%20149%402x.png);
  }
  .search {
    display: flex;
    justify-content: flex-end;
    padding-right: 12px;
    padding-top: 14px;
  }
  .search-item {
    // position: absolute;
    // top: 112px;
    // right: 12px;
    width: 78px;
    height: 32px;
    background: rgba(255, 255, 255, 0.32);
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #ffffff;
    display: flex;
    position: relative;
    align-items: center;
    justify-content: center;
    .img3 {
      margin-left: 4px;
      width: 10px;
      height: 10px;
    }
    view {
      font-weight: 400;
      font-size: 14px;
      color: #222333;
    }
  }
}
</style>
