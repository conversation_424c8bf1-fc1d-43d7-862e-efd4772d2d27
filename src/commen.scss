view,
div,
cover-view,
input {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}
.box-show {
  box-shadow: 0 0px 10px 1px rgba(0, 0, 0, 0.12);
  -moz-box-shadow: 0 0px 10px 1px rgba(0, 0, 0, 0.12);
  -webkit-box-shadow: 0 0px 10px 1px rgba(0, 0, 0, 0.12);
}
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex {
  display: flex;
  align-items: center;
}
image {
  will-change: transform;
}
// 安全区
.safePage {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
// .commen {
//    // padding-bottom: r(120);
//    // height: 100vh;
//    // height: 100%;
//    // height: 100px;
//    // background-color: #F6C864;
//    background-color: #f8f8f8;
//  }
//  .height-footer{
//     height: r(120);
//  }
page {
  background-color: #f5f6f7;
}
.page {
  min-height: 100vh;
  background-color: #f7f7f7;
}
.btn:active {
  transform: scale(0.9);
}

// 百度小程序常用全局css---
.page-baidu {
  height: 100vh;
  background-color: #fff;
  overflow-y: auto;
  position: relative;
}
.white {
  color: #fff;
}
.safe-btm {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.button {
  transition: all 0.25s;
}
.button:active {
  transform: scale(0.9);
}
.hide-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.no-data {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 140rpx;
  font-size: 28rpx;
  color: #ccc;
  line-height: 50px;
}

.job-textarea-placeholder {
  font-size: 28rpx;
  color: #c0c7ce;
}
.job-input-placeholder {
  font-size: 32rpx;
  color: #c0c7ce;
}
.flex-wrap {
  flex-wrap: wrap;
}

// .register-textarea .u-textarea {
//   border: none !important;
//   padding: 14px 16px !important;
//   height: 140px !important;
//   border-radius: 10px !important;
// }

// .register-textarea .u-textarea__count {
//   position: absolute !important;
//   right: 16px !important;
//   bottom: 14px !important;
//   font-size: 14px !important;
//   color: rgba(34, 35, 51, 0.6) !important;
// }


.RichText span {
  background-color: red;
  max-width: 81.86vw !important;
  white-space: pre !important;
  word-wrap: break-word !important;
}
.ellipsis {
  display: -webkit-box;
  line-clamp: 2;
  -webkit-line-clamp: 2; /* 限制行数为2 */
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.ellipsis-row1 {
  display: -webkit-box;
  line-clamp: 1;
  -webkit-line-clamp: 1; /* 限制行数为1 */
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.page-bottom-but {
  z-index: 1;
  width: 231px;
  text-align: center;
  height: 44px;
  line-height: 44px;
  background: #222333;
  border-radius: 33px 33px 33px 33px;
  font-weight: bold;
  font-size: 14px;
  color: #ffffff;
}
.page-bottom-but-fixed {
  position: fixed;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
}


@font-face {
  font-family: 'ShuHeiTi';
  src: url('https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/datatpls/Alimama_ShuHeiTi_Bold.ttf')
    format('truetype');
  font-weight: 500;
  font-style: normal;
}
@font-face {
  font-family: 'OPPOSans-B';
  src: url('https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/8d63174797917447471190_OPPOSans-B.ttf')
    format('truetype');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'OPPOSans-H';
  src: url('https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/datatpls/OPPOSans-H.ttf')
    format('truetype');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'PuHuiTi_75_SemiBold';
  src: url('https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/datatpls/PuHuiTi_75_SemiBold.ttf')
    format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'PuHuiTi_55_Regular';
  src: url('https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/datatpls/PuHuiTi_55_Regular.ttf')
    format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Inter-Regular';
  src: url('https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/datatpls/Inter-Regular.woff2')
    format('woff2');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: "SourceHanSansCN-VF";
  src: url('https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/datatpls/SourceHanSansCN-VF.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}
.ShuHeiTi {
  font-family: 'ShuHeiTi';
}
.OPPOSans {
  font-family: OPPOSans-H, OPPOSans-B;
}
