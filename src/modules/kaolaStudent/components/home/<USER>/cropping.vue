<template>
  <view class="container">
    <!-- 图片显示 -->
    <image
      :style="{ height: cameraHeight + 'px' }"
      :src="url"
      mode="aspectFit"
      class="image"
      @load="imageLoaded"
    />

    <!-- 裁剪区域 -->
    <view
      class="movable-area"
      :style="{
        width: areaWidth + 'px',
        height: areaHeight + 'px',
        '-webkit-mask-position': mask.maskPosition,
        '-webkit-mask-size': mask.maskSize
      }"
    >
      <!-- 裁剪框 -->
      <view
        class="crop-box"
        :x="cropX"
        :y="cropY"
        direction="all"
        @change="onCropMove"
        :style="{
          width: cropWidth + 'px',
          height: cropHeight + 'px',
          left: cropX + 'px',
          top: cropY + 'px'
        }"
        inertia
        out-of-bounds
      >
        <!-- 拖拽点（四个角） -->
        <view
          class="drag-point drag-point-tl"
          @touchstart="startDrag('top-left', $event)"
          @touchmove="onDrag($event)"
          @touchend="endDrag($event)"
        ></view>
        <view
          class="drag-point drag-point-tr"
          @touchstart="startDrag('top-right', $event)"
          @touchmove="onDrag($event)"
          @touchend="endDrag($event)"
        ></view>
        <view
          class="drag-point drag-point-bl"
          @touchstart="startDrag('bottom-left', $event)"
          @touchmove="onDrag($event)"
          @touchend="endDrag($event)"
        ></view>
        <view
          class="drag-point drag-point-br"
          @touchstart="startDrag('bottom-right', $event)"
          @touchmove="onDrag($event)"
          @touchend="endDrag($event)"
        ></view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="button-group">
      <view class="tips">一次只框1道题，效果更好</view>
      <view class="but" @click="$emit('hide')">重拍</view>
      <view class="but2" @click="confirmCrop">
        <view class="b">
          <image class="img" src="/static/imgs/kaolaStudent/duihao.svg"></image>
        </view>
      </view>
      <!-- 旋转 -->
      <view class="but" @click="rotateImage">旋转</view>
    </view>
    <view style="position: absolute; left: -100vw; top: 0; z-index: -1">
      <canvas
        canvas-id="cropCanvas"
        :style="{
          width: imageInfo.width + 'px',
          height: imageInfo.height + 'px'
        }"
      ></canvas>
    </view>
    <view style="height: 200px"></view>
    <rotate :url="imgUrl" ref="rotate" @success="rotateSuccess"></rotate>
  </view>
</template>
<script>
import rotate from './rotate.vue'
export default {
  components: {
    rotate
  },
  props: {
    imgUrl: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      areaWidth: 300, // movable-area宽度（动态获取）
      areaHeight: 300, // movable-area高度（动态获取）
      cropX: 18, // 初始位置X
      cropY: 50, // 初始位置Y
      cropWidth: 200, // 初始宽度
      cropHeight: 200, // 初始高度
      minSize: 50, // 最小裁剪尺寸
      dragType: null, // 拖拽类型（top-left等）
      startX: 0, // 拖拽起始X
      startY: 0, // 拖拽起始Y
      startCropX: 0, // 拖拽起始cropX
      startCropY: 0,
      startCropWidth: 0,
      startCropHeight: 0,
      imageInfo: null, // 图片信息（实际尺寸）
      displayInfo: null, // 图片显示信息（缩放后尺寸和偏移）
      rotate: 0,
      cameraHeight: '',
      url: ''
    }
  },
  created() {
    console.log(this.imgUrl, 'imgUrl')
    this.url = this.imgUrl
    let { windowHeight, windowWidth, statusBarHeight } = uni.getSystemInfoSync()
    let cameraHeight = windowHeight - statusBarHeight - 160 - 50
    this.cropWidth = windowWidth - 18 * 2
    this.cropHeight = 123
    this.cropX = 18
    this.cropY = cameraHeight / 2 - 123 / 2
    this.cameraHeight = cameraHeight
  },
  computed: {
    mask() {
      return {
        maskPosition: `0 0, ${this.cropX}px ${this.cropY}px`,
        maskSize: `100% 100%, ${this.cropWidth}px ${this.cropHeight}px;`
      }
      return `-webkit-mask-position: 0 0;-webkit-mask-size: 100% 100%`
    }
  },
  methods: {
    rotateSuccess(url) {
      this.url = url
    },
    rotateImage() {
      console.log(this.$refs.rotate.rotateImage)
      this.$refs.rotate.rotateImage()
    },
    // 图片加载完成后初始化信息
    imageLoaded(e) {
      const { width, height } = e.detail
      this.imageInfo = { width, height }
      // 获取图片容器尺寸
      uni
        .createSelectorQuery()
        .in(this)
        .select('.image')
        .boundingClientRect(rect => {
          const { width: containerWidth, height: containerHeight } = rect
          // 计算图片缩放后的尺寸和偏移
          const ratio = Math.min(
            containerWidth / width,
            containerHeight / height
          )
          const displayWidth = width * ratio
          const displayHeight = height * ratio
          const offsetX = (containerWidth - displayWidth) / 2
          const offsetY = (containerHeight - displayHeight) / 2

          this.displayInfo = { displayWidth, displayHeight, offsetX, offsetY }
          this.areaWidth = containerWidth
          this.areaHeight = containerHeight
        })
        .exec()
    },

    // 开始拖拽调整大小
    startDrag(type, event) {
      this.dragType = type
      const touch = event.touches[0]
      this.startX = touch.clientX
      this.startY = touch.clientY
      this.startCropX = this.cropX
      this.startCropY = this.cropY
      this.startCropWidth = this.cropWidth
      this.startCropHeight = this.cropHeight
    },

    // 拖拽中
    onDrag(event) {
      if (!this.dragType) return
      event.preventDefault()
      const touch = event.touches[0]
      const deltaX = touch.clientX - this.startX
      const deltaY = touch.clientY - this.startY

      switch (this.dragType) {
        case 'bottom-right':
          this.handleBottomRight(deltaX, deltaY)
          break
        case 'top-left':
          this.handleTopLeft(deltaX, deltaY)
          break
        case 'top-right':
          this.handleTopRight(deltaX, deltaY)
          break
        case 'bottom-left':
          this.handleBottomLeft(deltaX, deltaY)
          break
      }
    },

    // 结束拖拽
    endDrag() {
      this.dragType = null
      // document.removeEventListener('touchmove', this.onDrag)
      // document.removeEventListener('touchend', this.endDrag)
    },

    // 处理右下角拖拽
    handleBottomRight(deltaX, deltaY) {
      let newWidth = this.startCropWidth + deltaX
      let newHeight = this.startCropHeight + deltaY

      // 限制最小尺寸和边界
      newWidth = Math.max(newWidth, this.minSize)
      newHeight = Math.max(newHeight, this.minSize)
      newWidth = Math.min(newWidth, this.areaWidth - this.cropX)
      newHeight = Math.min(newHeight, this.areaHeight - this.cropY)

      this.cropX = this.startCropX
      this.cropY = this.startCropY

      this.cropWidth = newWidth
      this.cropHeight = newHeight
    },
    // 处理左下角拖拽
    handleBottomLeft(deltaX, deltaY) {
      let newX = this.startCropX + deltaX
      let newWidth = this.startCropWidth - deltaX
      let newHeight = this.startCropHeight + deltaY
      let endX = this.startCropX + this.startCropWidth - this.minSize
      // 限制位置和尺寸
      newX = Math.max(newX, 0)
      newX = Math.min(newX, endX)
      newWidth = Math.max(newWidth, this.minSize)
      newHeight = Math.max(newHeight, this.minSize)
      newHeight = Math.min(newHeight, this.areaHeight - this.cropY)
      // 计算实际调整后的delta（可能被边界限制）
      const adjDeltaX = newX - this.startCropX
      this.cropX = newX
      this.cropWidth = this.startCropWidth - adjDeltaX
      this.cropHeight = newHeight
    },
    // 处理左上角拖拽
    handleTopLeft(deltaX, deltaY) {
      let newX = this.startCropX + deltaX
      let newY = this.startCropY + deltaY
      let newWidth = this.startCropWidth - deltaX
      let newHeight = this.startCropHeight - deltaY
      let endX = this.startCropX + this.startCropWidth - this.minSize
      let endY = this.startCropY + this.startCropHeight - this.minSize

      // 限制位置和尺寸
      newX = Math.max(newX, 0)
      newY = Math.max(newY, 0)
      newX = Math.min(newX, endX)
      newY = Math.min(newY, endY)
      newWidth = Math.max(newWidth, this.minSize)
      newHeight = Math.max(newHeight, this.minSize)

      // 计算实际调整后的delta（可能被边界限制）
      const adjDeltaX = newX - this.startCropX
      const adjDeltaY = newY - this.startCropY

      this.cropX = newX
      this.cropY = newY
      this.cropWidth = this.startCropWidth - adjDeltaX
      this.cropHeight = this.startCropHeight - adjDeltaY
    },
    // 处理右上角拖拽
    handleTopRight(deltaX, deltaY) {
      let newY = this.startCropY + deltaY
      let newWidth = this.startCropWidth + deltaX
      let newHeight = this.startCropHeight - deltaY
      let endY = this.startCropY + this.startCropHeight - this.minSize
      // 限制位置和尺寸
      newY = Math.max(newY, 0)
      newY = Math.min(newY, endY)
      newWidth = Math.max(newWidth, this.minSize)
      newWidth = Math.min(newWidth, this.areaWidth - this.cropX)
      newHeight = Math.max(newHeight, this.minSize)

      // 计算实际调整后的delta（可能被边界限制）
      const adjDeltaY = newY - this.startCropY

      // this.cropX = newX
      this.cropY = newY
      this.cropWidth = newWidth
      this.cropHeight = this.startCropHeight - adjDeltaY
    },

    // 处理其他方向（代码类似，略）
    // ...

    // 裁剪框移动事件
    onCropMove(e) {
      if (this.dragType == null) {
        this.cropX = e.detail.x
        this.cropY = e.detail.y
      }
    },

    // 确认裁剪
    async confirmCrop() {
      if (!this.displayInfo || !this.imageInfo) return

      // 转换坐标到原始图片尺寸
      const scale = this.imageInfo.width / this.displayInfo.displayWidth
      const originX = (this.cropX - this.displayInfo.offsetX) * scale
      const originY = (this.cropY - this.displayInfo.offsetY) * scale
      const originWidth = this.cropWidth * scale
      const originHeight = this.cropHeight * scale

      const ctx = uni.createCanvasContext('cropCanvas', this)
      ctx.drawImage(
        this.url,
        originX,
        originY,
        originWidth,
        originHeight,
        0,
        0,
        originWidth,
        originHeight
      )

      ctx.draw(false, () => {
        uni.canvasToTempFilePath(
          {
            x: 0, //设置图片x轴起始点
            y: 0, //设置图片y轴起始点
            width: originWidth,
            height: originHeight,
            canvasId: 'cropCanvas',
            success: res => {
              this.$emit('success', res.tempFilePath)

              // wx.previewImage({
              //   current: res.tempFilePath,
              //   urls: [res.tempFilePath]
              // })
            },
            fail(err) {
              console.log(err)
            }
          },
          this
        )
      })
    }
  }
}
</script>
<style lang="scss">
.container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  .image {
    height: calc(100% - 160px);
  }
}

.image {
  width: 100%;
  height: 100%;
}

.movable-area {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none; /* 允许穿透点击 */
  /* background: rgba(0, 0, 0, 0.5); */

  background: rgba(0, 0, 0, 0.5); /* 半透明背景 */

  /* 遮罩层配置 */
  -webkit-mask:
    /* 全屏黑色遮罩（保留区域） */ linear-gradient(black, black),
    /* 中间白色矩形（排除区域） */ linear-gradient(white, white);
  -webkit-mask-composite: xor;
  mask-composite: exclude;

  -webkit-mask-repeat: no-repeat;

  pointer-events: none; /* 允许穿透点击 */
}

.crop-box {
  border: 2px solid #fff;
  pointer-events: all; /* 启用内部事件 */
  position: absolute;
}

.drag-point {
  position: absolute;
  width: 18px;
  height: 18px;
  // background: #fff;
  // border: 2px solid #007aff;
  // border-radius: 50%;
  pointer-events: all;
  z-index: 1;
  background-image: url(http://xy-kaola.oss-cn-beijing.aliyuncs.com/public/1b67174729304187626442_Subtract%402x.png);
  background-size: 18px 18px;
}

.drag-point-tl {
  left: -6px;
  top: -6px;
}
.drag-point-tr {
  right: -6px;
  top: -6px;
  transform: rotateZ(90deg);
}
.drag-point-bl {
  left: -6px;
  bottom: -6px;
  transform: rotateZ(-90deg);
}
.drag-point-br {
  right: -6px;
  bottom: -6px;
  transform: rotateZ(-180deg);
}

.button-group {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 160px;
  background: #000000;
  display: flex;
  align-items: center;
  padding: 0 32px;
  justify-content: space-between;
  z-index: 1;
  .tips {
    font-weight: 400;
    font-size: 12px;
    color: #ffffff;
    width: 100%;
    text-align: center;
    position: absolute;
    top: -60px;
    left: 0px;
  }
  .but {
    font-weight: 400;
    font-size: 16px;
    color: #ffffff;
  }
  .but2 {
    border-radius: 50%;
    border: 3px solid #fff;
    display: flex;
    .b {
      background-color: #eaff00;
      width: 49px;
      height: 49px;
      border-radius: 50%;
      border: 2px solid #000000;
      display: flex;
      align-items: center;
      justify-content: center;

      .img {
        width: 25px;
        height: 25px;
      }
    }
  }
}
</style>
