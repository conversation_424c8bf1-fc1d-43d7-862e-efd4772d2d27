<template>
  <view class="page-account">
    <navbar class="navbar" title="我的账户"></navbar>
    <view class="head">
      <view class="info">
        <view class="name">账户余额</view>
        <view class="num">{{ userinfo2.balance_amount || 0 }}</view>
        <view class="but">
          <view @click="show = true">账户提现</view>
          <view
            @click="
              $xh.push('kaolaStudent', `pages/my/account/withdrawalDetails`)
            "
            >确认收款
          </view>
          <view style="margin-left: auto" @click="details">账单明细</view>
        </view>
      </view>
    </view>
    <view class="body">
      <image
        class="body-top"
        src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/2041174425494458285101_Rectangle%20163%402x.png"
      ></image>
      <view class="box-head">
        <view>余额充值</view>
        <image
          src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/67f6174425392200873066_Vector%204%402x.png"
        ></image>
      </view>

      <view class="price-box">
        <view class="list">
          <view
            class="item"
            :class="{
              active: item === priceIndex
            }"
            v-for="item of priceTabList"
            @click="priceIndex = item"
          >
            <view>¥ {{ item }}</view>
            <image
              src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/248c174425400403642691_Frame%402x.png"
            ></image>
          </view>
        </view>
        <view class="price-input">
          <view class="icon">¥</view>
          <input
            class="input"
            v-model="amount"
            inputmode="digit"
            type="digit"
            confirm-type="done"
            auto-height
            @focus="priceIndex = ''"
            @confirm="confirm"
            placeholder-style="placeholderStyle"
            adjust-keyboard-to="bottom"
            placeholder="自定义金额"
          />
        </view>
      </view>

      <view class="box-head">
        <view>充值说明</view>
        <image
          src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/67f6174425392200873066_Vector%204%402x.png"
        ></image>
      </view>
      <view>
        <view class="tips-row">
          <view></view>
          <view>本次充值仅限于在平台上消费，无法跨站点使用</view>
        </view>
        <view class="tips-row">
          <view></view>
          <view>若遇到充值未到账，请联系客服</view>
        </view>
      </view>
      <view class="pay-but" @click="pay">充值</view>
    </view>

    <view class="open" v-show="show">
      <view class="open-body">
        <image
          @click="show = false"
          class="close"
          src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/491917442653610152598_Frame%402x%20(1).png"
        ></image>
        <image
          class="title"
          src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/436c174426527055797503_%E8%B4%A6%E6%88%B7%E6%8F%90%E7%8E%B0%402x.png"
        ></image>
        <view class="price-input">
          <view class="icon">¥</view>
          <input
            class="input"
            v-model="withdrawalamount"
            inputmode="digit"
            type="digit"
            confirm-type="done"
            auto-height
            @confirm="confirm"
            @blur="withdrawalBlur"
            placeholder-style="placeholderStyle"
            adjust-keyboard-to="bottom"
            placeholder="输入提现金额"
          />
        </view>
        <view class="tips">
          <text>当前余额 {{ userinfo2.balance_amount || 0 }}元，</text>
          <text
            v-if="false"
            @click="withdrawalamount = userinfo2.balance_amount"
            >全部提现
          </text>
        </view>
        <view class="pay-but2" @click="withdrawalPay">确定</view>
      </view>
    </view>
  </view>
</template>

<script>
import navbar from '../../../components/commen/navbar.vue'
import { account, student, config } from '../../../api/index.js'
import { app_id } from '../../../config'
export default {
  components: {
    navbar
  },
  data() {
    return {
      amount: '',
      withdrawalamount: '',
      priceIndex: '',
      priceTabList: [100, 200, 300, 500, 600, 1000],
      show: false,
      goods_id: ''
    }
  },
  onLoad() {
    this.goods()
  },
  onShow() {
    student.detailv2()
  },
  computed: {
    userinfo2() {
      return this.$store.state.kaolaStudent.userinfo2
    }
  },
  methods: {
    goods() {
      account
        .goods({
          type: 6,
          is_order_auth: 1,
          page: 1,
          size: 10
        })
        .then(res => {
          if (res?.data?.list) {
            let data = res.data.list[0]
            this.goods_id = data.id
          }
        })

      config.base
        .ykmap({
          code: 'recharge_amount'
        })
        .then(res => {
          this.priceTabList = res.data.list.map(item => Number(item.name))
        })
    },
    topup() {
      // partner_type
      // 教师：4 学员：5
      // partner_id
      // 教师: 员工ID 学员：学员ID
      // dept_id
      // 空
      account.topup({
        partner_type: 5,
        topup_type: 1,
        partner_id: this.$store.state.kaolaStudent.userinfo.student_id,
        dept_id: '',
        goods_id: this.goods_id,
        amount: '',
        remark: ''
      })
    },
    confirm() {},
    details(item) {
      this.$xh.push('kaolaStudent', `pages/my/account/details?id=${8888}`)
    },
    withdrawalBlur() {
      let withdrawalamount = 0
      if (this.withdrawalamount) {
        if (Number(this.withdrawalamount) >= 1) {
          withdrawalamount =
            Math.floor(Number(this.withdrawalamount) * 100) / 100
          this.withdrawalamount = withdrawalamount
        } else {
          this.withdrawalamount = ''
        }
      }
      if (withdrawalamount > 200) {
        this.withdrawalamount = 200
      }
      if (withdrawalamount > this.userinfo2.balance_amount) {
        this.withdrawalamount = this.userinfo2.balance_amount
      }
    },
    withdrawalPay() {
      let withdrawalamount = 0
      if (this.withdrawalamount) {
        if (Number(this.withdrawalamount) >= 1) {
          withdrawalamount =
            Math.floor(Number(this.withdrawalamount) * 100) / 100
        } else {
          this.$xh.Toast('输入提现金额有误')
          return
        }
      } else {
        this.$xh.Toast('输入提现金额')
        return
      }
      if (withdrawalamount > this.userinfo2.balance_amount) {
        this.$xh.Toast('提现金额不能大于账户余额')
        // this.withdrawalamount = this.userinfo2.balance_amount
        return
      }

      account.withdraw
        .withdraw({
          app_id: app_id,
          open_id: uni.getStorageSync('__xingyun_weixinInfo__').openid,
          username: this.userinfo2.name,
          partner_type: 5,
          partner_id: this.$store.state.kaolaStudent.userinfo.student_id,
          amount: String(withdrawalamount),
          remark: '提现'
        })
        .then(res => {
          this.show = false
          this.$xh.push(
            'kaolaStudent',
            `pages/my/account/withdrawal?id=${res.data.apply_id}`
          )
        })
    },
    pay(item) {
      let amount = 0
      if (this.amount) {
        if (Number(this.amount) >= 0.01) {
          amount = Math.floor(Number(this.amount) * 100) / 100
        } else {
          this.$xh.Toast('输入充值金额有误')
          this.amount = ''
          return
        }
      } else if (this.priceIndex !== '') {
        amount = Number(this.priceIndex)
      } else {
        this.$xh.Toast('请选择或输入充值金额')
        return
      }
      //       partner_type
      // 教师：4 学员：5
      // partner_id
      // 教师: 员工ID 学员：学员ID
      // dept_id
      // 空
      account
        .topup({
          partner_type: 5,
          topup_type: 1,
          partner_id: this.$store.state.kaolaStudent.userinfo.student_id,
          dept_id: '',
          goods_id: this.goods_id,
          amount: amount,
          remark: ''
        })
        .then(res => {
          this.amount = ''
          this.wechatapplet(res.data, amount)
        })
      // this.$xh.push('kaolaStudent', `pages/my/account/success?id=${8888}`)
    },

    wechatapplet(obj, amount) {
      account
        .payParams({
          flow_id: obj.flow_id,
          order_id: obj.order_id,
          goods_ids: this.goods_id
        })
        .then(res => {
          const payParams = {
            appId: app_id,
            timeStamp: res.data.time_stamp,
            nonceStr: res.data.nonce_str,
            signType: res.data.sign_type,
            paySign: res.data.pay_sign,
            package: res.data.package,
            finance_body_id: res.data.finance_body_id
          }

          this.$xh.pay(
            payParams,
            () => {
              student.detailv2()
              this.$xh.push(
                'kaolaStudent',
                `pages/my/account/success` + `?amount=${amount}`
              )
            },
            () => {
              this.$xh.Toast('支付失败！')
            }
          )
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.open {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1;
  background: rgba(0, 0, 0, 0.5);
  .open-body {
    position: absolute;
    left: 0;
    bottom: 0;
    height: 296px;
    width: 100%;
    padding: 24px 12px 0 12px;
    display: flex;
    flex-direction: column;
    background-size: 100% 100%;
    background-image: url(https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/42ab174426520756971483_openbackdddd.png);
    .title {
      margin-bottom: 24px;
      width: 118px;
      height: 24px;
    }
    .close {
      position: absolute;
      z-index: 1;
      top: 0;
      right: 0;
      width: 24px;
      height: 24px;
      border: 12px solid rgba(0, 0, 0, 0);
    }
    .pay-but2 {
      // position: absolute;
      // bottom: 40px;
      // left: 50%;
      // transform: translateX(-50%);
      // z-index: 1;
      margin: 52px auto 40px auto;
      width: 231px;
      text-align: center;
      height: 44px;
      line-height: 44px;
      background: #222333;
      border-radius: 33px 33px 33px 33px;
      font-weight: bold;
      font-size: 14px;
      color: #ffffff;
    }
    .tips {
      margin-top: 12px;
      margin-left: 8px;
      text {
        font-weight: 400;
        font-size: 12px;
        color: rgba(34, 35, 51, 0.65);
      }
      text:nth-child(2) {
        font-weight: 400;
        font-size: 12px;
        color: #222333;
      }
    }
    .price-input {
      width: 100%;
      height: 52px;
      background: #ffffff;
      border-radius: 12px;
      display: flex;
      align-items: center;
      // margin-bottom: 24px;
      .icon {
        font-weight: bold;
        font-size: 16px;
        color: rgba(34, 35, 51, 0.85);
        margin-left: 16px;
        margin-right: 8px;
        flex-shrink: 0;
      }
      .input {
        flex: 1;
      }
      .placeholderStyle {
        font-weight: 500;
        font-size: 16px;
        color: rgba(34, 35, 51, 0.35);
      }
    }
  }
}
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
}
.price-box {
  width: calc(100% + 12px);
  .price-input {
    width: calc(100% - 12px);
    height: 52px;
    background: #f7f9fb;
    border-radius: 8px 8px 8px 8px;
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    .icon {
      font-weight: bold;
      font-size: 16px;
      color: rgba(34, 35, 51, 0.85);
      margin-left: 28px;
      margin-right: 6px;
      flex-shrink: 0;
    }
    .input {
      flex: 1;
    }
    .placeholderStyle {
      font-weight: 500;
      font-size: 16px;
      color: rgba(34, 35, 51, 0.35);
    }
  }
  .list {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    .item {
      max-width: calc((100% - 36px) / 3);
      width: 109px;
      height: 52px;
      background: #f7f9fb;
      border-radius: 8px 8px 8px 8px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      margin-bottom: 12px;
      view {
        font-weight: bold;
        font-size: 16px;
        color: rgba(34, 35, 51, 0.85);
      }
      image {
        display: none;
      }
    }
    .active {
      border: 1px solid #000000;
      image {
        display: block;
        position: absolute;
        right: 0;
        bottom: 0;
        width: 18px;
        height: 18px;
      }
    }
  }
}
.box-head {
  position: relative;
  margin-bottom: 16px;
  view {
    font-weight: 500;
    font-size: 16px;
    color: #222333;
    line-height: 20px;
    position: relative;
    z-index: 1;
  }
  image {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 58px;
    height: 7px;
  }
}
.page-account {
  background-size: 100% 812px;
  background: linear-gradient(180deg, #d4ff00 0%, #ffffff 100%);
  background-color: #fff;
  height: 100vh;
  overflow-y: auto;
  .head {
    padding: 98px 12px 0 12px;

    .info {
      background-size: 100% 100%;
      background-image: url(https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/8dca174425396121463929_Mask%20group%402x.png);
      padding: 16px 16px 30px 16px;
      .name {
        font-weight: bold;
        font-size: 16px;
        color: rgba(34, 35, 51, 0.85);
      }
      .num {
        margin-top: 8px;
        margin-bottom: 16px;
        font-weight: 800;
        font-size: 28px;
        color: #222333;
        line-height: 38px;
      }
      .but {
        display: flex;
        view {
          margin-right: 8px;
          text-align: center;
          width: 64px;
          height: 24px;
          line-height: 24px;
          background: rgba(255, 255, 255, 0.5);
          border-radius: 10px 10px 10px 10px;
          border: 1px solid #ffffff;
          font-weight: 400;
          font-size: 12px;
          color: rgba(34, 35, 51, 0.85);
        }
      }
    }
  }
  .pay-but {
    position: fixed;
    bottom: 40px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
    width: 231px;
    text-align: center;
    height: 44px;
    line-height: 44px;
    background: #222333;
    border-radius: 33px 33px 33px 33px;
    font-weight: bold;
    font-size: 14px;
    color: #ffffff;
  }
  .body {
    min-height: calc(100vh - 251px);
    padding: 0 12px 16px 12px;
    // border-radius: 8px;
    // overflow-x: hidden;
    background-color: #fff;
    position: relative;
    // top: -10px;
    z-index: 1;
    .body-top {
      position: absolute;
      width: 100vw;
      height: 16px;
      top: -16px;
      left: 0;
      z-index: 10;
    }
    .tips-row {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      view:nth-child(1) {
        margin-left: 4px;
        margin-right: 12px;
        width: 4px;
        height: 4px;
        background: #332222;
        border-radius: 2px;
      }
      view:nth-child(2) {
        font-weight: 400;
        font-size: 12px;
        color: rgba(34, 35, 51, 0.65);
        line-height: 14px;
      }
    }
  }
}
</style>
