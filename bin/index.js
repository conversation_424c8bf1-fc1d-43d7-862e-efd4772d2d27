const fs = require('fs')
const path = require('path')
const run_name = process.argv[2]
const file_name = `./project/${run_name}.json`
const main_name = `../src/modules/${run_name}/main.js`
let mian_path = './src/main.js'
const exec = require('child_process').exec // 创建子进程处理命令
function readFile(url) {
  return new Promise((resolve, reject) => {
    fs.readFile(url, 'utf-8', (err, data) => {
      if (err) {
        reject(err)
      } else {
        resolve(data)
      }
    })
  })
}
function writeFile(path, content) {
  return new Promise((resolve, reject) => {
    fs.writeFile(path, content, err => {
      if (!err) {
        resolve()
      } else {
        reject(err)
      }
    })
  })
}
function compileRenderMain() {
  return new Promise(async (resolve, reject) => {
    if (!run_name) {
      return reject('不存在的项目！')
    }
    // 编译入口文件 读取 -> 写入
    try {
      let main_path = path.join(__dirname, main_name)
      let main_content = await readFile(main_path)
      await writeFile(mian_path, main_content)
      // 写入
      resolve()
    } catch (error) {
      reject(error)
      console.log(error)
    }
  })
}
function compile(options = {}) {
  return new Promise(async (resolve, reject) => {
    if (!run_name) {
      return reject('不存在的项目！')
    }
    let curPorjectInfo = await readFile(path.join(__dirname, file_name))
    let miniProgramNames = [
      'quickapp',
      'mp-weixin',
      'mp-alipay',
      'mp-baidu',
      'mp-toutiao',
      'mp-qq'
    ]
    // 编译路由文件
    try {
      const manifestPath = path.join(__dirname, '../src/manifest.json')
      const pagesjsonPath = path.join(__dirname, '../src/pages.json')
      let projectInfo = JSON.parse(curPorjectInfo)
      let uni_manifest = await readFile(manifestPath)
      const _uni_manifest = JSON.parse(uni_manifest)
      _uni_manifest.appid = projectInfo.appid
      // 默认给到小程序appid 如果配置会覆盖
      _uni_manifest['mp-weixin'].appid = projectInfo.appid
      // 微信小程序是否添加隐私协议，json配置了此字段微信会强制校验用户是否同意了隐私协议，拒绝将不能获取到对应api的回调
      _uni_manifest['mp-weixin'].__usePrivacyCheck__ = projectInfo[
        '__usePrivacyCheck__'
      ]
        ? true
        : false
      // 各大小程序特殊配置处理
      miniProgramNames.forEach(miniName => {
        if (projectInfo[miniName]) {
          _uni_manifest[miniName] = {
            ..._uni_manifest[miniName],
            ...projectInfo[miniName]
          }
        }
      })
      _uni_manifest.name = projectInfo.name
      _uni_manifest.description = projectInfo.description
      _uni_manifest.versionName = projectInfo.versionName

      // 数组中的项目路由，将被编译成history模式
      let routerHistoryArr = ['xingyunapp_gzh']
      if (routerHistoryArr.includes(run_name)) {
        _uni_manifest.h5.router.mode = 'history'
      } else {
        _uni_manifest.h5.router.mode = 'hash'
      }

      const pages_json = projectInfo['pages.json']
      pages_json.pages = pages_json.pages.map(item => {
        return {
          ...item,
          path: `modules/${run_name}/${item.path}`
        }
      })
      if (
        pages_json.tabBar &&
        pages_json.tabBar.list &&
        pages_json.tabBar.list.length
      ) {
        pages_json.tabBar.list = pages_json.tabBar.list.map(item => {
          if (item.pagePath) {
            item.pagePath = `modules/${run_name}/${item.pagePath}`
          }
          // if (item.iconPath) {
          //   item.iconPath = `modules/${run_name}/${item.iconPath}`
          // }
          // if (item.selectedIconPath) {
          //   item.selectedIconPath = `modules/${run_name}/${item.selectedIconPath}`
          // }
          return item
        })
      }
      await writeFile(manifestPath, JSON.stringify(_uni_manifest, null, 2))
      await writeFile(pagesjsonPath, JSON.stringify(pages_json, null, 2))
      // 写入
      resolve()
    } catch (error) {
      reject(error)
    }
    // 编译入口文件 读取 -> 写入
    try {
      // main_name
      let main_path = path.join(__dirname, main_name)
      let main_content = await readFile(main_path)
      await writeFile(mian_path, main_content)
    } catch (error) {
      console.log(error)
    }
  })
}

// 根据run_name名称，读取bin/config目录下相应项目下的配置文件，并依次写入根目录下，替换原有env配置文件
function compileEnv() {
  return new Promise(async (resolve, reject) => {
    if (!run_name) {
      return reject('不存在的项目！')
    }
    let files = []
    try {
      files = fs.readdirSync(path.join(__dirname, './env' + `/${run_name}`))
    } catch (err) {
      // console.log('该项目没有独立配置')
    }
    if (files.length) {
      files.forEach(async function (item, index) {
        let env_content = await readFile(
          path.join(__dirname, `./env/${run_name}/${item}`)
        )
        await writeFile(path.join(__dirname, `../${item}`), env_content)
      })
    }
    resolve()
  })
}

compileEnv()
  .then(() => {
    console.log('项目配置文件编译已启动！')
  })
  .catch(err => {
    console.log(err)
    process.exit()
  })

compileRenderMain()
  .then(() => {
    console.log('入口文件编译已启动！')
  })
  .catch(err => {
    console.log(err)
    process.exit()
  })
compile()
  .then(() => {
    console.log('编译已启动！')
  })
  .catch(err => {
    console.log(err)
    process.exit()
  })
fs.watchFile(
  path.join(__dirname, file_name),
  { interval: 20 },
  (curr, prev) => {
    if (curr.mtime !== prev.mtime) {
      compile()
        .then(() => {
          console.log(`${run_name}.json文件更新成功！`)
        })
        .catch(err => {
          console.log(err)
        })
    }
  }
)
fs.watchFile(
  path.join(__dirname, main_name),
  { interval: 20 },
  (curr, prev) => {
    if (curr.mtime !== prev.mtime) {
      compileRenderMain()
        .then(() => {
          console.log('入口文件已更新！')
        })
        .catch(err => {
          console.log(err)
        })
    }
  }
)
