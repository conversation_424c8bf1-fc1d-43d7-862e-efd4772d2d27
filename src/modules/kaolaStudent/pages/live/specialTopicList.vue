<template>
  <view class="course-detail back">
    <u-navbar
      title="专题"
      bgColor="rgba(255,255,255,0)"
      @rightClick="rightClick"
      :autoBack="true"
    ></u-navbar>

    <view class="nav-block"></view>
    <view class="page-content">
      <view class="course-intro">
        <view class="img-wrap">
          <image
            class="main-image"
            :src="$xh.completepath(query.image)"
            mode="aspectFill"
          ></image>
        </view>
        <view class="course-info-top">
          <view class="info-title">{{ chaperInfo.name }}</view>

          <!-- <view class="teacher-wrap">
          <u-avatar :src="avatarUrl"></u-avatar>
          <text class="teacher-name">刘老师</text>
          <text>清华大学</text>
        </view> -->
          <view class="course-content">
            <view class="course-content-title">内容说明:</view>
            <view class="course-content-text">
              <text class="u-line-3 line-height-text">
                {{ chaperInfo.description }}
              </text>
            </view>
          </view>
        </view>
      </view>
      <view class="page-title">课次内容</view>
      <scroll-view :scroll-top="scrollTop" scroll-y="true" class="course-list">
        <view
          class="course-item"
          v-for="item in chaperList"
          :key="item.id"
          @click="goCoursePlay(item)"
        >
          <view class="left-img">
            <image
              mode="aspectFill"
              class="course-image"
              :src="customComplete(item.cover_image_url)"
            ></image>
          </view>
          <view class="right">
            <view class="course-title">{{ item.name }}</view>
            <view class="course-info">
              {{ item.description }}
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <u-modal :show="showTip" title=" " content="" :showConfirmButton="false">
      <view class="slot-content">
        <view class="tip-text">
          <text class="text">升级会员</text>
          <image
            class="bg-image"
            src="../../../../static/imgs/kaolaStudent/tip-title-bg.png"
          ></image>
        </view>
        <view class="tip-content">当前视频，需要成为会员，才可查看</view>
        <view class="update-btn" @click="goVipPage">升级会员</view>
        <view class="text-button" @click="showTip = false">我再想想</view>
      </view>
    </u-modal>
  </view>
</template>
<script>
import { square, teaching } from '../../api'

export default {
  name: 'courseDetail',
  data() {
    return {
      scrollTop: 0,
      chaperInfo: {},
      query: {
        title: '',
        image: '',
        id: '',
        type: ''
      },
      showTip: false,
      chaperList: [],
      avatarUrl:
        'https://xy-kaola.oss-cn-beijing.aliyuncs.com/merchantid_385164383640140890/389806835466550453/resumes/1757125666/583169428993085165.noRMX8htnQK76a9f4109dd80c4a8210d740a1e773482.png'
    }
  },
  onLoad(e) {
    this.query.title = e.title
    this.query.id = e.id
    this.query.type = e.type
    this.query.image = e.image
    this.getData()
  },
  onShow() {
    console.log('onShow - 直播详情')
    if (this.showTip) {
      this.showTip = false
    }
  },
  methods: {
    customComplete(path) {
      if (path && path.startsWith('http')) {
        return path
      } else if (path) {
        return this.$xh.completepath(path)
      } else {
        return ''
      }
    },
    goCoursePlay(item) {
      console.log('this。query.type', this.query.type)
      if (this.query.type == 3) {
        const params = { system_id: item.id }

        square.getCallbackDetail(params).then(res => {
          console.log('录播详情： ', res)
          if (Number(res.data.student_vip_level) < Number(res.data.vip_level)) {
            this.showTip = true
          } else {
            this.$xh.push(
              'kaolaStudent',
              'pages/live/callbackPlay?system_id=' + item.id
            )
          }
        })
      } else {
        this.$xh.push(
          'kaolaStudent',
          'pages/live/liveDetail?system_id=' + item.id
        )
      }
    },

    goVipPage() {
      this.$xh.push('kaolaStudent', 'pages/my/vip/index')
    },
    getData() {
      teaching
        .getTeachingSystemTree({
          id: this.query.id,
          is_use_common_cache: false,
          noloading: true
        })
        .then(res => {
          console.log('章节列表', res)
          if (res.data && res.data.length > 0) {
            this.chaperInfo = res.data[0]
            this.chaperList = res.data[0].subs
          }
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.course-detail {
  height: 100vh;
  padding: 32rpx 0;

  .page-content {
    padding: 32px 32rpx 0;
    background-color: #fff;
  }

  &.back {
    background-image: url('https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/626f174562773210963083_Group%20272%402x.png');

    background-repeat: no-repeat;
    background-position: 12.92% -7.5%;
  }

  .nav-block {
    height: 142rpx;
  }

  .line-height-text {
    line-height: 1.4;
  }

  .slot-content {
    width: 624rpx;
    height: 440rpx;
    border-radius: 32rpx;
    padding: 64rpx;

    .tip-text {
      width: 162rpx;
      height: 64rpx;
      margin: 0 auto 0;
      text-align: center;
      font-weight: 700;
      font-size: 40rpx;
      letter-spacing: 0%;
      color: #222333;
      position: relative;

      .text {
        position: absolute;
        top: 0;
        width: 100%;
        text-align: center;
        height: 32rpx;
        left: 0;
        z-index: 2;
      }

      .bg-image {
        position: absolute;
        z-index: 2;
        bottom: 0;
        left: 0;
        height: 20rpx;
        width: 100%;
      }
    }

    .tip-content {
      font-weight: 400;
      font-size: 28rpx;
      color: #222333db;
      padding-top: 32rpx;
    }

    .update-btn {
      width: 496rpx;
      height: 88rpx;
      line-height: 88rpx;
      border-radius: 100rpx;
      background: #2d2a40;
      color: #fff;
      text-align: center;
      margin-top: 64rpx;
    }

    .text-button {
      font-weight: 400;
      font-size: 28rpx;
      color: #222333db;
      padding-top: 16rpx;
      text-align: center;
      margin: 0 auto;
    }
  }

  .course-intro {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .img-wrap {
      width: 240rpx;
      border-radius: 32rpx;
    }

    .main-image {
      width: 240rpx;
      height: 320rpx;
      background-color: #666;
      border-radius: 32rpx;
      object-fit: cover;
      object-position: center;
    }

    .course-info-top {
      padding-left: 32rpx;
    }
  }

  .teacher-wrap {
    padding-top: 32rpx;
    font-size: 28rpx;
    color: #999;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .teacher-name {
      padding-right: 10rpx;
      font-size: 28rpx;
      font-weight: 400;
      color: #000;
      margin-left: 8rpx;
    }
  }

  .course-content {
    font-size: 26rpx;
    color: #666;
    padding-top: 20rpx;
    font-weight: 400;

    .course-content-title {
      padding-bottom: 13rpx;
    }

    .course-content-text {
      white-space: pre-line;
      height: 110rpx;
    }
  }

  .info-title {
    font-size: 34rpx;
    color: #333;
    font-weight: 600;
  }

  .page-title {
    font-family: 'SourceHanSansCN-VF';
    font-weight: 500;
    font-size: 32rpx;
    height: 80rxp;
    line-height: 80rpx;
    letter-spacing: 5%;
    color: #000;
    // padding: 16rpx 0;
  }

  .course-list {
    height: calc(100vh - 720rpx);
  }

  .course-item {
    border-radius: 32rpx;
    margin-bottom: 32rpx;
    background-color: #f8f8f8;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .left-img {
      width: 208rpx;
      margin-right: 32rpx;
      background-color: #ddf3ff;
      border-radius: 32rpx;
    }

    .course-image {
      width: 208rpx;
      height: 208rpx;
      border-radius: 32rpx;
      object-fit: cover;
      object-position: center;
    }

    .right {
      .course-title {
        font-size: 34rpx;
        line-height: 36rpx;
        color: #000;
        padding-bottom: 25rpx;
      }

      .course-info {
        font-size: 24rpx;
        font-weight: 400;
        color: #666;
        letter-spacing: 3%;
        white-space: pre-line;
        line-height: 34rpx;
      }
    }
  }
}
</style>
