<template>
  <view class="protocol-container">
    <view class="protocol-header">
      <!-- 返回按钮 -->
      <uni-icons
        type="left"
        size="24"
        class="back-icon"
        @tap="goBack"
      ></uni-icons>
      <text class="protocol-title">考好啦会员服务协议（VIP 补充协议）</text>
      <text class="protocol-date">更新日期：2025年9月22日</text>
      <text class="protocol-date">生效日期：2025年9月22日</text>
    </view>
    <scroll-view class="protocol-content" scroll-y="true">
      <view class="protocol-section">
        <text class="section-content"
          >本《VIP 补充协议》（以下简称"本补充协议"）由用户与
          北京考好啦科技有限公司（以下简称"考好啦"或"本公司"）共同缔结，是对已生效的《考好啦会员服务协议》及相关规则（以下简称"主协议"）的补充和说明。除非本补充协议另有约定，主协议与本补充协议均为用户与考好啦之间具有同等法律效力的约定。本补充协议自上述生效日起对购买或使用VIP相关服务的用户生效。</text
        >
      </view>

      <view class="protocol-section">
        <text class="section-title">一、适用范围</text>
        <text class="section-content"
          >本补充协议适用于以"VIP"或"俱乐部"形式展示的会员等级、VIP专属服务、VIP定价与优惠、以及与VIP相关的特定权利义务（包括但不限于清北俱乐部、985俱乐部、211俱乐部、基础会员等）。</text
        >
      </view>

      <view class="protocol-section">
        <text class="section-title">二、价格与会员权益</text>
        <view class="table-container">
          <view class="table-row">
            <text class="table-cell title-cell">等级</text>
            <text class="table-cell title-cell">年费（元/人/年）</text>
            <text class="table-cell title-cell">主要权益</text>
          </view>
          <view class="table-row">
            <text class="table-cell">基础会员</text>
            <text class="table-cell">365</text>
            <text class="table-cell">直播课、录播课、AI答疑</text>
          </view>
          <view class="table-row">
            <text class="table-cell">211俱乐部会员</text>
            <text class="table-cell">399</text>
            <text class="table-cell">AI答疑、测评、错题本、清北学霸直播课</text>
          </view>
          <view class="table-row">
            <text class="table-cell">985俱乐部会员</text>
            <text class="table-cell">985</text>
            <text class="table-cell"
              >211权益 + 答疑60分钟；答疑、伴学享9折优惠</text
            >
          </view>
          <view class="table-row">
            <text class="table-cell">清北俱乐部会员</text>
            <text class="table-cell">1999</text>
            <text class="table-cell"
              >985权益 + 清北伴学60分钟；答疑、伴学享8折优惠</text
            >
          </view>
        </view>
        <text class="section-content"
          >说明：基础会员现包含AI答疑功能（此次更新已生效）。上述价格为年度订阅价格，用户购买时请以支付页面显示为准。</text
        >
      </view>

      <view class="protocol-section">
        <text class="section-title">三、VIP 专属服务与伴学定价</text>
        <text class="section-content"
          >本公司提供两类真人伴学服务：即时伴学与深度伴学，VIP用户可享受相应折扣优先排期及专属客服通道，具体如下：</text
        >
        <text class="list-item"
          >• 即时伴学（真人一对一，双一流以上师资）：清北 5元/分钟；985
          4元/分钟；双一流 3元/分钟。</text
        >
        <text class="list-item"
          >• 深度伴学（真人一对一，课前规划）：清北 1000元/小时（2小时/次）；985
          500元/小时（2小时/次）；双一流 300元/小时（2小时/次）；首席伴学
          1500元/小时（2小时/次）；金牌伴学参照985。</text
        >
        <text class="list-item"
          >• 一对多折扣：一对二 6折；一对三
          5折；一对六3折（家长自行组班，起报15次）。初中按9折，小学按8折。</text
        >
      </view>

      <view class="protocol-section">
        <text class="section-title">四、VIP 优惠</text>
        <text class="list-item"
          >•
          1V1优惠（不含一对多）：报10次送1次；报20次送3次；报30次送6次。</text
        >
        <text class="list-item"
          >•
          转介绍优惠：老客户转介绍新客户，按新客户消费金额10%返佣；该新客户再转介绍，按消费金额2%返佣；永久效。</text
        >
        <text class="list-item"
          >•
          特殊优惠：种子客户享标准价4折；教师子女参照种子客户标准；教师激励：老师转介绍老师入职，按被介绍老师课时费8%奖励；被介绍老师再介绍新老师，按2%奖励；永久有效。</text
        >
      </view>

      <view class="protocol-section">
        <text class="section-title">五、VIP 权利与使用规则</text>
        <text class="section-content"
          >1. VIP
          用户在会员有效期内享有相应等级的全部权益，考好啦为VIP用户提供优先排课、优先客服支持（视公司服务承载能力）以及部分课程或服务的专属报名通道。</text
        >
        <text class="section-content"
          >2. VIP
          权益仅限会员本人使用，除本公司书面同意外，不得转让、出租或代售。对滥用、共享账号的情况，本公司有权暂停或终止其会员资格并不予退费。</text
        >
        <text class="section-content"
          >3. VIP
          用户在享受AI答疑等智能服务时，应理解该等服务为辅助性质，学习效果受多种因素影响，本公司不作保证承诺。</text
        >
      </view>

      <view class="protocol-section">
        <text class="section-title">六、支付、退款与生效</text>
        <text class="section-content"
          >1.
          价格以支付页面为准，用户支付成功后服务立即生效（或按页面约定时间生效）。</text
        >
        <text class="section-content"
          >2.
          会员服务一经开通，不支持无理由退款。因本公司原因无法履行服务的，按剩余服务时间比例退还或协商补偿。</text
        >
        <text class="section-content"
          >3.
          本补充协议自页面顶部所示生效日期起对新购买以及续费的VIP用户生效；已购买的用户若因本次更新而产生实质性权利变更，本公司将依据法律法规和合理性原则予以单独通知或补偿安排（如有）。</text
        >
      </view>

      <view class="protocol-section">
        <text class="section-title">七、隐私与数据</text>
        <text class="section-content"
          >VIP
          服务过程中产生的学习数据（含测评、答疑记录、陪学记录、作业与错题本等）将按照公司隐私政策进行存储与使用。公司在未取得用户同意的情况下，不会将用户个人隐私用于商业化分发，但为提供服务可能对匿名/聚合数据进行统计分析与产品优化。</text
        >
      </view>

      <view class="protocol-section">
        <text class="section-title">八、协议变更</text>
        <text class="section-content"
          >如因业务调整或法律法规要求，本公司可对本补充协议进行更新。更新后的协议将在本公司官网/App进行公告或通过消息方式通知用户。用户在收到变更通知后继续使用服务，即视为接受修改后的协议。如用户不同意修改，可在变更生效前选择不再续费或与公司客服沟通处理方案。</text
        >
      </view>

      <view class="protocol-section">
        <text class="section-title">九、争议解决与法律适用</text>
        <text class="section-content"
          >本补充协议的订立、执行与解释及争议的解决均适用中华人民共和国法律。双方因本补充协议发生争议的，应首先友好协商解决；协商不成的，任一方可向本公司住所地有管辖权的人民法院提起诉讼。</text
        >
      </view>

      <view class="protocol-section">
        <text class="section-title">十、联系方式</text>
        <text class="section-content">公司名称：北京考好啦科技有限公司</text>
        <text class="section-content">客服邮箱：<EMAIL></text>
        <text class="section-content"
          >本补充协议为
          VIP（俱乐部）相关权益的补充文件，与主协议共同构成用户与考好啦之间完整的会员服务约定。若本补充协议与主协议存在冲突，以本补充协议对
          VIP 权益的特别约定为准。</text
        >
      </view>

      <!-- 协议底部，距离底部有一定距离 -->
      <view class="protocol-footer">
        <text class="footer-text">北京考好啦科技有限公司</text>
        <text class="footer-text">© 2025 考好啦 版权所有</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import uniIcons from '@/components/uni-icons/uni-icons.vue'

export default {
  components: {
    uniIcons
  },
  data() {
    return {}
  },
  methods: {
    goBack() {
      // 返回上一页
      this.$xh.back()
    }
  }
}
</script>

<style lang="scss" scoped>
.protocol-container {
  width: 100%;
  min-height: 100vh;
  background-color: #fff;
  padding: 0 16px; // 增加容器的左右内边距
  box-sizing: border-box; // 确保内边距不会增加元素总宽度
}

.protocol-header {
  padding: 20px 0 16px;
  border-bottom: 1px solid #f0f0f0;
}

.back-icon {
  color: #222333;
  margin-bottom: 12px;
}

.protocol-title {
  display: block;
  font-size: 18px;
  font-weight: 500;
  color: #222333;
  margin-bottom: 8px;
}

.protocol-date {
  display: block;
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.protocol-content {
  padding: 16px 0;
  height: calc(100vh - 150px);
}

.protocol-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #222333;
  margin-bottom: 12px;
  display: block;
}

.section-subtitle {
  font-size: 14px;
  font-weight: 500;
  color: #444;
  margin: 16px 0 8px 0;
  display: block;
}

.section-content {
  font-size: 14px;
  color: #666;
  line-height: 1.8;
  margin-bottom: 8px;
  display: block;
  word-wrap: break-word; // 确保长单词或词组能够换行
}

.table-container {
  margin: 16px 0;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
  width: 100%;
  display: table;
}

.table-row {
  display: table-row;
  border-bottom: 1px solid #f0f0f0;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  display: table-cell;
  padding: 12px 8px;
  font-size: 14px;
  color: #666;
  text-align: center;
  border-right: 1px solid #f0f0f0;
  word-wrap: break-word;
  word-break: break-all;
}

.table-cell:last-child {
  border-right: none;
}

.title-cell {
  background-color: #f9f9f9;
  font-weight: 500;
  color: #222333;
}

.protocol-footer {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
  text-align: center;
}

.footer-text {
  font-size: 12px;
  color: #999;
  display: block;
  margin-bottom: 4px;
}
.list-item {
  display: block;
  margin-bottom: 8px; /* 修改为与section-content一致 */
  font-size: 14px;
  color: #666; /* 修改为与section-content一致 */
  line-height: 1.8; /* 修改为与section-content一致 */
  padding-left: 6px;
  word-wrap: break-word; /* 添加与section-content一致的换行属性 */
}
</style>