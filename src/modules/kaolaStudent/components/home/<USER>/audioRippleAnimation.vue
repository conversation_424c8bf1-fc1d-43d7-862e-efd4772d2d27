<template>
  <view class="audioRippleAnimation">
    <!-- <view class="start">
      <view class="item" v-for="item of 7"></view>
    </view> -->
    <view class="rippleList" v-for="ee of 3">
      <view class="item" v-for="item of 7"></view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {}
  }
}
</script>

<style lang="scss" scoped>
.audioRippleAnimation {
  display: flex;
  align-items: center;
}
.start {
  display: flex;
  align-items: center;
  .item {
    width: 3px;
    height: 4px;
    margin: 0 3rpx;
    // background-color: #fff;
    flex-shrink: 0;
    animation: audio-wave-start 1s ease-in-out infinite;
    &:nth-child(1) {
      animation-delay: 0s;
    }

    &:nth-child(2) {
      animation-delay: 0.2s;
    }

    &:nth-child(3) {
      animation-delay: 0.4s;
    }
    &:nth-child(4) {
      animation-delay: 0.5s;
    }

    &:nth-child(5) {
      animation-delay: 0.7s;
    }

    &:nth-child(6) {
      animation-delay: 0.9s;
    }
    &:nth-child(7) {
      animation-delay: 1s;
    }
  }
  @keyframes audio-wave-start {
    0% {
      background-color: rgba($color: #fff, $alpha: 0.2);
    }
    50% {
      background-color: rgba($color: #fff, $alpha: 1);
    }
    100% {
      background-color: rgba($color: #fff, $alpha: 0.2);
    }
  }
}
.rippleList {
  display: flex;
  align-items: center;
  .item {
    width: 3px;
    height: 4px;
    margin: 0 3rpx;
    background-color: #fff;
    flex-shrink: 0;
    animation: audio-wave 1s ease-in-out infinite;
    &:nth-child(1) {
      animation-delay: 0s;
    }

    &:nth-child(2) {
      animation-delay: 0.2s;
    }

    &:nth-child(3) {
      animation-delay: 0.4s;
    }
    &:nth-child(4) {
      animation-delay: 0.5s;
    }

    &:nth-child(5) {
      animation-delay: 0.4s;
    }

    &:nth-child(6) {
      animation-delay: 0.2s;
    }
    &:nth-child(7) {
      animation-delay: 0s;
    }
  }
  @keyframes audio-wave {
    0% {
      height: 4px;
    }
    50% {
      height: 16px;
    }
    100% {
      height: 4px;
    }
  }
}
</style>
