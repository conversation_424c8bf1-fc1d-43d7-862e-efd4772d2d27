<template>
  <scroll-view
    class="scroll"
    scroll-y
    :lower-threshold="50"
    :refresher-enabled="true"
    :refresher-triggered.async="triggered"
    @refresherrefresh="refresh"
    @scrolltolower="scrolltolower"
  >
    <slot></slot>
  </scroll-view>
</template>

<script>
let canClick = true
export default {
  props: {
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      triggered: true
    }
  },
  watch: {
    loading() {
      if (!this.loading) {
        this.triggered = false
        uni.hideLoading()
      }
    }
  },
  methods: {
    scrolltolower() {
      if (canClick) {
        this.$emit('nextPage')
        uni.showLoading({
          title: '加载中...'
        })
        canClick = false
        setTimeout(() => {
          canClick = true
          uni.hideLoading()
        }, 1000)
      }
    },
    refresh() {
      this.triggered = true
      this.$emit('refresh')
    }
  }
}
</script>

<style lang="scss" scoped>
.scroll {
  height: 100%;
  width: 100%;
}
</style>
