<template>
  <view class="page-content">
    <view class="search">
      <selectStageGrade
        v-model="search.stage_grade"
        :name.sync="search.stage_grade_name"
        @change="refresh"
        allName="年级"
      >
        <view class="search-item">
          <view>{{ search.stage_grade_name[1] || '年级' }}</view>
          <image
            class="img3"
            src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/6ace174418173457834915_Frame%2012%402x.png"
          ></image>
        </view>
      </selectStageGrade>
      <pickerSelectCode
        code="subject"
        v-model="search.subject_id"
        :name.sync="search.subject_id_name"
        @change="refresh"
        allName="全部"
      >
        <view class="search-item">
          <view>{{ search.subject_id_name || '学科' }}</view>
          <image
            class="img3"
            src="static/imgs/kaolaStudent/arrow_down.png"
            lazy-load="true"
          ></image>
        </view>
      </pickerSelectCode>
      <pickerSelect
        code="subject"
        v-model="search.sex"
        :name.sync="search.sex_name"
        @change="refresh"
        :options="[
          {
            name: '全部',
            id: ''
          },
          {
            name: '男',
            id: '1'
          },
          {
            name: '女',
            id: '2'
          }
        ]"
      >
        <view class="search-item">
          <view>{{ search.sex_name || '性别' }}</view>
          <image
            class="img3"
            src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/6ace174418173457834915_Frame%2012%402x.png"
          ></image>
        </view>
      </pickerSelect>

      <pickerSelectCode
        v-model="search.teach_style"
        :name.sync="search.teach_style_name"
        @change="refresh"
        code="teach_style"
        allName="全部"
      >
        <view class="search-item">
          <view>{{ search.teach_style_name || '教学风格' }}</view>
          <image
            class="img3"
            src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/6ace174418173457834915_Frame%2012%402x.png"
          ></image>
        </view>
      </pickerSelectCode>
    </view>
    <pageList
      class="scrollpageList"
      :key="list.length"
      :loading="search.loading"
      @nextPage="getList"
      @refresh="refresh"
    >
      <view class="list" :key="list.length">
        <noData v-if="list.length == 0"></noData>
        <view class="item" v-for="item of list">
          <view class="teacher_grade_name" v-if="item.teacher_grade_name">
            <image
              class="grade"
              :src="$xh.teacher_grade_img(item.teacher_grade_name)"
            ></image>
          </view>

          <view class="top">
            <view class="info">
              <image
                class="avatar"
                :src="
                  $xh.completepath(
                    item.teacher_avatar ||
                      'public/5ef917440086508568967_laoshitouxiang.png'
                  )
                "
              ></image>
              <view class="name">{{ item.teacher_name }}</view>
              <image
                v-if="item.sex == 1"
                class="sex"
                src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/3ab5174563172190027036_Group%20257%402x.png"
              ></image>
              <image
                v-if="item.sex == 2"
                class="sex"
                src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/8b74174563170016061969_Group%20257%402x(1).png"
              ></image>

              <view class="school-label-container">
                <image
                  class="label"
                  :src="$xh.completepath(item.school_img)"
                  @error="handleImageError(item)"
                  :style="{ display: item.showSchoolName ? 'none' : 'block' }"
                ></image>
                <view
                  class="school-name-text"
                  v-if="item.showSchoolName"
                  :style="{
                    backgroundColor: item.schoolBadgeColor || '#34c759'
                  }"
                  :title="item.school_name || '未知学校'"
                  :data-fullname="item.school_name || '未知学校'"
                >
                  {{ item.school_name || '未知学校' }}
                </view>
              </view>
            </view>
            <view class="but" @click="push(item)">约课</view>
          </view>
          <view class="bottom">
            <view class="xing">
              <image
                src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/38a1174555253755312367_Star%2011%402x.png"
              ></image>
              <view>{{ item.score || 0 }}</view>
            </view>
            <view class="fen">|</view>
            <view class="text"
              ><text>{{ item.stage_name }}</text
              ><text style="margin: 3px">·</text>{{ item.subject_name }}</view
            >
          </view>
        </view>
        <view style="height: 40px"></view>
      </view>
    </pageList>
  </view>
</template>

<script>
import { goToLogin } from '../../utils/index'
import noData from '../commen/no-data.vue'
import { appoint } from '../../api/index'
import pageList from '../commen/page-list.vue'
import pickerSelectCode from '../commen/picker-select-code.vue'
import pickerSelect from '../commen/picker-select.vue'
import selectStageGrade from '../commen/select-stage-grade.vue'
import login from '../commen/login.vue'
export default {
  components: {
    pageList,
    noData,
    pickerSelectCode,
    selectStageGrade,
    pickerSelect,
    login
  },
  data() {
    return {
      search: {
        stage_grade: [],
        stage_grade_namw: [],
        subject_id: '',
        subject_id_name: '',
        sex: '',
        sex_name: '',
        teach_style: '',
        teach_style_name: '',
        page: 1,
        size: 10,
        loading: false
      },
      list: []
    }
  },
  computed: {
    userinfo2() {
      return this.$store.state.kaolaStudent.userinfo2
    },
    token() {
      return this.$store.state.kaolaStudent.token
    }
  },
  watch: {
    token(newToken, oldToken) {
      // 当 token 从无到有时，或者 token 发生变化时（如果需要处理 token 更新的情况）
      // 确保只有在 token 变为有效值时才加载
      if (newToken && !this.list.length) {
        // 确保只有在列表为空且token有效时加载，避免重复加载
        this.refresh()
      }
    }
  },
  created() {
    this.refresh()
    // // 如果创建时 token 就已经存在，则直接加载
    // if (this.token) {
    //   this.refresh();
    // }
    // // 否则等待 watch token 生效，或者在 onShow 中处理（如果父组件会触发）
  },
  methods: {
    refresh() {
      this.search.page = 1
      this.list = []
      this.getList()
    },
    // 获取随机颜色
    getRandomColor() {
      // 定义一些美观的亮色作为备选
      const colors = [
        // '#ff3b30', // 红色
        // '#5ac8fa', // 蓝色
        '#34c759' // 绿色
        // '#ff9500', // 橙色
        // '#af52de', // 紫色
        // '#ff2d55', // 粉色
        // '#5856d6', // 靛蓝色
        // '#ffcc00' // 黄色
      ]
      // 随机选择一种颜色
      return colors[Math.floor(Math.random() * colors.length)]
    },
    handleImageError(item) {
      // 为当前项设置显示学校名称的标志和随机颜色
      this.$set(item, 'showSchoolName', true)
      this.$set(item, 'schoolBadgeColor', this.getRandomColor())
    },
    push(item) {
      // 未登录时跳转登录页
      if (!this.token) {
        this.$xh.Toast('请先登录，才可以约课', {
          duration: 10000, // 显示8秒，更长的时间
          position: 'top', // 显示在中间
          icon: 'none', // 可以根据需要选择图标，'none'表示不显示图标
          backgroundColor: 'rgba(0, 0, 0, 0.8)', // 背景色更暗，更明显
          color: '#ffffff' // 文字颜色更亮
        })
        this.goToLogin()
        return
      }
      console.log(item, ...arguments, 'ddd')
      this.$xh.push(
        'kaolaStudent',
        `pages/companions/reservationTeacher?teacher_id=${item.teacher_id}`
      )
    },
    getList() {
      this.search.loading = true
      appoint.teacher
        .getList({
          state_id: this.search.stage_grade[0] || '',
          grade_id: this.search.stage_grade[1] || '',
          teach_style: this.search.teach_style,
          subject_id: this.search.subject_id,
          sex: this.search.sex,
          teach_style: this.search.teach_style,
          page: this.search.page++,
          size: this.search.size
        })
        .then(res => {
          if (res.data?.list?.length) {
            this.list = this.list.concat(
              res.data.list.map(item => {
                let subject_name = []
                let stage_name = []
                if (item.subjects?.length) {
                  item.subjects.forEach(ee => {
                    if (!subject_name.find(s => s == ee.subject_name)) {
                      subject_name.push(ee.subject_name)
                    }
                    if (!stage_name.find(s => s == ee.stage_name)) {
                      stage_name.push(ee.stage_name)
                    }
                  })
                }
                item.subject_name = subject_name.join('/')
                item.stage_name = stage_name.join('/')
                // 初始化时设置showSchoolName为false，确保默认显示图片而不是特效
                item.showSchoolName = false
                return item
              })
            )
          }
          this.search.loading = false
        })
        .catch(() => {
          this.search.loading = false
        })
    },
    goToLogin
  }
}
</script>

<style lang="scss" scoped>
.list {
  .item {
    margin: 0 12px 8px 12px;
    background: #ffffff;
    border-radius: 12px 12px 12px 12px;
    padding: 12px 16px 16px 16px;
    position: relative;
    .teacher_grade_name {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 103px;
      height: 40px;
      background-image: url(http://xy-kaola.oss-cn-beijing.aliyuncs.com/public/7cf0174764961528493331_backddsfa.png);
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      image {
        margin-left: 16px;
        width: 79px;
        height: 20px;
      }
    }
    .bottom {
      padding-top: 16px;
      display: flex;
      align-items: center;
      .xing {
        display: flex;
        align-items: center;
        image {
          width: 16px;
          height: 16px;
          // width: 16.17px;
          // height: 15px;
        }
        view {
          font-weight: 500;
          font-size: 14px;
          color: #fb8105;
          line-height: 20px;
        }
      }
      .fen {
        margin: 0 7px;
        // width: 13px;
        font-size: 12px;
        color: rgba(34, 35, 51, 0.35);
        line-height: 15px;
      }
      .text {
        font-weight: 400;
        font-size: 10.5px; // ui 是12
        color: rgba(34, 35, 51, 0.65);
      }
    }
    .top {
      display: flex;
      align-items: center;
      .info {
        display: flex;
        align-items: center;
        .avatar {
          width: 40px;
          height: 40px;
          background: #999faa;
          border-radius: 50%;
        }
        .name {
          margin-right: 4px;
          margin-left: 8px;
          font-weight: 500;
          font-size: 16px;
          color: #222333;
        }
        .sex {
          width: 18px;
          height: 18px;
          margin-right: 8px;
        }
        .label {
          width: 77px;
          height: 23px;
        }
      }
      .but {
        margin-left: auto;
        width: 60px;
        height: 32px;
        font-weight: 500;
        font-size: 14px;
        color: #222333;
        line-height: 32px;
        text-align: center;
        background: #dbf46b;
        border-radius: 10px 10px 10px 10px;
      }
    }
  }
}
.search {
  display: flex;
  // justify-content: space-between;
  padding-left: 12px;
  padding-top: 5px;
  padding-bottom: 8px;
  .search-item {
    // position: absolute;
    // top: 112px;
    // right: 12px;
    // width: 56px;
    padding: 0 8px 0 12px;
    height: 32px;
    background: #ffffff;
    display: flex;
    position: relative;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    border-radius: 17px;
    border: 1px solid rgba(223, 241, 134, 0.7);
    .img3 {
      margin-left: 4px;
      width: 10px;
      height: 10px;
    }
    view {
      font-weight: 400;
      font-size: 12px;
      color: #222333;
    }
  }
}

.page-content {
  height: 100%;
  flex: 1;
  height: calc(100%);
  display: flex;
  flex-direction: column;
  .scrollpageList {
    flex: 1;
    height: 500px;
  }
}

.school-label-container {
  position: relative;
  display: inline-block;
  height: 23px;
  .school-name-text {
    position: relative;
    height: 23px;
    border-radius: 11.5px;
    color: white;
    font-size: 11px;
    font-weight: 500;
    display: flex !important;
    align-items: center;
    justify-content: center;
    padding: 0 10px 0 14px; /* 左侧留出星星位置 */
    box-sizing: border-box;
    white-space: nowrap;
    width: auto;
    max-width: 120px !important; /* 限制最大宽度 */
    overflow: hidden !important;
    text-overflow: ellipsis !important; /* 显示省略号 */
    background-color: #34c759; /* 默认背景色 */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    // 星星图标样式 - 使用更具体的类名避免冲突
    &::before {
      content: '★';
      position: absolute;
      left: 6px;
      font-size: 8px;
      color: white;
      opacity: 0.8;
      z-index: 1;
    }
    // 悬停时扩展宽度以显示完整文本
    &:hover {
      max-width: none !important;
      z-index: 100;
      transition: max-width 0.3s ease-in-out;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
    // 悬停时显示tooltip效果（使用data属性获取完整名称）
    &:hover::after {
      content: attr(data-fullname);
      position: absolute;
      bottom: 100%;
      left: 50%;
      transform: translateX(-50%);
      margin-bottom: 5px;
      padding: 4px 8px;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      font-size: 11px;
      border-radius: 4px;
      white-space: nowrap;
      z-index: 999;
      pointer-events: none;
    }
    // 为tooltip添加小三角（使用不同的伪元素方式避免冲突）
    &:hover {
      &::before {
        content: '' !important;
      }
      &::after {
        content: attr(data-fullname);
      }
      // 使用一个额外的span元素来添加小三角（替代方案）
      &::before {
        content: '';
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        margin-top: 1px;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        border-bottom: 4px solid rgba(0, 0, 0, 0.8);
        z-index: 999;
        pointer-events: none;
      }
    }
  }
}
.login-but {
  width: 203px;
  text-align: center;
  height: 44px;
  line-height: 44px;
  background: #222333;
  border-radius: 22px;

  font-weight: bold;
  font-size: 14px;
  color: #ffffff;
}
</style>
