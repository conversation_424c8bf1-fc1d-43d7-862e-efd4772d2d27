<template>
  <view class="wait-page" v-if="qingBeiAnswerin.show">
    <view class="background">
      <image
        class="close"
        @click="close"
        src="/static/imgs/kaolaStudent/close2.svg"
      ></image>
      <!-- 正常显示 -->
      <view class="content" v-if="progressNum < 100">
        <view class="role">
          <view class="tips"><view>正在全力为你锁定老师</view></view>
          <!-- https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/6324174557307533862553_BgSub_%E7%94%B7%E5%AD%A92.pic%402x.png -->
          <image
            class="img"
            :src="backgroup_img"
             mode="aspectFit"
          ></image>
        </view>
        <view class="question-box">
          <view class="title">
            <view>问题</view>
            <image
              src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/529e174548540895743798_Rectangle%20280%402x.png"
            ></image>
          </view>
          <view class="text" v-if="info.message_type == 'text'">
            {{ info.content }}
          </view>

          <view class="text" v-if="info.message_type == 'image'">
            <image
              mode="center"
              show-menu-by-longpress
              :src="info.content"
              @click="previewImage(info.content)"
            ></image>
          </view>
          <view class="but" @click="close">取消提问</view>
        </view>
        <view class="load-box">
          <view class="loading">
            <view
              :style="{
                width: progressNum + '%'
              }"
            ></view>
          </view>
          <view class="tips">
            <image
              src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/52b1174548537547219914_Group%20248%402x.png"
            ></image>
            <view>预计等待 2 分钟</view>
          </view>
        </view>
      </view>


      <!-- 时间到 -->
       <view class="time-out" v-if="progressNum >99">
        <view class="timeout-container">
          <view class="header">
            <view class="title-en">
              <view class="l">
                W
              </view>
              ARM PROMPT</view>
            <view class="title-cn">
              温馨提示
              <view class="icon">（^_^）</view>
              <!-- <image class="icon" src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/kaola/image/20240726/153948-81.png"></image> -->
            </view>
          </view>
          <view class="body">
            <view class="greeting">亲爱的同学:</view>
            <view class="message">
              您提交的提问我们已经收到,但目前暂时没有老师接单答疑。我们理解您迫切希望得到解答的心情,对此深表歉意。
            </view>
            <view class="reason-title">出现这种情况原因:</view>
            <view class="reasons">
              <view>1. 当前时段是答疑高峰期,老师较为繁忙,建议稍后再试;</view>
              <view>2. 问题描述不够清晰,可补充更多细节;如学科、具体疑惑点。</view>
            </view>
            <view class="suggestion-title">您可以:</view>
            <view class="suggestions">
              <view>1. 稍后刷新页面,重新发起请求;</view>
              <view>2. 检查问题内容,补充关键信息以提高匹配效率。</view>
            </view>
            <view class="thanks">
              感谢您的理解与支持!每一个问题都会被重视,我们会持续优化服务,为您提供更高效的学习帮助。
            </view>
            <view class="footer">
            始终陪伴您的【考拉帮帮团】
          </view>
          </view>
        </view>
        <view class="but" @click="close">知道了</view>
       </view>

       
    </view>
  </view>
</template>

<script>
import { student, aimessageStream } from '../../../api/index'
export default {
  props: {},
  data() {
    return {
      progressNum: 0,
      backgroup_img:"https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/6324174557307533862553_BgSub_%E7%94%B7%E5%AD%A92.pic%402x.png",
      info: {}
    }
  },
  created() {},
  computed: {
    qingBeiAnswerin() {
      return this.$store.state.kaolaStudent.qingBeiAnswerin
    }
  },
  watch: {
    'qingBeiAnswerin.show'(val) {
      if (val) {
        this.init()
      }
    }
  },
  methods: {
    init() {
      this.progressNum = 0
      this.time = 0
      const use =  this.$store.getters['kaolaStudent/getAiAvatar'];
    // console.log("Ssss",use)
    if(use&&use?.backgroup_img){
      //接口返回正确数据时候 解开注释
      this.backgroup_img = use.backgroup_img
    }
      this.progress()
      console.log(this.qingBeiAnswerin)
      this.showWait(this.qingBeiAnswerin.data)
    },
    async showWait(query) {
      let id = ''
      await student.question
        .created({
          original_type: 2
        })
        .then(res => {
          id = res.data.id
        })
      query.id = id
      this.info = query
      student.question.teachermessage(query)
    },
    progress() {
      setTimeout(() => {
        this.progressNum++
        if (this.progressNum == 100) {
          this.time = 100 // 100秒
          clearTimeout()
        } else {
          this.progress()
        }
      }, 1000)
    },
    close() {
      this.$store.commit('kaolaStudent/setQingBeiAnswerin', {
        show: false,
        query: {}
      })
      this.$emit('close')
    },
    previewImage(url) {
      wx.previewImage({
        current: url,
        urls: [url]
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.wait-page {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 10;
  width: 100vw;
  height: 100vh;
  // width: 375px;
  // height: 812px;
  background: linear-gradient(180deg, #d4ff00 0%, #ffffff 100%), #ffffff;
  // position: relative;
  .background {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    justify-content: flex-end;
    background-image: url(https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/3cac174557323293588407_Group%20256%402x.png);
    background-size: 100vw 157.86vw;
    background-repeat: no-repeat;
  }
   // Timeout styles
  .time-out{
     
    // display: flex;
    width: 335px;
    margin-left: 20px;
    font-size: 12px;
    margin-bottom: 83px;
    // justify-content: center;
    .but {
    margin: 0 auto;
    width: 131px;
    text-align: center;
    height: 44px;
    line-height: 44px;
    background: #222333;
    border-radius: 33px 33px 33px 33px;
    font-weight: bold;
    font-size: 14px;
    color: #ffffff;
    margin-top: 44px;
    // margin-bottom: 30px;
  }
  .timeout-container {
    width: 100%;
    max-width: 340px;
    background-color: #fff;
    border: 2px solid #ff0000;
    border-radius: 12px;
    padding: 10px;
    box-sizing: border-box;
    font-family: sans-serif;
    color: #333;
    background: #fefcec;

    .header {
      text-align: left;
      .title-en {
        font-weight: bold;
        font-size: 16px;
        display: flex;
        color: #333;
        padding-bottom: 2px;
        margin-bottom: 8px;
        .l{
          border-bottom: 2px solid #333;
        }
      }
      .title-cn {
        font-weight: bold;
        font-size: 18px;
        color: #333;
        display: flex;
        align-items: center;
        .icon {
          width: 75px;
          height: 24px;
          margin-left: 8px;
          color: #FF5722;
        }
      }
    }

    .body {
      font-size: 12px;
      line-height: 1.8;
      color: #555;
      margin-top: 15px;
      text-align: left;

      .greeting,
      .reason-title,
      .suggestion-title {
        font-weight: bold;
        margin-top: 10px;
        margin-bottom: 5px;
        color: #333;
      }
      
      // .reasons,
      // .suggestions {
      //   padding-left: 1.2em;
      //   text-indent: -1.2em;
      // }

      .thanks {
        margin-top: 15px;
      }
    }

    .footer {
      margin-top: 20px;
      text-align: center;
      font-size: 12px;
      color: #999;
    }
  
}
}
  // border-radius: 0px 0px 0px 0px;
  .content {
    display: flex;
    flex-direction: column;
  }
  .close {
    position: fixed;
    left: 0;
    top: 40px;
    border: 16px solid rgba(111, 133, 5, 0);
    width: 24px;
    height: 24px;
    border-radius: 0px 0px 0px 0px;
  }
  .role {
    margin: 0 auto;
    padding-bottom: 32px;
    view {
      position: relative;
      z-index: 1;
    }
    .tips {
      padding: 11px 16px;
      background: rgba(34, 35, 51, 0.85);
      border-radius: 12px 12px 12px 12px;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      line-height: 22px;
      position: relative;
      text-align: center;
      &::before {
        content: ' ';
        position: absolute;
        bottom: -6px;
        left: calc(50% - 6px);
        display: block;
        transform: rotateZ(45deg);
        width: 12px;
        border-radius: 2px;
        height: 12px;
        background: rgba(34, 35, 51, 0.85);
      }
    }
    .img {
      margin-top: 34px;
      // width: 152px;
      height: 242px;
      border-radius: 0px 0px 0px 0px;
    }
  }
}
.load-box {
  padding-bottom: 46px;
  padding-top: 42px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .loading {
    margin-bottom: 12px;
    width: 239px;
    height: 10px;
    background: #eff983;
    border-radius: 4px 4px 4px 4px;
    overflow: hidden;
    view {
      transition: width 1s;
      width: 0;
      height: 100%;
      background: #222333;
      border-radius: 4px 4px 4px 4px;
    }
  }
  .tips {
    display: flex;
    align-items: center;
    view {
      font-weight: bold;
      font-size: 16px;
      color: #222333;
      line-height: 19px;
    }
    image {
      width: 22px;
      height: 22px;
      margin-right: 4px;
    }
  }
}
.question-box {
  margin: 0 auto;
  padding: 16px 16px 24px 16px;
  width: 343px;
  background: #ffffff;
  box-shadow: 0px 29px 45px -13px rgba(111, 133, 5, 0.2);
  border-radius: 16px 16px 16px 16px;
  display: flex;
  flex-direction: column;
  .title {
    position: relative;
    view {
      position: relative;
      z-index: 1;
      font-weight: bold;
      font-size: 16px;
      color: #222333;
      line-height: 16px;
    }
    image {
      position: absolute;
      left: 0;
      top: 0;
      width: 55.79px;
      height: 26px;
    }
  }
  .text {
    font-weight: 400;
    font-size: 14px;
    color: #222333;
    line-height: 20px;
    margin-top: 10px;
    image {
      width: 100%;
      height: 80px;
      border-radius: 8px 8px 8px 8px;
    }
  }
  .but {
    margin: 0 auto;
    width: 131px;
    text-align: center;
    height: 44px;
    line-height: 44px;
    background: #222333;
    border-radius: 33px 33px 33px 33px;
    font-weight: bold;
    font-size: 14px;
    color: #ffffff;
    margin-top: 24px;
  }
}
</style>
