<template>
  <div>
    <view class="top-nav">
      <view :style="statusBarHeight"></view>
      <view class="navbar">
        <view class="back" @click="$xh.back()">
          <image
            class="navbarback"
            src="/static/imgs/kaolaStudent/navbarback.png"
          ></image>
        </view>
        <view class="order-status" v-if="info.order_status == 1">
          <view class="name"> 等待付款 </view>
          <view class="time">请在 15:00 内支付</view>
        </view>
        <!-- <view class="title">{{ title }}</view> -->
      </view>
    </view>
    <view class="teacher-box">
      <image
        class="avatar"
        :src="
          $xh.completepath(
            info.teacher_avatar ||
              'public/5ef917440086508568967_laoshitouxiang.png'
          )
        "
      ></image>
      <view class="teacher-info">
        <view class="name">
          <view>{{ info.teacher_name }}</view>
          <!-- <image
            src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/8cea17455524882824282_Group%20190%402x.png"
          ></image> -->

          <!-- <view v-if="info.teacher_grade_name" style="margin-left: 12px">{{
            info.teacher_grade_name
          }}</view> -->

          <image
            class="grade"
            :src="$xh.teacher_grade_img(info.teacher_grade_name)"
          ></image>
        </view>
        <view class="data">
          <view class="xing">
            <image
              src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/38a1174555253755312367_Star%2011%402x.png"
            ></image>
            <view>{{ info.score }}</view>
          </view>
          <view class="fen">｜</view>
          <view class="num">
            <view style="margin-right: 2px">伴学</view>
            <view>{{ info.appointed_num }}次</view>
          </view>
        </view>
      </view>
      <!-- teacher_school_logo -->
      <!--   src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/36e2174555257039919651_Group%20261%402x.png" -->
      <image
        v-if="info.teacher_school_logo"
        class="xueixao"
        :src="$xh.completepath(info.teacher_school_logo)"
      ></image>
    </view>

    <view class="order-info">
      <view class="text-item">
        <view class="label">
          <view> 预定课时 </view>
        </view>
        <view class="value">{{ info.goods_num }}</view>
      </view>
      <view class="text-item">
        <view class="label"> <view> 课时费用 </view> </view>
        <view class="value">¥{{ info.payable_amount }}</view>
      </view>

      <view class="text-item">
        <view class="label"> <view> 优惠券 </view></view>
        <view class="value empty">无可用</view>
      </view>
      <view class="text-item">
        <view class="label"> <view> 总计 </view> </view>
        <view class="value">¥{{ info.payable_amount }}</view>
      </view>
    </view>

    <view class="slot-info" style="margin-top: 8px">
      <view class="title"> 课程时间表 </view>
      <view class="appoint">
        <view class="item" v-for="(item, index) of info.appoint">
          <view class="date">{{ item.dateName }} {{ item.week }}</view>
          <view class="time">{{ item.start_time }}-{{ item.end_time }}</view>
        </view>
      </view>
    </view>
    <view>
      <!-- {{ info }} -->
    </view>
    <view class="pay" v-if="info.order_status == '1'">
      <view class="price">
        <view class="actualPayment"
          ><text>总计</text><text style="margin: 0 2px 0 4px">¥</text
          ><text>{{ info.payable_amount }}</text></view
        >
        <view class="discount">已优惠0.00元</view>
      </view>
      <view
        class="but"
        @click="
          $xh.push(
            'kaolaStudent',
            'pages/my/reservationOrder/pay?order_id=' +
              query.order_id +
              '&flow_id=' +
              query.flow_id
          )
        "
        >支付</view
      >
    </view>
  </div>
</template>

<script>
import { appoint, account } from '../../../api'
import navbar from '../../../components/commen/navbar.vue'
import dayjs from 'dayjs'
export default {
  components: {
    navbar
  },
  data() {
    return {
      info: {
        order_status: 0,
        list: []
      },
      query: {},
      statusBarHeight: 'height:25px;'
    }
  },
  onLoad(query) {
    this.query = query
    this.detail(query.order_id)
    let BarHeight = wx.getSystemInfoSync().statusBarHeight || 25
    this.statusBarHeight = `height:${BarHeight}px;`
  },
  computed: {
    systemMessage() {
      return this.$store.state.kaolaStudent.systemMessage
    }
  },
  methods: {
    detail(id) {
      appoint.order.detail({ order_id: id }).then(res => {
        res.data.appoint = res.data.appoint.map(item => {
          return {
            ...item,
            dateName: dayjs(item.date).format('MM/DD')
          }
        })
        this.info = res.data
      })
      // appoint.order.DayiDetail({ order_id: id }).then(res => {
      //   this.info = res.data
      // })
    }
  }
}
</script>

<style lang="scss" scoped>
.top-nav {
  background-color: #fff;
  .order-status {
    padding-bottom: 4px;
    .name {
      font-weight: 500;
      font-size: 18px;
      color: #ff6111;
      margin-bottom: 5px;
    }
    .time {
      font-weight: 400;
      font-size: 12px;
      color: rgba(34, 35, 51, 0.85);
    }
  }
}
.navbar {
  display: flex;
  align-items: center;
  // justify-content: center;
  height: 50px;
  position: relative;
  .title {
    font-weight: 400;
    font-size: 16px;
    color: #000000;
  }
  .back {
    // position: absolute;
    // left: 0;
    padding-left: 16px;
    padding-right: 15px;
    height: 100%;
    display: flex;
    align-items: center;
  }
  .navbarback {
    width: 16px;
    height: 20px;
  }
}
.pay {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  background-color: #fff;
  padding: 12px 16px 32px 16px;
  display: flex;
  justify-content: space-between;
  .price {
    .actualPayment {
      display: flex;
      align-items: baseline;
      text:nth-child(1) {
        font-weight: 400;
        font-size: 14px;
        color: #222333;
      }
      text:nth-child(2) {
        font-weight: 500;
        font-size: 14px;
        color: #ff6111;
      }
      text:nth-child(3) {
        font-weight: 500;
        font-size: 20px;
        color: #ff6111;
      }
    }
    .discount {
      font-weight: 400;
      font-size: 12px;
      color: rgba(34, 35, 51, 0.65);
      line-height: 20px;
    }
  }
  .but {
    width: 188px;
    text-align: center;
    height: 44px;
    line-height: 44px;
    background: #ff6111;
    border-radius: 33px 33px 33px 33px;
    font-weight: bold;
    font-size: 14px;
    color: #ffffff;
  }
}
.slot-info {
  padding: 16px 0px 6px 16px;
  margin: 0 12px;
  background: #ffffff;
  border-radius: 12px 12px 12px 12px;
  .title {
    font-weight: 400;
    font-size: 15px;
    color: #222333;
    line-height: 20px;
    margin-bottom: 16px;
  }
  .appoint {
    display: flex;
    flex-wrap: wrap;
    .item {
      flex-shrink: 0;
      margin-right: 8px;
      width: calc((100% - 32px) / 3);
      max-width: 108px;

      padding: 10px 0 12px 10px;
      background: #f7f9fb;
      border-radius: 8px 8px 8px 8px;
      position: relative;
      background: #f7f9fb;
      margin-bottom: 10px;
      .date {
        font-weight: 400;
        font-size: 12px;
        color: rgba(34, 35, 51, 0.65);
        line-height: 16px;
        margin-bottom: 8px;
      }
      .time {
        font-weight: 400;
        font-size: 12px;
        color: #222333;
        line-height: 16px;
      }
    }
  }
}
.order-info {
  padding: 6px 16px;
  margin: 0 12px;
  background: #ffffff;
  border-radius: 12px 12px 12px 12px;
  .text-item {
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .label {
      view {
        font-weight: 400;
        font-size: 15px;
        color: #222333;
        line-height: 20px;
      }
      .tips {
        margin-top: 4px;
        font-weight: 400;
        font-size: 12px;
        color: rgba(34, 35, 51, 0.35);
        line-height: 14px;
      }
    }
    .value {
      font-weight: 400;
      font-size: 15px;
      color: #222333;
      line-height: 20px;
    }
    .empty {
      color: rgba(34, 35, 51, 0.35);
    }
  }
}

.teacher-box {
  margin: 8px 12px 8px 12px;
  padding: 15px 16px;
  background: #ffffff;
  border-radius: 12px 12px 12px 12px;
  display: flex;
  align-items: center;
  .avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 12px;
    flex-shrink: 0;
  }
  .xueixao {
    margin-left: auto;
    width: 77px;
    height: 23px;
  }
  .teacher-info {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .name {
      display: flex;
      align-items: center;
      view {
        font-weight: 500;
        font-size: 16px;
        color: #222333;
        line-height: 20px;
        margin-right: 4px;
      }
      image {
        width: 79px;
        height: 20px;
      }
    }
    .data {
      margin-top: 8px;
      display: flex;
      align-items: center;
      .xing {
        display: flex;
        align-items: center;
        image {
          width: 16.17px;
          height: 15px;
        }
        view {
          font-weight: 500;
          font-size: 15px;
          color: #fb8105;
          line-height: 20px;
        }
      }
      .fen {
        margin: 0 8px;
        font-size: 13px;
        color: rgba(34, 35, 51, 0.35);
        line-height: 15px;
      }
      .num {
        display: flex;
        align-items: center;
        view {
          font-weight: 400;
          font-size: 13px;
          color: rgba(34, 35, 51, 0.6);
          line-height: 16px;
        }
        view:nth-child(1) {
          color: #222333;
        }
      }
    }
  }
}
</style>
