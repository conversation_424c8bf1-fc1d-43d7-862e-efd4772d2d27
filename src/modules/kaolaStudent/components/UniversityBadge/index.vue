<template>
  <view class="container"> 
    <image class="image" src="../../../../static/imgs/kaolaStudent/universty.png" ></image>
    <view class="purple-bg"></view>
    <view class="school-name">{{ schoolName }}</view>
  </view>
</template>
<script> 
export default {
  props: {
    schoolName: {
      type: String,
      default: '北京大学'
    }
  },
}
</script>
<style scoped lang="scss">
.container { 
  position: relative;
  width: 154rpx;
  height: 46rpx;
  .image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 10rpx;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 1;
  }
  .purple-bg {
    width: 71.5%;
    height: 100%;
    background-color: #9A26C9;
    opacity: 0.95;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 2;
    border-radius: 0 46rpx 46rpx 0;
  }
  .school-name { 
    
    border-radius: 46rpx;
    position: absolute;
    right: 0;
    top: 0;
    color: #fff;
    width: 80%;
  height: 46rpx;
  line-height: 46rpx;
  font-size: 22rpx;
    text-align: center;
    position: absolute;
    z-index: 3;
  }

}
</style>
