<template>
  <view>
    <view class="mask" v-if="isUnpaid" @click="show = true"> </view>
    <view class="unpaid" v-if="show">
      <view class="unpaid-body">
        <view class="title">
          <view>温馨提示</view>
          <image
            src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/3b34174548334920816804_Vector%203%402x.png"
          ></image>
        </view>
        <view class="text">先完成上次答疑结算，即可继续提问哦~</view>

        <view class="but" @click="success">立即支付</view>
        <view class="but2" @click="hide">我再想想</view>
      </view>
    </view>
  </view>
</template>

<script>
import { student } from '../../api'
export default {
  props: {
    pageShow: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      show: false,
      isUnpaid: false,
      info: {
        flow_id: '',
        order_id: '',
        pay_amount: 0,
        is_supervise_order: 0
      }
    }
  },

  created() {
    this.getUnpaid()
  },
  watch: {
    pageShow(val) {
      if (val) {
        this.getUnpaid()
      }
    }
  },
  methods: {
    getUnpaid() {
      // this.isUnpaid = true
      student.question.lastunpaidorder({}).then(res => {
        if (res.data.is_all_paid == 1) {
          this.isUnpaid = false
        }
        if (res.data.is_all_paid == 2) {
          this.isUnpaid = true
          this.info = res.data
        }
      })
    },
    success() {
      uni.navigateTo({
        url: `/modules/kaolaStudent/pages/my/order/detail?flow_id=${this.info.flow_id}&is_supervise_order=${this.info.is_supervise_order}&order_id=${this.info.order_id}&pay_amount=${this.info.pay_amount}`
      })
      this.show = false
    },
    hide() {
      this.show = false
    }
  }
}
</script>

<style lang="scss" scoped>
.mask {
  position: fixed;
  z-index: 10;
  left: 0;
  bottom: 0;
  width: 100vw;
  height: calc(100vh - 100px);
  background: rgba($color: #000000, $alpha: 0);
}
.unpaid {
  position: fixed;
  z-index: 10;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 0px 0px 0px 0px;
  display: flex;
  align-items: center;
  justify-content: center;
  .unpaid-body {
    width: 311px;
    // height: 220px;
    background: linear-gradient(180deg, #eafe80 0%, #ffffff 100%), #ffffff;
    background-image: url(https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/58a417454833674607463_Mask%20group%402x.png);
    background-size: 100% 96px;
    background-repeat: no-repeat;
    border-radius: 16px 16px 16px 16px;
    padding: 28px 26px 0 26px;
    display: flex;
    flex-direction: column;
    .title {
      margin: 0 auto;
      position: relative;
      view {
        font-weight: bold;
        font-size: 20px;
        color: #222333;
        line-height: 20px;
        position: relative;
        z-index: 1;
      }
      image {
        position: absolute;
        bottom: -5px;
        left: 0;
        width: 77px;
        height: 10px;
      }
    }
    .text {
      font-weight: 400;
      font-size: 14px;
      color: rgba(34, 35, 51, 0.85);
      line-height: 20px;
      margin-top: 16px;
      margin-bottom: 20px;
    }
    .but {
      width: 259px;
      line-height: 44px;
      height: 44px;
      background: #222333;
      border-radius: 33px 33px 33px 33px;
      font-weight: bold;
      font-size: 14px;
      color: #ffffff;
      text-align: center;
    }
    .but2 {
      margin: 0 auto;
      text-align: center;
      padding: 16px;
      font-weight: 400;
      font-size: 14px;
      color: rgba(34, 35, 51, 0.85);
      line-height: 20px;
    }
  }
}
</style>
