<template>
  <view class="systemMessage" v-if="systemMessage.isNavigate" @click="push">
    进入直播间
  </view>
</template>

<script>
export default {
  computed: {
    systemMessage() {
      return this.$store.state.kaolaStudent.systemMessage
    }
  },
  methods: {
    push() {
      uni.navigateTo({
        url: `/modules/kaolaStudent/pages/my/systemMessage`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.systemMessage {
  position: fixed;
  right: 10px;
  bottom: 200px;
  background-color: #fff;
  z-index: 1;
}
</style>
