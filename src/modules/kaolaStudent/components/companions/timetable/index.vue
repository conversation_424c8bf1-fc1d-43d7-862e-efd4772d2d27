<template>
  <view class="timetable">
    <view class="search">
      <view></view>
      <view class="tabs">
        <view
          class="item"
          :class="{
            active: tabIndex == '1'
          }"
          @click="tabChange('1')"
        >
          <view>周</view>
        </view>
        <view
          class="item"
          :class="{
            active: tabIndex == '2'
          }"
          @click="tabChange('2')"
        >
          <view>月</view>
        </view>
      </view>
    </view>
    <view class="timetable-content">
      <week
        v-if="tabIndex == '1'"
        ref="week"
        :pageShow="pageShow"
        :currentDate.sync="currentDate"
        :selectDate.sync="selectDate"
        @select="tabIndex = '2'"
      ></week>
      <month
        v-if="tabIndex == '2'"
        ref="month"
        :pageShow="pageShow"
        :currentDate.sync="currentDate"
        :selectDate.sync="selectDate"
      ></month>
    </view>
  </view>
</template>

<script>
import dayjs from 'dayjs'
import week from './week.vue'
import month from './month.vue'
// 添加缺失的导入
import { appoint } from '../../../api/index'
export default {
  props: {
    pageShow: {
      type: Boolean,
      default: true
    }
  },
  components: {
    week,
    month
  },
  data() {
    return {
      tabIndex: 2,
      currentDate: dayjs().format('YYYY-MM-DD'),
      selectDate: dayjs().format('YYYY-MM-DD')
    }
  },
  methods: {
    tabChange(index) {
      this.tabIndex = index

      uni.$emit('timetableTabChange', index)
    },
    refreshData() {
      // 使用nextTick确保组件已渲染
      this.$nextTick(() => {
        if (this.tabIndex == '1' && this.$refs.week) {
          this.$refs.week.my();
        } else if (this.tabIndex == '2' && this.$refs.month) {
          this.$refs.month.my({
            // 设置合适的日期范围
            start: dayjs().format('YYYY-MM-01'),
            end: dayjs().format('YYYY-MM-DD')
          });
        } else {
          // 强制刷新数据
          if (this.tabIndex == '2') {
            // 直接调用api获取数据的逻辑
            this.loadMonthData();
          }
        }
      });
    },
    // 新增：直接加载月数据的方法
    loadMonthData() {
      if (!this.token) {
        console.warn('Token is not available, skipping data fetch.');
        return;
      }
      // 调用api获取数据
      appoint.student
        .my({
          // 设置合适的日期范围
          start: dayjs().format('YYYY-MM-01'),
          end: dayjs().format('YYYY-MM-DD')
        })
        .then(res => {
          console.log('直接加载月数据成功', res);
          // 这里可以根据需要更新相关数据
        });
    }
  }
}
</script>

<style lang="scss" scoped>
.timetable {
  height: calc(100%);
  .timetable-content {
    height: calc(100% - 44px);
  }
}
.search {
  // height: 44px;
  width: 100vw;
  padding: 0 12px 12px 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .tabs {
    display: flex;
    align-items: center;
    .item {
      font-weight: 500;
      font-size: 14px;
      color: #222333;
      line-height: 32px;
      width: 32px;
      text-align: center;
      height: 32px;
      background: #ffffff;
      border-radius: 8px 8px 8px 8px;
      border: 1px solid rgba(223, 241, 134, 0.7);
      margin-left: 16px;
    }
    .active {
      background: #dbf46b;
      position: relative;
      &::before {
        position: absolute;
        left: 9px;
        bottom: 0;
        content: ' ';
        width: 14px;
        height: 4px;
        background-size: 14px 4px;
        background-image: url(http://xy-kaola.oss-cn-beijing.aliyuncs.com/public/9404174705276502748132_Rectangle%20426%402x.png);
      }
    }
  }
}
</style>
