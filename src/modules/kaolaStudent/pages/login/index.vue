<template>
  <div>
    <image
      class="loginback"
      src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/91f7174382106181486742_loginback.png"
    ></image>

    <div class="login-box">
      <div v-if="!radio" @click="mask()" class="login-but">手机号快捷登录</div>
      <login v-else @success="success">
        <div class="login-but">手机号快捷登录</div>
      </login>
      <div class="cancel-btn" @click="goBack">取消登录</div>
      <div class="tips" @click="radio = !radio">
        <div
          class="radio"
          :class="{
            select: radio
          }"
        >
          <uni-icons v-if="radio" type="success" size="12" color="#ffffff" />
        </div>
        <div class="text">
          我已阅读并同意
          <text class="agreement-link" @click.stop="openAgreement"
            >《用户服务协议》</text
          >
          、
          <text class="agreement-link" @click.stop="openPrivacy"
            >《隐私政策》</text
          >
          和
          <text class="agreement-link" @click.stop="openChildPrivacy"
            >《儿童隐私保护声明》</text
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { config, student } from '../../api/index.js'
import login from '../../components/commen/login.vue'
export default {
  components: {
    login
  },
  data() {
    return {
      radio: false,
      list: [],
      redirectUrl: '' // To store the redirect URL
    }
  },
  onLoad(options) {
    // Changed from created to onLoad to access options
    if (options && options.redirect) {
      this.redirectUrl = decodeURIComponent(options.redirect)
      console.log('Login page loaded with redirectUrl:', this.redirectUrl)
    } else {
      console.log('Login page loaded without redirectUrl.')
    }
    // this.getList()
  },
  methods: {
    // 添加新方法
    openAgreement() {
      console.log('openAgreement01')
      uni.navigateTo({
        url: '/modules/kaolaStudent/pages/agreement/userAgreement'
      })
    },
    openPrivacy() {
      uni.navigateTo({
        url: '/modules/kaolaStudent/pages/agreement/privacyPolicy'
      })
    },
    openChildPrivacy() {
      uni.navigateTo({
        url: '/modules/kaolaStudent/pages/agreement/childPrivacyPolicy'
      })
    },
    success() {
      console.log(
        'Login success triggered. Redirecting to:',
        this.redirectUrl || '/modules/kaolaStudent/pages/tabBar/home'
      )
      if (this.redirectUrl) {
        // Check if the redirectUrl is a tab page
        const tabPages = [
          '/modules/kaolaStudent/pages/tabBar/home',
          '/modules/kaolaStudent/pages/tabBar/course',
          '/modules/kaolaStudent/pages/tabBar/timetable',
          '/modules/kaolaStudent/pages/tabBar/my',
          '/modules/kaolaStudent/pages/tabBar/companions' // Added companions page
        ]
        // Extract the base path without query parameters for tab check
        const baseRedirectUrl = this.redirectUrl.split('?')[0]
        if (tabPages.includes(baseRedirectUrl)) {
          uni.switchTab({
            url: this.redirectUrl, // switchTab can handle query parameters
            fail: err => {
              console.error(
                'Failed to switchTab:',
                err,
                'Attempting redirectTo as fallback - THIS SHOULD NOT HAPPEN FOR TAB PAGES.'
              )
              // Fallback to home if switchTab fails for some unexpected reason, though it shouldn't for a tab page.
              uni.switchTab({ url: '/modules/kaolaStudent/pages/tabBar/home' })
            }
          })
        } else {
          uni.redirectTo({
            url: this.redirectUrl,
            fail: err => {
              console.error(
                'Failed to redirectTo:',
                err,
                'Attempting switchTab to home as fallback.'
              )
              uni.switchTab({ url: '/modules/kaolaStudent/pages/tabBar/home' })
            }
          })
        }
      } else {
        uni.switchTab({
          url: `/modules/kaolaStudent/pages/tabBar/home`,
          fail: err => {
            console.error('Failed to switchTab to home:', err)
          }
        })
      }
      // this.$xh.back() // Removed as it's generally not needed and can cause issues
      // this.$xh.push('kaolaStudent', 'pages/register/index')
    },
    goBack() {
      // 尝试返回上一页
      uni.navigateBack({
        fail: () => {
          // 如果返回失败，跳转到首页
          console.log('无法返回上一页，跳转到首页')
          uni.switchTab({
            url: '/modules/kaolaStudent/pages/tabBar/home'
          })
        }
      })
    },
    mask() {
      this.$xh.Toast(
        '请同意《用户服务协议》、《隐私政策》和《儿童隐私保护声明》！'
      )
    },
    getList() {
      config.back.getList({}).then(res => {
        this.list = res.data
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.login-box {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.cancel-btn {
  margin-top: 20rpx;
  color: #999;
  font-size: 28rpx;
  text-decoration: underline;
}
.tips {
  display: flex;
  align-items: flex-start;
  padding: 40rpx 30rpx 64px;
  width: 100%;
  box-sizing: border-box;
  .radio {
    width: 24rpx;
    height: 24rpx;
    border-radius: 50%;
    border: 1.5rpx solid #cccccc;
    margin-right: 12rpx;
    margin-top: 4rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    flex-shrink: 0;
    background-color: #ffffff;
  }
  .radio.select {
    border-color: #007aff;
    background-color: #007aff;
  }
  .text {
    font-weight: 400;
    font-size: 26rpx;
    color: #666666;
    line-height: 1.5;
    flex: 1;
  }
}

.text .agreement-link {
  color: #007aff;
  text-decoration: none;
  margin: 0 4rpx;
  position: relative;
  padding: 2rpx 0;
  &::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 1px;
    background-color: #007aff;
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
  }
  &:active::after {
    transform: scaleX(1);
  }
}

// 添加响应式调整，确保在小屏幕上显示正常
@media screen and (max-width: 320px) {
  .tips {
    .text {
      font-size: 24rpx;
    }
  }
}
.loginback {
  width: 100vw;
  height: 90vw;
}
.login-but {
  width: 203px;
  text-align: center;
  height: 44px;
  line-height: 44px;
  background: #222333;
  border-radius: 22px;

  font-weight: bold;
  font-size: 14px;
  color: #ffffff;
}
.text .agreement-link {
  color: #007aff;
  text-decoration: underline;
  margin: 0 4px;
}
</style>
