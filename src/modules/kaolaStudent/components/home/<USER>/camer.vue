<template>
  <view :style="{ height: windowHeight + 'px' }">
    <camera
      mode="normal"
      :device-position="devicePosition"
      :flash="flashStyle"
      :style="{ height: cameraHeight + 'px' }"
      @error="errorCamera"
      v-if="isAuth"
    >
      <cover-view class="controls" style="width: 100%; height: 100%">
        <cover-view class="center" id="myCamera">
          自定义内容。。。。。。。。。。
          <!-- <cover-image
              v-if="photoType == 'idCardCopy'"
              class="cover_img"
              src="../../static/images/sfzz.png"
              mode="widthFix"
            />
            <cover-image
              v-else-if="photoType == 'idCardNational'"
              class="cover_img"
              src="../../static/images/sfzf.png"
              mode="widthFix"
            />
            <cover-image
              v-else-if="
                photoType == 'bankCardPicFront' ||
                photoType == 'bankCardPicBack'
              "
              class="cover_img"
              src="../../static/images/bank.png"
              mode="widthFix"
            /> -->
        </cover-view>
      </cover-view>
    </camera>
    <!-- 底部操作区域 -->
    <view class="bottom bottom-box">
      <view class="wrap">
        <!-- 相册 -->
        <view @click="chooseImage">
          <image
            :src="picture"
            mode=""
            style="width: 60rpx; height: 60rpx"
          ></image>
        </view>
        <!-- 相机 -->
        <view @click="takePhoto">
          <image class="take-box" :src="icon"></image>
        </view>
        <!-- 切换前后摄像头 -->
        <view class="back" @click="switchBtn">
          <image
            :src="flip"
            mode=""
            style="width: 60rpx; height: 60rpx"
          ></image>
        </view>
      </view>
    </view>
    <canvas class="cop" canvas-id="image-canvas"></canvas>
    <image class="" :src="src"></image>
  </view>
</template>
<script>
export default {
  data() {
    return {
      flip: 'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/82aa174710789575332315_%E6%91%84%E5%83%8F%E5%A4%B4%E5%8F%8D%E8%BD%AC.png', // 反转
      icon: 'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/4f73174710931150449248_237%E6%8B%8D%E7%85%A7.png', // 相机
      picture:
        'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/8681174710793002335068_%E5%9B%BE%E7%89%87.png', // 照片
      cameraContext: {},
      windowHeight: '',
      cameraHeight: '',
      idcardFrontSide: true,
      photoType: '',
      devicePosition: 'back', // 摄像头默认后置
      flashStyle: 'off',
      tokens: '',
      isAuth: false,
      src: ''
    }
  },
  onLoad(options) {
    const _this = this
    uni.getSetting({
      success: res => {
        if (res.authSetting['scope.camera']) {
          // 用户已经授权
          _this.isAuth = true
          _this.init()
        } else {
          // 用户还没有授权，向用户发起授权请求
          uni.authorize({
            scope: 'scope.camera',
            success() {
              // 用户同意授权
              _this.isAuth = true
              _this.init()
            },
            fail(err) {
              console.log(err)
              // 用户不同意授权
              // _this.openSetting('camera').then(res => {
              //   _this.isAuth = true
              // })
            }
          })
        }
      },
      fail: res => {
        console.log('获取用户授权信息失败')
      }
    })

    uni.getSetting({
      success: res => {
        if (res.authSetting['scope.record']) {
          // 用户已经授权
          _this.isAuth = true
        } else {
          // 用户还没有授权，向用户发起授权请求
          uni.authorize({
            scope: 'scope.record',
            success() {
              // 用户同意授权
              _this.isAuth = true
            },
            fail() {
              // 用户不同意授权
              // _this.openSetting('record').then(res => {
              //   _this.isAuth = true
              // })
            }
          })
        }
      },
      fail: res => {
        console.log('获取用户授权信息失败')
      }
    })
  },
  onShow() {
    const systemInfo = uni.getSystemInfoSync()
    this.windowHeight = systemInfo.windowHeight
    this.cameraHeight = systemInfo.windowHeight - 160
  },
  methods: {
    init() {
      let _this = this
      if (uni.createCameraContext) {
        _this.cameraContext = uni.createCameraContext()
      } else {
        // 如果希望用户在最新版本的客户端上体验您的小程序，可以这样子提示
        uni.showModal({
          title: '提示',
          content:
            '当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。'
        })
      }
      // _this.photoType = options.photoType
      _this.devicePosition = 'back'
      _this.tokens = uni.getStorageSync('usertoken')
    },
    errorCamera(e) {
      const that = this
      uni.getSetting({
        success(res) {
          if (!res.authSetting['scope.camera']) {
            uni.showModal({
              title: '提示',
              content: '请开启摄像头权限，否则无法拍照',
              confirmText: '去开启',
              success(res) {
                if (res.confirm) {
                  uni.openSetting({
                    success(res) {
                      if (res.authSetting['scope.camera']) {
                        uni.navigateBack({
                          delta: 1
                        })
                      } else {
                        uni.navigateBack({
                          delta: 1
                        })
                      }
                    }
                  })
                } else if (res.cancel) {
                  uni.navigateBack({
                    delta: 1
                  })
                }
              }
            })
          }
        }
      })
    },
    // 拍照
    takePhoto() {
      uni.showLoading({
        title: '拍摄中'
      })
      console.log(this.cameraContext)
      this.cameraContext.takePhoto({
        quality: 'normal',
        success: res => {
          let idPhoto = res.tempImagePath
          this.loadTempImagePath(idPhoto)
          uni.showToast({
            title: '拍照成功',
            icon: 'none',
            duration: 1200
          })
        },
        fail: err => {
          console.log(err)
          uni.showToast({
            title: '拍照失败，请检查系统是否授权',
            icon: 'none',
            duration: 1200
          })
        }
      })
    },

    //rpx转px
    rpx2px(rpx) {
      const screenWidth = uni.getSystemInfoSync().screenWidth
      return (screenWidth * Number.parseInt(rpx)) / 750
    },

    loadTempImagePath(url) {
      let { windowWidth, windowHeight } = uni.getSystemInfoSync()
      const camera = uni.createSelectorQuery().select('#myCamera') // 获取camera组件
      camera
        .boundingClientRect(data => {
          const x = data.left // 取景框左上角的x坐标
          const y = data.top // 取景框左上角的y坐标
          const width = data.width // 取景框的宽度
          const height = data.height + 120 // 取景框的高度
          console.log(x, y, width, height, 'x, y, width, height')
          let testc = uni.createCanvasContext('image-canvas')
          testc.drawImage(url, 0, 0, windowWidth, windowHeight)
          testc.draw(false, () => {
            uni.canvasToTempFilePath({
              x: x, //设置图片x轴起始点
              y: y, //设置图片y轴起始点
              width: width,
              height: height,
              canvasId: 'image-canvas',
              fileType: 'jpg',
              quality: 1,
              complete: res2 => {
                console.log(res2.tempFilePath, 'res2.tempFilePath88')
                this.chosePhoto(res2.tempFilePath)
              }
            })
          })
        })
        .exec()
    },

    // 从相册选取
    chooseImage() {
      uni.chooseImage({
        count: 1,
        sizeType: ['original', 'compressed'],
        sourceType: ['album'],
        success: res => {
          let idPhoto = res.tempFilePaths[0]
          this.chosePhoto(idPhoto)
        },
        fail: err => {}
      })
    },

    //反转
    switchBtn() {
      if (this.devicePosition == 'front') {
        this.devicePosition = 'back'
      } else {
        this.devicePosition = 'front'
      }
    },

    // 选择图片跳转
    chosePhoto(item) {
      console.log(item, this.photoType, '拍摄完成')

      // if (
      //   this.photoType == 'idCardCopy' ||
      //   this.photoType == 'bankCardPicFront'
      // ) {
      //   // 身份证正面 银行卡正面
      //   this.ocrCard(item)
      // } else if (
      //   this.photoType == 'idCardNational' ||
      //   this.photoType == 'bankCardPicBack'
      // ) {
      //   // 身份证反面 银行卡反面
      //   this.uploadCard(item)
      // }
      this.src = item
    },

    // 身份证正面/银行卡 ocr识别
    ocrCard(item) {
      uni.uploadFile({
        url:
          this.photoType == 'idCardCopy'
            ? getApp().globalData.globalOcrCardUrl
            : getApp().globalData.globalOcrBankUrl,
        filePath: item,
        header: {
          'Blade-Auth': 'bearer ' + this.tokens,
          'Tenant-Id': '000000',
          'Blade-Requested-With': 'BladeHttpRequest'
        },
        name: 'file',
        complete: res => {
          console.log('res', JSON.parse(res.data))
          let link = JSON.parse(res.data)
          if (link.code == 200) {
            if (this.photoType == 'idCardCopy') {
              uni.$emit('onOcrCard', link)
            } else {
              uni.$emit('onOcrBank', link)
            }
            uni.navigateBack({
              delta: 1
            })
          } else {
            uni.showToast({
              title: link.msg,
              icon: 'none'
            })
          }
        }
      })
    },

    //  身份证反面/银行卡反面上传
    uploadCard(item) {
      uni.uploadFile({
        url: getApp().globalData.uploadUrl,
        filePath: item,
        name: 'file',
        header: {
          'Blade-Auth': 'bearer ' + this.tokens,
          'Tenant-Id': '000000',
          'Blade-Requested-With': 'BladeHttpRequest'
        },
        success: res => {
          let data = JSON.parse(res.data)
          if (this.photoType == 'idCardNational') {
            uni.$emit('onCard', data)
          } else {
            uni.$emit('onBank', data)
          }
          uni.navigateBack({
            delta: 1
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.top-box {
  height: 200rpx;
  background-color: #000;
}
.camera-box {
  display: flex;
  align-items: center;
  justify-content: center;
  // flex-direction: column;
}

.camera-con {
  width: 520rpx;
  height: 100%;
}

.camera-bgcolor {
  // width: 66%;
  width: 520rpx;
  height: 100%;
}
.center {
  width: 100%;
  height: 750rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.center-top {
  width: 100%;
  height: calc((100% - 750rpx) / 2);
  background-color: rgba(248, 248, 248, 0.6);
}

.center-bottom {
  width: 100%;
  height: calc((100% - 750rpx) / 2);
  background-color: rgba(248, 248, 248, 0.6);
}

.bottom {
  width: 100%;
  background-color: #000;
}

.wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 320rpx;
  padding: 0 73rpx;
}

.take-box {
  width: 131rpx;
  height: 131rpx;
}

.bottom-box {
  font-size: 36rpx;
  color: #fff;
}

.controls {
  display: flex;
  align-items: center;
  justify-content: center;
  // flex-direction: column;
  .center {
    width: 80%;
    height: 380rpx;
    border: 1px dashed #fff;
    border-radius: 10rpx;
    color: #fff;
    text-align: center;
  }
}

.controls1-bgcolor {
  list-style: none;
  padding: 0;
  margin: 0;
  // width: 22%;
  width: calc((100% - 522rpx) / 2);
  height: 100%;
  background-color: rgba(248, 248, 248, 0.6);
}

.camera-bgcolor {
  // width: 66%;
  width: 520rpx;
  height: 100%;
}
.center {
  width: 100%;
  height: 750rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.center-top {
  width: 100%;
  height: calc((100% - 750rpx) / 2);
  background-color: rgba(248, 248, 248, 0.6);
}

.center-bottom {
  width: 100%;
  height: calc((100% - 750rpx) / 2);
  background-color: rgba(248, 248, 248, 0.6);
}

.cover_img {
  width: 520rpx;
}

.controls3-bgcolor {
  list-style: none;
  padding: 0;
  margin: 0;
  width: calc((100% - 520rpx) / 2);
  height: 100%;
  background-color: rgba(248, 248, 248, 0.6);
}

.cop {
  width: 100%;
  height: 100vh;
}
</style>
