<template>
  <div>
    <view class="price"> ¥ {{ info.withdraw_amount }} </view>
    <view class="step">
      <view
        class="item"
        :class="{
          current: stepIndex == index,
          infront: index <= stepIndex
        }"
        v-for="(item, index) of step"
      >
        <view class="xian"></view>
        <view class="icon">
          <image
            v-if="stepIndex == index"
            class="img2"
            src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/290a17442702366825753_Frame%402x.png"
          ></image>
          <view v-else class="img1"></view>
        </view>
        <view class="text">
          <view class="name">{{ item.name }}</view>
          <view v-if="item.tips" class="tips">{{ item.tips }}</view>
        </view>
      </view>
    </view>
    <view>
      <view class="form-item">
        <image src="/static/imgs/kaolaStudent/wxlogo.png"></image>
        <view>提现至微信</view>
        <!-- <view>招商银行 尾号1298</view> -->
      </view>

      <view class="form-item">
        <image
          src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/1237174426969391968768_Frame%402x(2).png"
        ></image>
        <view>服务费</view>
        <view>¥ 0.00</view>
      </view>
    </view>
    <view class="pay-but" @click="$xh.back()">完成</view>
  </div>
</template>

<script>
import { account, student, config } from '../../../api/index.js'
import { app_id, mchId } from '../../../config'
export default {
  data() {
    return {
      query: {},
      stepIndex: 2,
      step: [],
      info: {
        withdraw_amount: 0
      }
    }
  },
  onLoad(query) {
    this.query = query
    this.withdrawDetail()
  },
  methods: {
    withdrawDetail() {
      account.withdraw
        .detail({
          apply_id: this.query.id
        })
        .then(res => {
          this.info = res.data
          this.step = res.data.withdraw_flows.map(e => {
            if (e.id == '1' && this.info.status == '1') {
              e.tips = this.info.audite_note
            }
            if (e.id == '2' && this.info.status == '2') {
              e.tips = this.info.transfer_status_name
            }
            return e
          })
          this.stepIndex = this.info.status
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.pay-but {
  position: fixed;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
  width: 231px;
  text-align: center;
  height: 44px;
  line-height: 44px;
  background: #222333;
  border-radius: 33px 33px 33px 33px;
  font-weight: bold;
  font-size: 14px;
  color: #ffffff;
}
.step {
  margin-bottom: 16px;
  margin: 0 12px;
  // width: 351px;
  // height: 278px;
  background: #ffffff;
  border-radius: 12px 12px 12px 12px;
  padding-top: 20px;
  padding-bottom: 20px;
  .item {
    display: flex;
    // height: 53px;
    // align-items: center;
    padding-bottom: 40px;
    position: relative;
    .xian {
      position: absolute;
      left: 23px;
      top: 12px;
      height: 100%;
      border-right: 1px dashed #9799a1;
    }
    .icon {
      padding-top: 6px;
      width: 53px;
      position: relative;
      .img1 {
        margin-left: 20px;
        width: 7px;
        height: 7px;
        background: #9799a1;
        border-radius: 50%;
        // border-radius: 0px 0px 0px 0px;
        // border: 2px solid #ffffff;
      }
      .img2 {
        width: 25px;
        height: 25px;
        margin-left: 12px;
      }
    }
    .text {
      .name {
        font-weight: 400;
        font-size: 14px;
        color: rgba(34, 35, 51, 0.45);
        line-height: 20px;
      }
    }
  }
  .current {
    .text {
      .name {
        font-weight: 500;
        font-size: 16px;
        color: #222333;
        line-height: 19px;
      }
      .tips {
        margin-top: 8px;
        font-weight: 400;
        font-size: 14px;
        color: rgba(34, 35, 51, 0.45);
        line-height: 20px;
      }
    }
  }
  .infront {
    .icon {
      padding-top: 6px;
      width: 53px;
      .img1 {
        margin-left: 20px;
        width: 7px;
        height: 7px;
        background: #222333;
        // border-radius: 0px 0px 0px 0px;
        // border: 2px solid #ffffff;
      }
    }
  }
  .item:nth-last-child(1) {
    .xian {
      display: none;
    }
    padding-bottom: 10px;
  }
}
.price {
  font-weight: 500;
  font-size: 32px;
  color: #222333;
  line-height: 38px;
  padding: 25px 0;
  text-align: center;
}
.form-item {
  display: flex;
  align-items: center;
  padding: 8px 0 8px 12px;
  image {
    width: 20px;
    height: 20px;
    margin-right: 4px;
  }
  view {
    font-weight: 400;
    font-size: 12px;
    color: #222333;
    line-height: 20px;
    margin-right: 8px;
  }
  view:nth-child(3) {
    color: rgba(34, 35, 51, 0.65);
  }
}
</style>
