<template>
  <div class="pageList">
    <view class="top">
      <navbar class="navbar" :title="query.title + '错题'"></navbar>
      <view class="serach">
        <pickerSelect
          v-model="search.original_type"
          :options="[
            { name: '全部', id: '0' },
            { name: 'AI答疑', id: '1' },
            { name: '清北答疑', id: '2' }
          ]"
          @change="searchChage"
        >
          <view class="item">
            <image
              class="img2"
              src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/4e3617442676697245229_Union%402x.png"
            ></image>
            <image
              class="img1"
              src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/5bc8174438409377474365_Frame%402x(15).png"
            ></image>
            <view>{{ search.name || '筛选' }}</view>
            <image
              class="img3"
              src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/6ace174418173457834915_Frame%2012%402x.png"
            ></image>
          </view>
        </pickerSelect>
      </view>
    </view>

    <pageList
      class="scrollpageList"
      :key="list.length"
      :loading="search.loading"
      @nextPage="getList"
      @refresh="refresh"
    >
      <view class="list" :key="list.length">
        <noData v-if="list.length == 0"></noData>
        <view class="item" v-for="item of list" :key="item.id">
          <view class="head">
            <view class="l">
              <!-- item.teacher_img -->
              <image
                class="avatar"
                :src="$xh.completepath(avatar.teacher)"
              ></image>
              <view class="name">{{ item.teacher_name }}</view>
              <view class="label" v-if="item.original_type == '2'"
                >清华大学</view
              >
              <image
                v-if="item.original_type == '1'"
                class="label2"
                src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/4b52174438389535476119_Group%20138%402x.png"
              ></image>
            </view>
            <view class="r" v-if="false">
              <image
                class="icon"
                src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/6a4e174438378278527840_Frame%402x(7).png"
              ></image>
              <view class="text">再来一单</view>
            </view>
          </view>
          <view v-if="item.question.message_type == 'text'" class="question">
            <text class="ellipsis"> {{ item.question.message }}</text>
          </view>
          <view
            v-if="item.question.message_type == 'image'"
            class="question ellipsis"
          >
            <image
              mode="center"
              show-menu-by-longpress
              :src="item.question.message"
              @click="previewImage(item.question.message)"
            ></image>
          </view>
          <view class="label-box">
            <view class="label-item">
              <image
                src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/1614174438380400214501_Frame%402x(3).png"
              ></image>
              <view>答疑时间</view>
              <view>{{ item.created_at }}</view>
            </view>
            <view class="label-item" v-if="item.original_type == '2'">
              <image
                src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/3a35174438382134844631_Frame%402x(9).png"
              ></image>
              <view>答疑时长</view>
              <view>{{ item.effective_time }}分钟</view>
            </view>
          </view>
          <view class="bottom">
            <view class="price" v-if="item.original_type == '2'"
              >¥ {{ item.order_amount }}</view
            >
            <view v-else></view>
            <view class="but-group">
              <view class="but" @click="example(item, 1)">
                <image
                  src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/65e4174438383970658330_Frame%402x(5).png"
                ></image>
                <view>举一反三</view>
              </view>
              <view
                class="but"
                v-if="item.original_type == '2'"
                @click="replay(item)"
              >
                <image
                  src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/2566174438385345699780_Frame%402x(6).png"
                ></image>
                <view>答疑回看</view>
              </view>
              <view
                class="but"
                @click="example(item, 2)"
                v-if="item.original_type == '1'"
              >
                <image
                  src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/2566174438385345699780_Frame%402x(6).png"
                ></image>
                <view>学情诊断</view>
              </view>
            </view>
          </view>
        </view>
        <view style="height: 40px"></view>
      </view>
    </pageList>
  </div>
</template>

<script>
import navbar from '../../../components/commen/navbar.vue'
import { student } from '../../../api/index'
import pageList from '../../../components/commen/page-list.vue'
import pickerSelect from '../../../components/commen/picker-select.vue'
import noData from '../../../components/commen/no-data.vue'
import { avatar } from '../../../config'
import dayjs from 'dayjs'
export default {
  components: {
    pageList,
    pickerSelect,
    navbar,
    noData
  },
  data() {
    return {
      avatar,
      search: {
        original_type: '',
        name: '',
        page: 1,
        size: 10,
        loading: false
      },
      list: [],
      query: {
        title: ''
      }
    }
  },
  onLoad(query) {
    this.query = query
    wx.setNavigationBarTitle({
      title: query.title + '错题'
    })
    this.refresh()
  },
  methods: {
    replay(item) {
      if (item.replay_url) {
        wx.previewMedia({
          sources: [
            {
              url: item.replay_url,
              type: 'video'
            }
          ],
          fail(err) {
            console.log(err)
          }
        })
      } else {
        this.$xh.Toast('暂未生成回放地址')
      }
    },
    searchChage(e) {
      if (e.id == '0') {
        this.search.name = ''
      } else {
        this.search.name = e.name
      }
      this.refresh()
    },
    example(item, type) {
      if (type == '1') {
        this.$xh.push(
          'kaolaStudent',
          `pages/my/footprint/example?id=${item.id}&type=1&title=举一反三`
        )
      }
      if (type == '2') {
        this.$xh.push(
          'kaolaStudent',
          `pages/my/footprint/example?id=${item.id}&type=2&title=学情诊断`
        )
      }
    },
    refresh() {
      this.search.page = 1
      this.list = []
      this.getList()
    },
    getList() {
      this.search.loading = true
      student.question
        .getList({
          state_id: this.query.state_id,
          grade_id: this.query.grade_id,
          subject_id: this.query.subject_id,
          original_type:
            this.search.original_type > 0 ? this.search.original_type : '',
          page: this.search.page++,
          size: this.search.size
        })
        .then(res => {
          if (res.data?.list?.length) {
            this.list = this.list.concat(
              res.data.list.map(item => {
                item.created_at = dayjs(item.created_at).format(
                  'YYYY/MM/DD HH:mm'
                )
                return item
              })
            )
          }
          this.search.loading = false
        })
        .catch(() => {
          this.search.loading = false
        })
    },
    previewImage(url) {
      wx.previewImage({
        current: url,
        urls: this.list
          .filter(item => item.question.message_type == 'image')
          .map(e => e.question.message)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.list {
  .item {
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;
    padding: 0 12px;
    margin: 0px 12px 12px 12px;
    .bottom {
      height: 56px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .price {
        font-weight: 800;
        font-size: 14px;
        color: #222333;
        line-height: 20px;
      }
      .but-group {
        display: flex;
        align-items: center;
        .but {
          width: 88px;
          height: 32px;
          background: #222333;
          border-radius: 8px 8px 8px 8px;
          margin-left: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          image {
            width: 20px;
            height: 20px;
          }
          view {
            font-weight: 400;
            font-size: 12px;
            color: #ffffff;
          }
        }
      }
    }
    .label-box {
      display: flex;
      align-items: center;
      border-bottom: 1px dashed rgba(214, 219, 227, 0.65);
      .label-item {
        padding-top: 8px;
        padding-bottom: 8px;
        display: flex;
        align-items: center;
        margin-right: 24px;
        flex-shrink: 0;
        image {
          width: 20px;
          height: 20px;
        }
        view:nth-child(2) {
          font-weight: 400;
          font-size: 12px;
          color: #222333;
          line-height: 20px;
          margin-right: 4px;
        }
        view:nth-child(3) {
          font-weight: 400;
          font-size: 12px;
          color: rgba(34, 35, 51, 0.35);
          line-height: 20px;
        }
      }
    }
    .question {
      font-weight: 400;
      font-size: 14px;
      color: rgba(34, 35, 51, 0.85);
      line-height: 20px;
      width: 100%;
      background: #f7f9fb;
      border-radius: 4px 4px 4px 4px;
      padding: 8px 12px;
      image {
        width: 100%;
        height: 80px;
      }
    }
    .head {
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .l {
        display: flex;
        align-items: center;
        .avatar {
          width: 28px;
          height: 28px;
          border-radius: 50%;
        }
        .name {
          font-weight: 500;
          font-size: 15px;
          color: #222333;
          line-height: 20px;
          margin: 0 6px;
        }
        .label {
          width: 56px;
          text-align: center;
          height: 20px;
          line-height: 20px;
          background: #c2f607;
          border-radius: 0px 0px 0px 0px;
          font-weight: bold;
          font-size: 12px;
          color: #222333;
        }
        .label2 {
          width: 18px;
          height: 18px;
        }
      }
      .r {
        display: flex;
        align-items: center;
        .icon {
          width: 20px;
          height: 20px;
        }
        .text {
          font-weight: 400;
          font-size: 14px;
          color: rgba(34, 35, 51, 0.85);
          line-height: 20px;
        }
      }
    }
  }
}
.pageList {
  height: 100vh;
  overflow: hidden;
  width: 100vw;
  display: flex;
  flex-direction: column;
  .scrollpageList {
    flex: 1;
    height: 500px;
  }
}
.top {
  padding-bottom: 12px;
}
.serach {
  display: flex;
  justify-content: space-between;
  padding: 0 12px 0px 12px;

  // background-color: #fff;
  z-index: 1;
  .item {
    min-width: 96px;
    height: 36px;
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;
    display: flex;
    position: relative;
    align-items: center;
    .img1 {
      margin-left: 12px;
      margin-right: 2px;
      width: 20px;
      height: 20px;
    }
    .img2 {
      position: absolute;
      left: 0;
      top: 0;
      width: 32px;
      height: 26px;
    }
    .img3 {
      margin-left: auto;
      width: 10px;
      height: 10px;
      margin-right: 12px;
    }
    view {
      font-weight: 400;
      font-size: 14px;
      color: #222333;
    }
  }
}
</style>
