<template>
  <view class="wait-page">
    <view class="background">
      <image
        class="close"
        @click="$xh.back()"
        src="/static/imgs/kaolaStudent/navbarback.png"
      ></image>

      <view style="height: 56.13vw"></view>
      <view class="teacher-info">
        <image
          class="backimg"
          src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/6e64174563071733084601_Group%20269%402x.png"
        ></image>
        <view class="styleList">
          <view class="ee" v-if="info.teach_sytle_name">{{
            info.teach_sytle_name
          }}</view>
        </view>
        <view class="avatar">
          <image class="img1" :src="$xh.completepath(info.avatar)"></image>
          <image
            v-if="info.sex == 1"
            class="img2"
            src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/3ab5174563172190027036_Group%20257%402x.png"
          ></image>
          <image
            v-if="info.sex == 2"
            class="img2"
            src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/8b74174563170016061969_Group%20257%402x(1).png"
          ></image>
        </view>

        <view class="teacher_name">
          <view class="name">{{ info.name }}</view>
        </view>

        <view class="xinglabel">
          <view class="xing">
            <image
              src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/38a1174555253755312367_Star%2011%402x.png"
            ></image>
            <view>{{ info.lesson_score }}</view>
          </view>
          <image
            class="label"
            :src="$xh.completepath(info.school_logo)"
          ></image>
        </view>
        <view class="text" v-if="info.introduce">
          {{ info.introduce }}
        </view>

        <view class="label-box">
          <view class="item">
            <image
              src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/1456174563030489247359_Group%20258%402x.png"
            ></image>
            <view>{{ info.ranking_id_name }}</view>
          </view>
          <view class="item" style="width: 80px">
            <image
              src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/4e7717456304756695113_Group%20259%402x.png"
            ></image>
            <view>{{ info.score }}分</view>
          </view>
          <view class="item" style="width: 80px">
            <image
              src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/75f3174563049189437730_Group%20260%402x.png"
            ></image>
            <view>{{ info.question_num }}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { config, appoint } from '../../api/index'

export default {
  data() {
    return {
      info: {},
      time: 5,
      query: {}
    }
  },
  onLoad(query) {
    this.query = query

    this.detail()
  },

  methods: {
    detail() {
      appoint.teacher
        .applyJoinDetail({
          // employee_id: '561855922658350517'
          employee_id: this.query.teacher_id
        })
        .then(res => {
          this.info = res.data
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.page-bottom-but-fixed {
  display: flex;
  flex-direction: column;
  align-items: center;

  .tips {
    font-weight: 400;
    font-size: 13px;
    color: #708600;
    line-height: 20px;
    margin-bottom: 32px;
    white-space: pre;
  }
}
.label-box {
  margin: 0 20px;
  padding-top: 38px;
  padding-bottom: 32px;
  border-top: 1px dashed #e2eea9;
  display: flex;
  align-items: center;
  .item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 103px;
    height: 40px;
    background: #fbffe7;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #222333;
    position: relative;
    margin: 0 4px;
    image {
      position: absolute;
      top: -15px;
      left: 0;
      width: 43.7px;
      height: 29.84px;
    }
    view {
      font-weight: 400;
      font-size: 14px;
      color: #222333;
    }
  }
}
.teacher-info {
  margin: 0 24px;
  // width: 100%;
  // height: 400px;
  // background: linear-gradient(180deg, #d4ff00 0%, #ffffff 100%);
  background-color: #fff;
  box-shadow: 0px 29px 45px -13px rgba(111, 133, 5, 0.2);
  border-radius: 24px 24px 24px 24px;
  position: relative;
  display: flex;
  flex-direction: column;
  // padding-top: 60px;
  .backimg {
    position: absolute;
    left: 0;
    top: -56.13vw;
    width: 100%;
    height: 92.26666vw;
  }
  .styleList {
    position: absolute;
    display: flex;
    flex-wrap: wrap;
    .ee {
      width: 80px;
      text-align: center;
      height: 32px;
      line-height: 32px;
      background: rgba(255, 255, 255, 0.5);
      border-radius: 26px 26px 26px 26px;
      border: 1px solid #ffffff;
      font-weight: 400;
      font-size: 12px;
      color: #404b07;
      margin-left: 24px;
    }
  }
  .xinglabel {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 10px;
    padding-bottom: 16px;
    .xing {
      display: flex;
      align-items: center;
      image {
        width: 16.17px;
        height: 15px;
      }
      view {
        font-weight: 500;
        font-size: 15px;
        color: #fb8105;
        line-height: 20px;
      }
    }
    .label {
      margin-left: 6px;
      width: 77px;
      height: 23px;
    }
  }
  .teacher_name {
    position: relative;
    margin: 0 auto;
    display: flex;
    align-items: center;
    padding-top: 18px;
    // padding-bottom: 18px;
    .name {
      font-weight: bold;
      font-size: 20px;
      color: #222333;
      margin-right: 5px;
    }
    .label {
      font-weight: bold;
      font-size: 14px;
      color: #222333;
      width: 68px;
      text-align: center;
      height: 24px;
      line-height: 24px;
      background: #d4ff00;
      border-radius: 4px 4px 4px 4px;
    }
  }
  .text {
    font-weight: 400;
    font-size: 14px;
    color: rgba(34, 35, 51, 0.85);
    line-height: 20px;
    margin: 0 24px;
    padding-bottom: 28px;
  }
  .avatar {
    position: relative;
    margin: 0 auto;
    width: 80px;
    height: 80px;
    background: #999faa;
    border: 4px solid #222333;
    border-radius: 50%;
    // overflow: hidden;
    position: relative;
    .img1 {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      overflow: hidden;
    }
    .img2 {
      position: absolute;
      bottom: -4px;
      right: -4px;
      width: 28px;
      height: 28px;
    }
  }
}
.wait-page {
  width: 100vw;
  height: 100vh;
  // width: 375px;
  // height: 812px;
  // background: linear-gradient(180deg, #d4ff00 0%, #ffffff 100%), #ffffff;
  // position: relative;
  .background {
    height: 100%;
    width: 100%;
    // background-image: url(https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/3cac174557323293588407_Group%20256%402x.png);
    background-size: 100vw 157.86vw;
    background-repeat: no-repeat;
  }
  // border-radius: 0px 0px 0px 0px;
  .content {
    display: flex;
    flex-direction: column;
  }
  .close {
    position: fixed;
    left: 0;
    top: 40px;
    border: 16px solid rgba(111, 133, 5, 0);
    width: 16px;
    height: 20px;
    border-radius: 0px 0px 0px 0px;
  }
}
</style>
