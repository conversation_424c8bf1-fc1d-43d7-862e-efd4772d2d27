<template>
  <view class="week">
    <view class="month">
      <view class="but" @click="last">
        <image src="/static/imgs/kaolaStudent/monthButIcon.svg"></image
      ></view>
      <view>{{ month }}</view>
      <view class="but next" @click="next">
        <image src="/static/imgs/kaolaStudent/monthButIcon.svg"></image>
      </view>
    </view>
    <weekHead ref="week" :date="date" @change="change"> </weekHead>
    <view class="week-body">
      <view class="back">
        <view class="border" v-for="item of borderHieght"></view>
      </view>
      <view class="day-col" v-for="item of timeSlot" :key="item.date">
        <view
          class="item"
          v-for="ee of item.list"
          @click="tap(ee)"
          :key="ee.id"
        >
          <view class="name">{{ ee.subject_name || '科目' }}</view>
          <view class="name">{{ ee.teacher_name }}</view>
          <view class="time">
            <view>{{ ee.start_time }} </view>
            <view>- </view>
            <view>{{ ee.end_time }}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import dayjs, { Dayjs } from 'dayjs'
import { appoint } from '../../../api/index'
import weekHead from './weekHead.vue'
function timeToMinutes(timeStr) {
  const [hours, minutes] = timeStr.split(':').map(Number)
  return hours * 60 + minutes
}

function isTimeInRange(targetTime, startTime, endTime) {
  const target = timeToMinutes(targetTime)
  const start = timeToMinutes(startTime)
  const end = timeToMinutes(endTime) - 1

  if (start <= end) {
    // 时间段在同一天
    return target >= start && target <= end
  } else {
    // 时间段跨天
    return target >= start || target <= end
  }
}
export default {
  components: {
    weekHead
  },
  props: {
    pageShow: {
      type: Boolean,
      default: true
    },
    currentDate: {
      type: String,
      default: ''
    },
    selectDate: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      hasLoadedInitialData: false, // 新增状态，防止重复加载
      styleName: {
        shuxue: '数学',
        yingyu: '英语',
        zhengzhi: '政治',
        wuli: '物理'
      },
      timeSlot: [],
      week: [],
      list: [],
      borderHieght: 0,
      date: ''
    }
  },
  computed: {
    token() {
      return this.$store.state.kaolaStudent.token;
    },
    month() {
      return dayjs(this.currentDate).format('YYYY-MM')
    }
  },
  created() {
    this.date = this.currentDate;
    // 如果 pageShow 为 true 且 token 存在，则在 created 时加载一次
    // week.vue 的数据加载依赖于 weekHead 的 change 事件，所以这里不直接调用 my()
    // change 方法会在 weekHead 初始化或改变时被调用，届时会调用 my()
  },
  watch: {
    pageShow(val) {
      // 当 pageShow 变为 true 且 token 存在，并且尚未加载过初始数据时，加载数据
      // week.vue 的数据加载依赖于 weekHead 的 change 事件触发的 my() 调用
      // 这里确保在页面显示且 token 存在时，如果 change 尚未触发或未成功加载，可以考虑强制刷新
      if (val && this.token && !this.hasLoadedInitialData && this.week && this.week.length > 0) {
         // 如果 week 数据已通过 change 事件设置，但 my() 可能因 token 未及时获取而失败
         this.my(); 
         this.hasLoadedInitialData = true;
      }
    },
    token(newToken) {
      // 当 token 从无到有时，且 pageShow 为 true，并且尚未加载过初始数据时，加载数据
      // week.vue 的数据加载依赖于 weekHead 的 change 事件触发的 my() 调用
      if (newToken && this.pageShow && !this.hasLoadedInitialData && this.week && this.week.length > 0) {
        this.my();
        this.hasLoadedInitialData = true;
      }
    }
  },

  methods: {
    last() {
      this.$refs.week.last()
    },
    next() {
      this.$refs.week.next()
    },
    tap(item, type) {
      this.$emit('update:selectDate', item.date)
      this.$emit('select')
    },
    getData(time, week) {
      let data = this.list
        .filter(e => e.date == week.date)
        .filter(e => isTimeInRange(e.start_time, time.start, time.end))

      if (data.length) {
        return data[0]
      } else {
        return {
          teacher_name: '',
          subject_id_name: '',
          id: ''
        }
      }
    },
    change(week) {
      this.week = week
      this.$emit('update:currentDate', dayjs(week[0].date).format('YYYY-MM-DD'))
      this.timeSlot = week.map(item => {
        item.list = []
        return item
      })

      this.my()
    },
    my() {
      if (!this.token) {
        console.warn('Week.vue: Token is not available, skipping data fetch.');
        return;
      }
      appoint.student
        .my({
          start: this.week[0].date,
          end: this.week[6].date
        })
        .then(res => {
          let list = res.data || []
          list.forEach(e => {
            e.teacher_name = e.teacher_name.substring(0, 3)
            return e
          })
          this.timeSlot.forEach(item => {
            for (let ee of list) {
              if (dayjs(item.date).format('YYYY-MM-DD') == ee.date) {
                item.list.push(ee)
              }
            }
          })
          let len = 0
          this.timeSlot.forEach(e => {
            if (e.list.length > len) {
              len = e.list.length
            }
          })
          this.borderHieght = len
          this.timeSlot = JSON.parse(JSON.stringify(this.timeSlot))

          console.log(this.timeSlot, 'timeSlot')
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.week {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  position: relative;
  .month {
    position: absolute;
    left: 12px;
    top: -44px;
    z-index: 1;
    width: 121px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    border-radius: 19px 19px 19px 19px;
    border: 1px solid rgba(223, 241, 134, 0.7);
    font-weight: 500;
    font-size: 14px;
    color: #222333;
    padding: 0 4px;
    .but {
      align-items: center;
      display: flex;
      height: 100%;
      padding: 0 6px 0 6px;
      image {
        width: 10px;
        height: 10px;
      }
    }
    .next {
      image {
        transform: rotateY(180deg);
      }
    }
  }
  .back {
    position: absolute;
    z-index: 0;
    left: 0;
    top: 0;
    width: 100%;
    .border {
      height: 98px;
      border-bottom: 1px dashed rgba(34, 35, 51, 0.1);
    }
  }
  .week-body {
    flex: 1;
    height: 200px;
    overflow-y: auto;
    display: flex;
    padding: 0 2px;
    position: relative;
    .day-col {
      position: relative;
      z-index: 1;
      width: calc(100% / 7);
      display: flex;
      flex-direction: column;
      .item {
        margin: 4px auto 0 auto;
        width: calc(100% - 4px);
        // width: 49px;
        height: 94px;
        background: #e5e5fc;
        border-radius: 8px 8px 8px 8px;
        border: 1px solid #b7b7ff;
        padding-top: 8px;
        justify-content: space-between;
        .name {
          text-align: center;
          width: calc(100% - 8px);
          margin: 0 auto;
          font-weight: 500;
          font-size: 10px;
          color: #4040b1;
        }
        .name:nth-child(1) {
          margin-bottom: 4px;
          font-size: 12px;
        }
        .name:nth-child(2) {
          margin-bottom: 6px;
          font-size: 10px;
        }
        .time {
          margin: 4px;
          margin-top: 5px;
          height: 42px;
          background: #ffffff;
          border-radius: 4px 4px 4px 4px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          view {
            font-weight: 400;
            font-size: 10px;
            color: #4040b1;
            line-height: 10px;
          }
        }
      }
      .shuxue {
        background: #e5e5fc;
        border: 1px solid #b7b7ff;
        .name {
          color: #4040b1;
        }
        .time {
          view {
            color: #4040b1;
          }
        }
      }

      .yingyu {
        background: #e5f3fe;
        border: 1px solid #9dd4ff;
        .name {
          color: #0083ea;
        }
        .time {
          view {
            color: #0083ea;
          }
        }
      }

      .zhengzhi {
        background: #ffeed3;
        border: 1px solid #ffdba2;
        .name {
          color: #de8700;
        }
        .time {
          view {
            color: #de8700;
          }
        }
      }

      .wuli {
        background: #ffeef1;
        border: 1px solid #ffd9df;
        .name {
          color: #f96a83;
        }
        .time {
          view {
            color: #f96a83;
          }
        }
      }
    }
  }
}
</style>
