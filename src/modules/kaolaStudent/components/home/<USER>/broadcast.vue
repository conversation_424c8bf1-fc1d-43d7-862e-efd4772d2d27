<template>
  <view></view>
  <!-- <view
    class="tips"
    :style="{
      top: topsTop + 'px'
    }"
  >
    <image src="/static/imgs/kaolaStudent/home_tips.png"></image>
    <view>今日 123名清北名师 正在答疑</view>
  </view> -->
</template>

<script>
export default {
  data() {
    return {
      topsTop: 25 + 50 + 11
    }
  },
  created() {
    let BarHeight = wx.getSystemInfoSync().statusBarHeight || 25
    this.topsTop = BarHeight + 50 + 11
  }
}
</script>

<style lang="scss" scoped>
.tips {
  position: fixed;
  left: 16px;
  // top: 11px;
  // width: 197px;
  height: 24px;
  background: rgba(34, 35, 51, 0.5);
  border-radius: 12px;
  padding: 0 15px 0 4px;
  display: flex;
  align-items: center;
  z-index: 1;
  image {
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }
  view {
    font-weight: 400;
    font-size: 12px;
    color: #ffffff;
  }
}
</style>
