<template>
  <view>
    <picker
      mode="selector"
      :range="options"
      range-key="name"
      confirmColor="#ff6616"
      :value="defaultIndex"
      @change="change"
    >
      <slot></slot>
    </picker>
  </view>
</template>

<script>
import { config } from '../../api'
export default {
  props: {
    value: {
      type: String,
      default: ''
    },
    code: {
      type: String,
      default: ''
    },
    allName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      defaultIndex: 0,
      options: []
    }
  },
  mounted() {
    config.base
      .ykmap({
        code: this.code,
        is_usable: '1'
      })
      .then(res => {
        if (this.allName) {
          this.options = [{ id: '', name: this.allName }].concat(res.data.list)
        } else {
          this.options = res.data.list
        }

        this.init()
      })
  },
  watch: {
    value() {
      this.init()
    }
  },
  methods: {
    init() {
      if (this.options.length) {
        if (this.value.length) {
          let index1 = this.options.findIndex(item => item.id == this.value)
          if (index1 == -1) {
            index1 = 0
          }
          this.defaultIndex = index1
        }
      }
    },
    change(el) {
      let value = el.detail.value
      let one = this.options[value]
      this.defaultIndex = value
      this.$emit('input', one.id)
      this.$emit('update:name', one.name)
      this.$emit('change', {
        id: one.id,
        name: one.name
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
