<template>
  <div>
    <image
      :mode="imageMode"
      :style="imageStyle"
      show-menu-by-longpress
      :src="src"
      @load="onImageLoad"
      @click="previewImage(src)"
    ></image>
  </div>
</template>

<script>
export default {
  props: {
    src: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      imageMode: 'heightFix',
      imageStyle: 'height: 35.73vw;'
    }
  },
  computed: {
    chattingList() {
      return this.$store.getters['kaolaStudent/getChattingList']
    }
  },
  methods: {
    onImageLoad(event) {
      const { width, height } = event.detail
      console.log(width, height)
      let num = Math.floor((134 / 375) * 100)
      if (width > height) {
        this.imageMode = ''
        this.imageStyle = `width: ${num}vw;height: ${Math.floor(
          num * (height / width)
        )}vw;`
      } else {
        this.imageMode = ''
        this.imageStyle = `width: ${Math.floor(
          num * (width / height)
        )}vw;height: ${num}vw;`
      }
    },
    previewImage(url) {
      wx.previewImage({
        current: url,
        urls: this.chattingList
          .filter(item => item.source == 'user' && item.type == 'image')
          .map(e => e.content.content)
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
