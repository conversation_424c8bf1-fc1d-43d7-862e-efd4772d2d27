<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Group 61" filter="url(#filter0_d_139_2671)">
<rect id="Rectangle 93" x="1" y="9" width="18" height="2" fill="white"/>
<rect id="Rectangle 94" x="11" y="1" width="18" height="2" transform="rotate(90 11 1)" fill="white"/>
</g>
<defs>
<filter id="filter0_d_139_2671" x="0" y="0" width="20" height="20" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_139_2671"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_139_2671" result="shape"/>
</filter>
</defs>
</svg>
