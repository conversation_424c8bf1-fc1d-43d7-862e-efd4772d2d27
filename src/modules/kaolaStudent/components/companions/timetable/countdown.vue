<template>
  <view class="countdown">
    <text v-if="timeOver" class="status">已结束</text>
    <view v-else class="time-box">
      <text style="margin-right: 4px">距离上课还有</text>
      <text class="time-item" v-if="days != '00'">{{ days }}天</text>
      <text class="time-item">{{ hours }}时</text>
      <text class="time-item">{{ minutes }}分 </text>
      <text class="time-item">{{ seconds }}秒</text>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    endTime: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      days: '00',
      hours: '00',
      minutes: '00',
      seconds: '00',
      timeOver: false,
      timer: null
    }
  },
  mounted() {
    this.startCountdown()
  },
  beforeDestroy() {
    clearInterval(this.timer)
  },
  methods: {
    startCountdown() {
      this.timer = setInterval(() => {
        this.updateTime()
      }, 1000)
    },
    updateTime() {
      const end = new Date(this.endTime.replace(/-/g, '/'))
      const now = new Date()
      const diff = end.getTime() - now.getTime()

      if (diff <= 0) {
        this.$emit('finish')
        this.timeOver = true
        clearInterval(this.timer)
        return
      }

      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      const hours = Math.floor(
        (diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
      )
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = Math.floor((diff % (1000 * 60)) / 1000)

      this.days = days.toString().padStart(2, '0')
      this.hours = hours.toString().padStart(2, '0')
      this.minutes = minutes.toString().padStart(2, '0')
      this.seconds = seconds.toString().padStart(2, '0')
    }
  }
}
</script>

<style scoped lang="scss">
.countdown {
  margin-left: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  .text {
    font-weight: 400;
    font-size: 12px;
    color: rgba(34, 35, 51, 0.6);
  }
  .time-item {
    font-weight: 400;
    font-size: 12px;
    color: #ff6111;
  }
}

.time-box {
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 12px;
  color: rgba(34, 35, 51, 0.6);
}
.status {
  font-weight: 400;
  font-size: 12px;
  color: rgba(34, 35, 51, 0.6);
}
/* 可根据需要自定义样式 */
</style>
