const webpack = require('webpack')
const WebpackBar = require('webpackbar');
let progressPlugin = new WebpackBar({
  color: "#85d",  // 默认green，进度条颜色支持HEX
  basic: false,   // 默认true，启用一个简单的日志报告器
  profile:false,  // 默认false，启用探查器。
})

const path = require('path')
function resolve(dir) {
  return path.join(__dirname, dir)
}
module.exports = {
  transpileDependencies: ['uview-ui','@dcloudio/uni-ui', '@rojer/katex-mini'],
  configureWebpack: {
    plugins: [
      // new webpack.ProvidePlugin({
      //   // 对库做全局别名处理
      //   localStorage: ['mp-storage', 'localStorage'],
      //   'window.localStorage': ['mp-storage', 'localStorage'],
      // }),
      progressPlugin
    ]
  },
  chainWebpack: config => {
    config.resolve.alias
      .set('@utlis', resolve('/src/utlis'))
      .set('@', resolve('/src'))
      .set('@api', resolve('/src/api'))
      .set('@components', resolve('/src/components'))
      .set('util', require.resolve('util/')) // Ensure 'util' package is installed
    // config.plugin('define').tap(args => {
    //   // 配置全局环境变量 例如：
    //   args[0]['process.env'].VUE_APP_TEST = '"test"'
    //   return args
    // })
  }
}
