<template>
  <view class="protocol-container">
    <view class="protocol-header">
      <!-- 返回按钮 -->
      <uni-icons
        type="left"
        size="24"
        class="back-icon"
        @tap="goBack"
      ></uni-icons>
      <text class="protocol-title">考好啦儿童隐私保护声明</text>
      <text class="protocol-date">更新日期：2025年08月25日</text>
      <text class="protocol-date">生效日期：2025年08月25日</text>
    </view>
    <scroll-view class="protocol-content" scroll-y="true">
      <view class="protocol-section">
        <text class="section-content" 
          >北京考好啦科技有限公司（以下简称"我们"或"考好啦"）高度重视儿童个人信息的保护。本声明作为《考好啦隐私政策》的专项补充，专门适用于 14 周岁以下的儿童用户。如本声明与《隐私政策》存在不一致，以本声明为准。</text
        >
      </view>

      <view class="protocol-section">
        <text class="section-title">一、儿童信息保护原则</text>
        <text class="section-content">1. 最小化收集原则：仅收集提供学习服务所必需的最少信息，例如：年级、学科、学习进度。不会收集与功能无关的敏感信息（如监护人职业、收入、社会关系）。</text>
        <text class="section-content">2. 监护人同意原则：儿童在使用考好啦服务前，必须在父母或其他法定监护人的陪同下阅读本声明，并在监护人同意后方可使用。</text>
        <text class="section-content">3. 安全保障原则：采取加密、访问控制、去标识化等措施，防止儿童信息的未经授权访问、使用或泄露。</text>
        <text class="section-content">4. 可控透明原则：监护人享有访问、更正、删除、撤回同意、注销账号等权利，并可随时联系我们行使。</text>
      </view>

      <view class="protocol-section">
        <text class="section-title">二、我们收集的儿童个人信息</text>
        <text class="section-content">在监护人同意的前提下，我们可能收集以下信息：</text>
        <text class="section-content">- 基本注册信息：如昵称、年级、学习科目。</text>
        <text class="section-content">- 学习行为信息：如答疑记录、课程学习记录、练习结果。</text>
        <text class="section-content">- 必要的设备信息：为保障服务稳定运行，我们可能收集设备型号、操作系统、网络状态等信息。</text>
        <text class="section-content">我们不会要求儿童提供与教学、学习无关的个人信息。</text>
      </view>

      <view class="protocol-section">
        <text class="section-title">三、儿童个人信息的使用场景</text>
        <text class="section-content">儿童信息将仅用于以下目的：</text>
        <text class="section-content">1. 提供在线学习、答疑、伴学等核心服务；</text>
        <text class="section-content">2. 保障账号与服务安全，防止作弊、盗号等行为；</text>
        <text class="section-content">3. 改进学习体验与内容推荐；</text>
        <text class="section-content">4. 经监护人明确授权的其他合理用途。</text>
      </view>

      <view class="protocol-section">
        <text class="section-title">四、儿童个人信息的共享、转让与披露</text>
        <text class="section-content">1. 我们不会向任何第三方共享、转让或公开披露儿童信息，除非：</text>
        <text class="section-content">  - 已获得监护人明确同意；</text>
        <text class="section-content">  - 基于法律法规的要求；</text>
        <text class="section-content">  - 为维护儿童的生命安全、财产安全所必需。</text>
        <text class="section-content">2. 如确需与第三方（如支付机构、云服务商）共享，将严格遵循"最小必要"原则，并要求其承担同等保护责任。</text>
      </view>

      <view class="protocol-section">
        <text class="section-title">五、儿童信息的存储与删除</text>
        <text class="section-content">1. 儿童信息仅在完成服务所需期限内保存，最长期限为5年；超过期限后将立即删除或匿名化。</text>
        <text class="section-content">2. 当儿童或其监护人注销账号时，我们将立即删除对应的全部个人信息，不留存任何备份（除非法律法规另有强制要求）。</text>
      </view>

      <view class="protocol-section">
        <text class="section-title">六、监护人的权利</text>
        <text class="section-content">儿童的父母或其他法定监护人有权：</text>
        <text class="section-content">1. 访问/查询儿童的个人信息；</text>
        <text class="section-content">2. 更正/更新不准确的信息；</text>
        <text class="section-content">3. 删除已收集的儿童个人信息；</text>
        <text class="section-content">4. 撤回同意，要求我们停止继续收集与使用；</text>
        <text class="section-content">5. 注销账号，并要求立即删除相关信息。</text>
        <text class="section-content">监护人可通过本声明"联系我们"栏目与我们联系，我们将在 15 个工作日内完成核验并予以处理。</text>
      </view>

      <view class="protocol-section">
        <text class="section-title">七、我们如何保护儿童信息</text>
        <text class="section-content">1. 采用加密存储、访问控制、最小权限管理等措施。</text>
        <text class="section-content">2. 建立儿童信息专属管理机制，严格限制内部访问。</text>
        <text class="section-content">3. 发生儿童信息安全事件时，我们将立即告知监护人，并按法律要求向监管部门报告。</text>
      </view>

      <view class="protocol-section">
        <text class="section-title">八、修订与更新</text>
        <text class="section-content">我们可能适时修订本声明。若涉及儿童个人信息处理方式的重大变更，我们将通过弹窗、公告、通知监护人等方式重新征得监护人同意。</text>
      </view>

      <view class="protocol-section">
        <text class="section-title">九、联系我们</text>
        <text class="section-content">如监护人对儿童信息的保护有任何问题、建议或投诉，可通过以下方式联系我们：</text>
        <text class="section-content">北京考好啦科技有限公司</text>
        <text class="section-content"> 地址：北京市海淀区中关村大街18号B座16层1622室</text>
        <text class="section-content"> 邮箱：<EMAIL></text>
      </view>

      <!-- 底部留出空白，确保内容不被遮挡 -->
      <view class="bottom-space"></view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  onLoad() {
    uni.setNavigationBarTitle({
      title: '儿童隐私保护声明'
    })
  },
  methods: {
    goBack() {
      console.log('childPrivacyPolicy-back')
      // 确保返回功能生效
      uni.navigateBack({
        delta: 1
      })
    }
  }
}
</script>

<style scoped>
.protocol-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
}

.protocol-header {
  padding: 40px 20px 20px; /* 增加顶部padding使内容下移 */
  text-align: center;
  position: relative;
}

.back-icon {
  position: absolute;
  left: 20px;
  top: 40px; /* 调整top值使按钮下移 */
  color: #333;
}

.protocol-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
  display: block;
  margin-bottom: 15rpx;
}

.protocol-date {
  font-size: 26rpx;
  color: #666666;
  display: block;
  margin-bottom: 8rpx;
}

.protocol-content {
  flex: 1;
  padding: 30rpx;
  box-sizing: border-box;
}

.protocol-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 15rpx;
  display: block;
  padding-bottom: 10rpx;
  border-bottom: 1rpx solid #eeeeee;
}

.section-content {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  display: block;
  margin-bottom: 10rpx;
}

.bottom-space {
  height: 60rpx;
}
</style>