<template>
  <scroll-view scroll-y class="details-page">
    <view class="serach">
      <picker
        mode="date"
        v-model="search.month"
        start="2025-01"
        end="2030-01"
        fields="month"
        @change="dateChange"
      >
        <view class="item">
          <image
            class="img2"
            src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/4e3617442676697245229_Union%402x.png"
          ></image>
          <image
            class="img1"
            src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/46ea174426763829114871_Frame%402x(1).png"
          ></image>
          <view>{{ search.month }}</view>
          <image
            class="img3"
            src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/6ace174418173457834915_Frame%2012%402x.png"
          ></image>
        </view>
      </picker>
      <pickerSelect
        v-model="search.status"
        :name.sync="search.status_name"
        :options="statusOptions"
        @change="refresh"
      >
        <view class="item">
          <image
            class="img2"
            src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/4e3617442676697245229_Union%402x.png"
          ></image>
          <image
            class="img1"
            src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/4b48174426765601240384_Frame%402x.png"
          ></image>
          <view>{{ search.status_name || '全部类型' }}</view>
          <image
            class="img3"
            src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/6ace174418173457834915_Frame%2012%402x.png"
          ></image>
        </view>
      </pickerSelect>
    </view>
    <pageList
      class="scrollpageList"
      :key="list.length"
      :loading="search.loading"
      @nextPage="getList"
      @refresh="refresh"
    >
      <view class="list" :key="list.length">
        <noData v-if="list.length == 0"></noData>
        <view>
          <!-- <view class="date-head">2025 年 2 月</view> -->
          <view class="list">
            <view class="item" v-for="item of list" @click="withdrawal(item)">
              <image
                class="icon"
                src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/8b6f174426780307266460_Group%20112%402x.png"
              ></image>
              <view class="info">
                <view>账户提现</view>
                <view
                  ><text>{{ item.created_at }}</text></view
                >
              </view>
              <view class="num"> ￥{{ item.withdraw_amount }} </view>

              <view
                v-if="item.status == 1"
                @click="requestMerchantTransfer(item)"
                >去收款</view
              >
              <view v-else>{{ item.status_name }}</view>
            </view>
          </view>
        </view>
      </view>
    </pageList>
  </scroll-view>
</template>

<script>
import { account, student, config } from '../../../api/index.js'
import pickerSelect from '../../../components/commen/picker-select.vue'
import pageList from '../../../components/commen/page-list.vue'
import noData from '../../../components/commen/no-data.vue'
import dayjs from 'dayjs'
export default {
  components: {
    pickerSelect,
    pageList,
    noData
  },
  data() {
    return {
      statusOptions: [
        {
          id: '',
          name: '全部类型'
        },
        {
          id: '1',
          name: '去收款'
        },
        {
          id: '2',
          name: '已收款'
        },
        {
          id: '3',
          name: '已过期'
        }
      ],
      search: {
        month: dayjs().format('YYYY-MM'),
        status: '',
        status_name: '',
        page: 1,
        size: 20,
        loading: false
      },
      list: [],
      icon: [
        'https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/4bf317442676930561271_Group%20110%402x.png',
        'https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/3fbc174426778936772240_Group%20111%402x.png',
        'https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/8b6f174426780307266460_Group%20112%402x.png',
        'https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/95fc17442678139861182_Group%20113%402x.png'
      ]
    }
  },
  onLoad(query) {
    this.query = query
    this.refresh()
  },
  methods: {
    requestMerchantTransfer(item) {
      if (wx.canIUse('requestMerchantTransfer')) {
        wx.requestMerchantTransfer({
          mchId: item.mch_id,
          appId: item.app_id,
          package: item.transfer_info.package_info,
          success: res => {
            this.$xh.Toast('确认收款成功！')
            this.refresh()
            // res.err_msg将在页面展示成功后返回应用时返回ok，并不代表付款成功
            console.log('success:', res)
          },
          fail: res => {
            console.log('fail:', res)
          }
        })
      } else {
        wx.showModal({
          content: '你的微信版本过低，请更新至最新版本。',
          showCancel: false
        })
      }
    },
    withdrawal(item) {
      if (item.type == 2) {
      }
    },
    dateChange: function (e) {
      this.search.month = e.detail.value
      this.refresh()
    },
    PickerChange: function (e) {
      this.search.type = e.detail.value
    },
    refresh() {
      this.search.page = 1
      this.list = []
      this.getList()
    },
    getList() {
      this.search.loading = true
      account.withdraw
        .applylist({
          month: this.search.month,
          status: this.search.status,
          page: this.search.page++,
          size: this.search.size
        })
        .then(res => {
          if (res.data?.list?.length) {
            this.list = this.list.concat(res.data.list)
          }
          this.search.loading = false
        })
        .catch(() => {
          this.search.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.details-page {
  height: 100vh;
  // overflow-y: auto;
  background-color: #fff;
}
.list {
  margin: 0 12px;
  .item {
    display: flex;
    align-items: center;
    padding: 15px 0;
    .icon {
      width: 40px;
      height: 40px;
      margin-right: 12px;
    }
    .info {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      view:nth-child(1) {
        font-weight: 400;
        font-size: 14px;
        color: #222333;
        line-height: 20px;
      }
      view:nth-child(2) {
        font-weight: 400;
        font-size: 12px;
        color: rgba(34, 35, 51, 0.65);
        line-height: 14px;
      }
    }
    .num {
      margin-left: 20px;
      margin-right: auto;
      font-weight: bold;
      font-size: 16px;
      color: #222333;
      line-height: 22px;
    }
  }
}
.date-head {
  margin: 0 12px;
  font-weight: 500;
  font-size: 12px;
  color: #222333;
  height: 44px;
  line-height: 44px;

  border-bottom: 1px solid rgba(214, 219, 227, 0.65);
}
.serach {
  display: flex;
  justify-content: space-between;
  padding: 0 12px 4px 12px;
  position: sticky;
  top: 0;
  left: 0;
  background-color: #fff;
  z-index: 1;
  .item {
    width: 171px;
    height: 36px;
    background: #edeff2;
    border-radius: 8px 8px 8px 8px;
    display: flex;
    position: relative;
    align-items: center;
    .img1 {
      margin-left: 12px;
      margin-right: 2px;
      width: 20px;
      height: 20px;
    }
    .img2 {
      position: absolute;
      left: 0;
      top: 0;
      width: 32px;
      height: 26px;
    }
    .img3 {
      margin-left: auto;
      width: 10px;
      height: 10px;
      margin-right: 12px;
    }
    view {
      font-weight: 400;
      font-size: 14px;
      color: #222333;
    }
  }
}
</style>
