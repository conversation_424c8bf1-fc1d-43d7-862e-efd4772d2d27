# mini-program

小程序

## 医生人才招聘网站

##### 启动指令

npm run start medicalExaminationRecruitment
npm run serve

##### 打包指令

make [分支名] name=jobm

# 项目对应分支关系
## 注： 一个分支一个项目，修复或者开发功能仅在下面的开发分支上处理即可，小程序项目不用合并！
| 所属公司     | 项目       | 分支(开发目录)   | 环境       | 项目描述                             |
| :--------: | :--------: | :--------: | :--------: | :--------: |
| 顺顺（星云）   | app/小程序内嵌 h5 | h5webview(modules/xingyunapph5) | 浏览器 | 需要内嵌的 h5 页面都可以在这个分支写 |
| 顺顺（星云） | 金题库 | jintiku(modules/jintiku) | 微信小程序/浏览器 | 金题库 App 的小程序版本，部分功能可打包成 h5, 目前主要应用是考试 |
| 顺顺（星云） | 星云收银宝 | xy_pay(modules/xingyunapp_gzh) | 浏览器 | 用于星云报名后 学员在 h5 端口下单支付 |
| 顺顺（星云） | 小程序管理助手 | program_management(modules/program_management) | 微信小程序 | 由于顺顺各部门小程序层出不穷，因此开发的一个小程序管理助手，用于统一管理小程序 |
| 顺顺（星云） | 顺顺公开课 | officialWebsite(modules/officialWebsite) | 微信小程序 | 顺顺商品课程售卖的第一个平台 |
| 顺顺（老官网） | 顺顺教育平台 | baidu(modules/jintiku_baidu) | 百度小程序 | 运行在百度上的做题的一个小程序，还用于运营推广百度卡片 |
| 顺顺（星云） | 顺顺公开课（签到预约） | main_activity(modules/xingyunstudent) | 微信小程序 | 用于学员签到预约课程购买体验营公开课 |
| 顺顺（星云） | 百题斩小程序 | baitizhan(modules/baitizhan) | 微信小程序 | 百题盏小程序，部分功能迁移自金题库小程序 |
| 顺顺（老官网） | 技能真题 | jinengzhenti(modules/jiNengZhenTi) | 微信小程序 | 技能真题小程序 |
| 顺顺（老官网） | 会员小程序 | member_mini(modules/member) | 微信小程序 | 会员小程序 |
| 顺顺（老官网） | 医考成绩服务 | master(modules/ykExamServe) | 微信小程序 | 成绩查询小程序 |
| 顺顺（老官网） | 医考成绩公示 | master(modules/ykQueryGrades) | 微信小程序 | 成绩查询小程序（是医考成绩服务的备用） |
| 顺顺（老官网） | 金护考 | master(modules/jhk) | 微信小程序 | 金护考小程序 |
| 顺顺（老官网） | 顺顺平台 | master(modules/jyjPlatform) | 微信小程序 | 小程序通用登录中心 |
| 顺顺（老官网） | 技能历年真题 | master(modules/lcJiNeng) | 微信小程序 | 界面和功能是百度小程序的微信小程序版本 |
| 顺顺（老官网） | 药考模拟小程序 | master(modules/ykTrain) | 微信小程序 | 药考模拟小程序 |
| 顺顺（老官网） | 笔试机考模拟练习系统 | clinicaExamination(modules/clinica_examination) | 微信小程序 | 模拟真实问诊情况 |
| 顺顺（老官网） | 旧版小程序中 口腔金题库极速版（皮肤是绿色的 真实名叫：医学金题库）和 中医金题库极速版-最新版本（黄皮肤 真实名叫：模拟考试训练场） 的内嵌模块 | kouqiang_mokao(modules/kouqiang_mokao) | 微信小程序 | 打包编译后的文件放到旧版小程序的目录中 |
| 医生医世 | 医生医世招聘找工作 c 端后台 | ysys_admin(modules/ysysAdmin) | 微信小程序 | 招聘的管理系统的 c 端 |
| 医生医世 | 招聘找工作小程序版本 | 新版分支：ysys_v1.1.2(modules/doctorTalentNetwork) | 微信小程序 | 招聘分为两个环境分别是 h5 和小程序这里是小程序的版本 |
| 医生医世 | 招聘找工作 h5 版本 | medicalExaminationRecruitment-h5(modules/medicalExaminationRecruitment) | 浏览器 | 招聘分为两个环境分别是 h5 和小程序 这里是 h5 的版本 |
| 医生医世 | 招聘找工作 sass公众号 版本 | ysysSassOfficialAccount(modules/ysysSassOfficialAccount) | 浏览器 | 是招聘找工作小程序的简化版本，和b端账号一一对应 env文件中有专属公众号商家id，修改了makefile配置所以不用合并打包命令 make dev name=jobm bname=dev |
| 顺顺（星云） | 金展会 | exhibitionServices(modules/exhibitionServices) | 微信小程序 | 校企盟-推广活动展会分类专用，用于雄促会签到 |
