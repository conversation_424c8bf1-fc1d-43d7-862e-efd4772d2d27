<template>
  <scroll-view
    scroll-y
    class="chatting"
    :scrollTop="scrollTop"
    :scroll-with-animation="scrollWithAnimation"
  >
    <view class="chatting-content" :style="padding">
      <view
        class="chatting-item"
        v-for="(item, index) of chattingList"
        :key="index"
      >
        <view v-if="false" class="status-label">开始通话</view>
        <view
          class="one-text"
          v-if="item.source == 'AITips' && item.type == 'text'"
        >
          <view> {{ item.content.content }} </view>
        </view>

        <view
          v-if="item.source == 'AI' && item.type == 'text'"
          class="ai-message-container"
        >
          <text user-select class="AI-text">
            {{ item.content.content }}
          </text>

          <text class="ai-generated-tag">AI生成,仅供参考</text>
        </view>
        <!-- <view class="AI-view" v-if="item.source == 'AI' && item.type == 'text'">
          <RichText class="RichText" :nodes="parse(item.content.content)" />
        </view> -->
        <text
          user-select
          v-if="item.source == 'user' && item.type == 'text'"
          class="user-text"
        >
          {{ item.content.content }}
        </text>
        <view
          v-if="item.source == 'user' && item.type == 'image'"
          class="user-image"
        >
          <imagePre :src="item.content.content"></imagePre>
        </view>
        <reportBox
          v-if="item.source == 'AI' && item.type == 'report'"
          :content="item.content"
        ></reportBox>
        <qingbei v-if="item.source == 'AI' && item.type == 'qingbei'"></qingbei>

        <view class="tips" v-if="item.source == 'AI' && item.type == 'tips'">
          📢 遇到任何学习难题，记得大喊"考拉老师救命！
        </view>
      </view>

      <view class="stop" v-if="isShowStop" @click="stopChange">
        <image
          src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/5ea1174600414452651668_Frame%402x.png"
        ></image>
        <text>结束提问</text>
      </view>
    </view>
  </scroll-view>
</template>

<script>
import reportBox from './reportBox.vue'
import qingbei from './qingbei.vue'
import imagePre from './image-pre.vue'
export default {
  components: {
    reportBox,
    qingbei,
    imagePre
  },
  props: {
    padding: {
      type: String,
      default: 'padding: 0px 12px 0px 12px;'
    }
  },
  data() {
    return {
      scrollTop: 0,
      length: 1,
      scrollWithAnimation: false,
      text: '👋Hey～是不是又遇到头疼的难题，小拉帮你召唤清北学霸帮帮团，助你秒变作业区最靓的崽💥'.match(
        /.{1,2}/g
      ),
      showText: ''
    }
  },
  created() {
    // this.textInit()
  },
  mounted() {
    this.setScrollTop(99999)
    // setInterval(() => {
    //   this.length = this.chattingList.length
    //   this.setScrollTop(this.length * 300)
    // }, 300)
  },
  computed: {
    chattingList() {
      return this.$store.getters['kaolaStudent/getChattingList']
    },
    scrollTopChange() {
      return this.$store.state.kaolaStudent.chatting.scrollTop
    },
    original_type() {
      return this.$store.state.kaolaStudent.original_type
    },
    isShowStop() {
      if (this.chattingList.length == 0) {
        return false
      }
      let end = this.chattingList[this.chattingList.length - 1]
      if (
        end.source == 'AI' &&
        end.type == 'text' &&
        end.original_type == '1'
      ) {
        return true
      }
      return false
    }
  },
  watch: {
    chattingList() {
      this.setScrollTop()
    },
    scrollTopChange() {
      this.setScrollTop()
    }
  },
  methods: {
    textInit() {
      let i = 0
      let interval = setInterval(() => {
        if (this.text[i]) {
          this.showText += this.text[i]
        } else {
          clearInterval(interval)
        }
        i++
      }, 100)
    },
    stopChange() {
      if (!this.istap) {
        this.$store.commit('kaolaStudent/stopChange')
        this.$store.commit('kaolaStudent/pushChatting', {
          type: 'text',
          source: 'user',
          content: {
            content: '结束提问'
          }
        })
        this.istap = true
        setTimeout(() => {
          this.istap = false
        }, 3000)
      }
    },
    parse(answer) {
      return parse(answer, {
        displayMode: true,
        throwOnError: false
      })
    },
    setScrollTop(val) {
      let length = this.chattingList.length
      setTimeout(() => {
        this.scrollTop = length * 1000 + 500 + Math.floor(Math.random() * 100)
      }, 300)
      if (val === 99999) {
        setTimeout(() => {
          this.scrollWithAnimation = true
        }, 500)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.chatting {
  // position: fixed;
  // top: 129px;
  // left: 0;
  // z-index: 1;
  height: 100%;
  width: 100%;
  .chatting-content {
    // padding: 129px 12px 134px 12px;
    display: flex;
    flex-direction: column;
    .chatting-item {
      display: flex;
    }
  }
  .stop {
    width: 92px;
    display: flex;
    padding: 10px;
    margin-top: 3px;
    margin-bottom: 4px;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    image {
      margin-right: 4px;
      width: 16px;
      height: 16px;
    }
    text {
      font-weight: 400;
      font-size: 13px;
      color: #222333;
    }
  }
  .status-label {
    margin: 8px auto;
    font-weight: 400;
    font-size: 12px;
    color: #ffffff;
    width: 74px;
    text-align: center;
    height: 24px;
    line-height: 24px;
    background: rgba(34, 35, 51, 0.5);
    border-radius: 12px 12px 12px 12px;
  }
  .tips {
    padding: 20px 0;
    text-align: center;
    width: 100%;
    font-weight: 400;
    font-size: 12px;
    color: rgba(34, 35, 51, 0.45);
  }
  .AI-view {
    margin: 5px 0;
    padding: 10px 16px;
    font-weight: 400;
    // font-size: 15px;
    color: #222333;
    line-height: 18px;
    text-align: left;
    max-width: 81.86vw;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    border: 1px solid #ffffff;
    margin-right: auto;
    white-space: pre-line;
    .RichText {
      width: 100%;
    }
  }
  .one-text {
    margin: 8px 0;
    margin-top: 0;
    padding: 12px 12px;
    font-weight: 500;
    font-size: 12px;
    color: #222333;
    line-height: 20px;
    text-align: left;
    width: 100%;
    background: rgba(34, 35, 51, 0.1);
    border-radius: 10px;
    border: 1px solid #ffffff;
    margin-right: auto;
    white-space: pre-line;
    view:nth-child(1) {
      // font-weight: 500;
      // font-size: 16px;
      color: #222333;
      // line-height: 19px;
    }
  }
  .AI-text {
    margin: 5px 0;
    padding: 8px 12px;
    font-weight: 400;
    font-size: 12px;
    color: #222333;
    line-height: 20px;
    text-align: left;
    max-width: 65.0666vw;
    background: rgba(34, 35, 51, 0.1);
    border-radius: 10px;
    border: 1px solid #ffffff;
    margin-right: auto;
    white-space: pre-line;
  }
  .user-text {
    margin: 5px 0;
    margin-left: auto;
    padding: 8px 12px;
    font-weight: 400;
    font-size: 12px;
    color: #222333;
    line-height: 20px;
    text-align: left;
    max-width: 44vw;
    background: #dbf46b;
    border-radius: 10px;
    white-space: pre-line;
    word-wrap: break-word;
    word-break: break-word;
    white-space: normal;
  }
  .user-image {
    max-width: 200px;
    margin: 8px 0;
    margin-left: auto;
  }
  .ai-message-container {
    display: flex;
    align-items: flex-end;
    margin-right: auto;
    position: relative;
  }

  .ai-generated-tag {
    font-size: 9px; // 字体大小从10px减小到9px
    color: rgba(34, 35, 51, 0.5);
    margin-left: 6px;
    padding: 2px 4px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 4px;
    align-self: flex-end;
  }
}
</style>
