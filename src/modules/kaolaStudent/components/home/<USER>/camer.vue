<template>
  <view class="camer">
    <camera
      class="camera"
      mode="normal"
      :device-position="devicePosition"
      :flash="isTorchOn ? 'on' : 'off'"
      @error="errorCamera"
      v-if="isAuth"
    >
      <cover-view
        class="controls"
        style="width: 100%; height: 100%"
        id="myCamera"
      >
        <cover-view class="center">
          <cover-image
            class="add"
            src="/static/imgs/kaolaStudent/cameradd.png"
          ></cover-image>
          <cover-view class="text">题目放在中央，用+对准</cover-view>
        </cover-view>
      </cover-view>
    </camera>
    <!-- 底部操作区域 -->
    <view class="bottom bottom-box">
      <view class="wrap">
        <!-- 相册 -->
        <view class="xiangce" @click="chooseImage">
          <image src="/static/imgs/kaolaStudent/xiangce.svg" mode=""></image>
          <text>相册</text>
        </view>
        <!-- 相机 -->
        <view class="take-box" @click="takePhoto"> </view>
        <view class="xiangce" @click="toggleTorch">
          <view style="width: 20px"></view>
          <!-- <image
            src="/static/imgs/kaolaStudent/camerShoudian.svg"
            mode=""
          ></image>
          <text>轻点{{ isTorchOn ? '关闭' : '照亮' }}</text> -->
        </view>
        <!-- 切换前后摄像头 -->
        <!-- <view class="back" @click="switchBtn">
          <image
            :src="flip"
            mode=""
            style="width: 60rpx; height: 60rpx"
          ></image>
        </view> -->
      </view>
    </view>
    <canvas class="cop" canvas-id="image-canvas"></canvas>
    <image class="" :src="src"></image>
  </view>
</template>
<script>
export default {
  data() {
    return {
      flip: 'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/82aa174710789575332315_%E6%91%84%E5%83%8F%E5%A4%B4%E5%8F%8D%E8%BD%AC.png', // 反转
      icon: 'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/4f73174710931150449248_237%E6%8B%8D%E7%85%A7.png', // 相机
      picture:
        'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/8681174710793002335068_%E5%9B%BE%E7%89%87.png', // 照片
      cameraContext: {},
      windowHeight: '',
      cameraHeight: '',
      idcardFrontSide: true,
      photoType: '',
      devicePosition: 'back', // 摄像头默认后置
      flashStyle: 'off',
      tokens: '',
      isAuth: false,
      src: '',
      isTorchOn: false
    }
  },
  created() {
    const _this = this
    uni.getSetting({
      success: res => {
        if (res.authSetting['scope.camera']) {
          // 用户已经授权
          _this.isAuth = true
          _this.init()
        } else {
          // 用户还没有授权，向用户发起授权请求
          uni.authorize({
            scope: 'scope.camera',
            success() {
              // 用户同意授权
              _this.isAuth = true
              _this.init()
            },
            fail(err) {
              console.log(err)
              // 用户不同意授权
              // _this.openSetting('camera').then(res => {
              //   _this.isAuth = true
              // })
            }
          })
        }
      },
      fail: res => {
        console.log('获取用户授权信息失败')
      }
    })

    uni.getSetting({
      success: res => {
        if (res.authSetting['scope.record']) {
          // 用户已经授权
          _this.isAuth = true
        } else {
          // 用户还没有授权，向用户发起授权请求
          uni.authorize({
            scope: 'scope.record',
            success() {
              // 用户同意授权
              _this.isAuth = true
            },
            fail() {
              // 用户不同意授权
              // _this.openSetting('record').then(res => {
              //   _this.isAuth = true
              // })
            }
          })
        }
      },
      fail: res => {
        console.log('获取用户授权信息失败')
      }
    })

    const systemInfo = uni.getSystemInfoSync()
    this.windowHeight = systemInfo.windowHeight
    this.cameraHeight = systemInfo.windowHeight - 160
  },
  methods: {
    init() {
      let _this = this
      if (uni.createCameraContext) {
        _this.cameraContext = uni.createCameraContext()
      } else {
        // 如果希望用户在最新版本的客户端上体验您的小程序，可以这样子提示
        uni.showModal({
          title: '提示',
          content:
            '当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。'
        })
      }
      // _this.photoType = options.photoType
      _this.devicePosition = 'back'
      _this.tokens = uni.getStorageSync('usertoken')
    },
    errorCamera(e) {
      console.log(e, 'errorCamera')
      const that = this
      uni.getSetting({
        success(res) {
          if (!res.authSetting['scope.camera']) {
            uni.showModal({
              title: '提示',
              content: '请开启摄像头权限，否则无法拍照',
              confirmText: '去开启',
              success(res) {
                if (res.confirm) {
                  uni.openSetting({
                    success(res) {
                      if (res.authSetting['scope.camera']) {
                        uni.navigateBack({
                          delta: 1
                        })
                      } else {
                        uni.navigateBack({
                          delta: 1
                        })
                      }
                    }
                  })
                } else if (res.cancel) {
                  uni.navigateBack({
                    delta: 1
                  })
                }
              }
            })
          }
        }
      })
    },
    // 拍照
    takePhoto() {
      uni.showLoading({
        title: '拍摄中'
      })
      console.log(this.cameraContext)
      this.cameraContext.takePhoto({
        quality: 'normal',
        success: res => {
          let idPhoto = res.tempImagePath
          // this.loadTempImagePath(idPhoto)
          this.chosePhoto(res.tempImagePath)
          uni.showToast({
            title: '拍照成功',
            icon: 'none',
            duration: 1200
          })
        },
        fail: err => {
          console.log(err)
          uni.showToast({
            title: '拍照失败，请检查系统是否授权',
            icon: 'none',
            duration: 1200
          })
        }
      })
    },

    //rpx转px
    rpx2px(rpx) {
      const screenWidth = uni.getSystemInfoSync().screenWidth
      return (screenWidth * Number.parseInt(rpx)) / 750
    },

    loadTempImagePath(url) {
      let { windowWidth, windowHeight } = uni.getSystemInfoSync()
      const camera = uni.createSelectorQuery().in(this).select('#myCamera') // 获取camera组件
      camera
        .boundingClientRect(data => {
          const x = data.left // 取景框左上角的x坐标
          const y = data.top // 取景框左上角的y坐标
          const width = data.width // 取景框的宽度
          const height = data.height // 取景框的高度
          console.log(x, y, width, height, 'x, y, width, height')
          console.log(url, 'url')
          let testc = uni.createCanvasContext('image-canvas', this)
          testc.drawImage(url, 0, 0, windowWidth, windowHeight)
          testc.draw(false, () => {
            uni.canvasToTempFilePath(
              {
                x: 0, //设置图片x轴起始点
                y: 0, //设置图片y轴起始点
                width: width,
                height: height,
                canvasId: 'image-canvas',
                // canvas: testc,
                fileType: 'jpg',
                quality: 1,
                complete: res2 => {
                  console.log(res2.tempFilePath, 'res2.tempFilePath88')
                  this.chosePhoto(res2.tempFilePath)
                },
                success: res => {
                  console.log(res, 'success')
                },
                fail: res => {
                  console.log(res, 'fail')
                }
              },
              this
            )
          })
        })
        .exec()
    },

    // 从相册选取
    chooseImage() {
      uni.chooseImage({
        count: 1,
        sizeType: ['original', 'compressed'],
        sourceType: ['album'],
        success: res => {
          let idPhoto = res.tempFilePaths[0]
          this.chosePhoto(idPhoto)
        },
        fail: err => {}
      })
    },

    //反转
    switchBtn() {
      if (this.devicePosition == 'front') {
        this.devicePosition = 'back'
      } else {
        this.devicePosition = 'front'
      }
    },

    // 选择图片跳转
    chosePhoto(item) {
      console.log(item, this.photoType, '拍摄完成')
      this.$emit('success', item)
      this.src = item
    },
    // 切换手电筒
    toggleTorch() {
      this.isTorchOn = !this.isTorchOn
      // console.log(this.isTorchOn, this.cameraContext, 'this.isTorchOn')
      // this.switchBtn()
      // this.cameraContext.toggleFlashLight({
      //   success: () => {
      //     console.log('闪光灯已切换')
      //   },
      //   fail: err => {
      //     console.log('切换失败', err)
      //   }
      // })
      return
      // 调用手电筒 API
      uni.toggleTorch({
        success: () => {
          this.isTorchOn = newState
          wx.showToast({
            title: newState ? '手电筒已开启' : '手电筒已关闭',
            icon: 'none'
          })
        },
        fail: err => {
          wx.authorize({
            scope: 'scope.camera',
            success: () => {
              /* 已授权 */
            },
            fail: () => {
              wx.openSetting() // 引导用户打开设置
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.camer {
  overflow: hidden;
  height: 100%;
  .camera {
    height: calc(100% - 160px);
    background-color: #000;
  }
}
.topNavbar {
  position: absolute;
  left: 0;
  top: 0;
  width: 100vw;
  z-index: 1;
  background-color: #000;
}
.navbar {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50px;
  position: relative;
  .title {
    font-weight: 400;
    font-size: 16px;
    color: #000000;
  }
  .back {
    position: absolute;
    left: 0;
    padding-left: 16px;
    height: 100%;
    display: flex;
    align-items: center;
  }
  .navbarback {
    width: 24px;
    height: 24px;
  }
}
.top-box {
  height: 200rpx;
  background-color: #000;
}
.camera-box {
  display: flex;
  align-items: center;
  justify-content: center;
  // flex-direction: column;
}

.camera-con {
  width: 520rpx;
  height: 100%;
}

.camera-bgcolor {
  // width: 66%;
  width: 520rpx;
  height: 100%;
}

.center-top {
  width: 100%;
  height: calc((100% - 750rpx) / 2);
  background-color: rgba(248, 248, 248, 0.6);
}

.center-bottom {
  width: 100%;
  height: calc((100% - 750rpx) / 2);
  background-color: rgba(248, 248, 248, 0.6);
}

.bottom {
  width: 100%;
  background-color: #000;
}

.wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 160px;
  padding: 0 73rpx;
}

.take-box {
  width: 49px;
  height: 49px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 28px 28px 28px 28px;
  border: 4px solid #ffffff;
}

.bottom-box {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  font-size: 36rpx;
  color: #fff;
  z-index: 1;

  .xiangce {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    image {
      width: 20px;
      height: 20px;
      margin-bottom: 2px;
    }
    text {
      font-weight: 400;
      font-size: 10px;
      color: #ffffff;
    }
  }
}

.controls {
  display: flex;
  align-items: center;
  justify-content: center;
  // flex-direction: column;
  .center {
    width: calc(100% - 40px);
    height: 144px;
    border-radius: 10px 10px 10px 10px;
    border: 2px solid rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    .add {
      margin-top: 62px;
      width: 20px;
      height: 20px;
      margin-bottom: 21px;
    }
    .text {
      font-weight: 500;
      font-size: 15px;
      color: #ffffff;
    }
  }
}

.controls1-bgcolor {
  list-style: none;
  padding: 0;
  margin: 0;
  // width: 22%;
  width: calc((100% - 522rpx) / 2);
  height: 100%;
  background-color: rgba(248, 248, 248, 0.6);
}

.camera-bgcolor {
  // width: 66%;
  width: 520rpx;
  height: 100%;
}

.center-top {
  width: 100%;
  height: calc((100% - 750rpx) / 2);
  background-color: rgba(248, 248, 248, 0.6);
}

.center-bottom {
  width: 100%;
  height: calc((100% - 750rpx) / 2);
  background-color: rgba(248, 248, 248, 0.6);
}

.cover_img {
  width: 520rpx;
}

.controls3-bgcolor {
  list-style: none;
  padding: 0;
  margin: 0;
  width: calc((100% - 520rpx) / 2);
  height: 100%;
  background-color: rgba(248, 248, 248, 0.6);
}

.cop {
  width: 100%;
  height: 100vh;
}
</style>
