<template>
  <view class="calendar-container">
    <view class="month">
      <view class="but" @click="last">
        <image src="/static/imgs/kaolaStudent/monthButIcon.svg"></image
      ></view>
      <view>{{ month }}</view>
      <view class="but next" @click="next">
        <image src="/static/imgs/kaolaStudent/monthButIcon.svg"></image>
      </view>
    </view>
    <view class="week-header">
      <view v-for="(weekDay, i) in weekDays" :key="i">{{ weekDay }}</view>
    </view>
    <!-- 周历模式 -->
    <week
      v-if="!isExpanded"
      @change="weekChange"
      v-model="date"
      :data="data"
      :initDate="curentInitDate"
      ref="week"
    ></week>
    <!-- 月历模式 -->
    <month
      v-if="isExpanded"
      @change="monthChange"
      v-model="date"
      :data="data"
      :initDate="currentWeek.start || curentInitDate"
      ref="month"
    ></month>
    <!-- 展开/收起按钮 -->
    <view class="toggle-btn" @click="toggleExpand">
      <!-- {{ isExpanded ? '收起' : '展开' }} -->
      <image
        :class="{
          hide: isExpanded
        }"
        class="img"
        src="/static/imgs/kaolaStudent/mwshow.svg"
      ></image>
    </view>
  </view>
</template>

<script>
import month from './month.vue'
import week from './week.vue'
import dayjs from 'dayjs'
const weekDays = ['一', '二', '三', '四', '五', '六', '日']
export default {
  components: {
    month,
    week
  },
  props: {
    data: {
      type: Array,
      default: []
    },
    initDate: {
      type: String,
      defaul: ''
    },
    value: {
      type: String,
      defaul: ''
    }
  },
  data() {
    return {
      weekDays,
      isExpanded: false,
      date: dayjs().format('YYYY-MM-DD'),
      currentWeek: {},
      currentMonth: {},
      curentInitDate: ''
    }
  },
  created() {
    this.curentInitDate = this.initDate
  },
  watch: {
    date() {
      this.$emit('input', this.date)
      this.isExpanded = false
      this.curentInitDate = this.date
    },
    value() {
      this.date = this.value
    }
  },
  computed: {
    month() {
      if (this.isExpanded) {
        return dayjs(this.currentMonth.start).format('YYYY-MM')
      } else {
        return dayjs(this.currentWeek.start).format('YYYY-MM')
      }
    }
  },
  methods: {
    last() {
      this.$refs[this.isExpanded ? 'month' : 'week'].last()
    },
    next() {
      this.$refs[this.isExpanded ? 'month' : 'week'].next()
    },
    // 切换展开状态
    toggleExpand() {
      this.isExpanded = !this.isExpanded
    },
    weekChange(e) {
      this.currentWeek = e
      this.change(e)
    },
    monthChange(e) {
      this.change(e)
      this.currentMonth = e
    },
    change(e) {
      this.$emit('change', e)
    }
  }
}
</script>

<style lang="scss" scoped>
.calendar-container {
  padding: 0px 0px 4px 0px;
  // background: rgba(255, 255, 255, 0.4);
  // backdrop-filter: blur(4px);
  background-image: url(http://xy-kaola.oss-cn-beijing.aliyuncs.com/public/4928174764576784747728_ddasdfag.png);
  background-size: 100% 144px;
  background-repeat: no-repeat;
  position: relative;
  border-radius: 0px 0px 16px 16px;
  background-color: #ffffff;
  .month {
    position: absolute;
    left: 12px;
    top: -44px;
    z-index: 1;
    width: 121px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    border-radius: 19px 19px 19px 19px;
    border: 1px solid rgba(223, 241, 134, 0.7);
    font-weight: 500;
    font-size: 14px;
    color: #222333;
    padding: 0 4px;
    .but {
      align-items: center;
      display: flex;
      height: 100%;
      padding: 0 6px 0 6px;
      image {
        width: 10px;
        height: 10px;
      }
    }
    .next {
      image {
        transform: rotateY(180deg);
      }
    }
  }
}
.week-header {
  padding-top: 12px;
  padding-bottom: 3px;
  width: 100%;
  display: flex;
  view {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    flex: 1;
    height: 15px;
    font-weight: 400;
    font-size: 11px;
    color: rgba(34, 35, 51, 0.85);
  }
}
.toggle-btn {
  height: 24px;
  line-height: 24px;
  color: #233;
  font-size: 12px;
  text-align: center;
  .img {
    width: 24px;
    height: 24px;
  }
  .hide {
    transform: rotateX(180deg);
  }
}
</style>
