<template>
  <view class="appointment-list">
    <!-- <u-tabs :list="tabList" @click="click"></u-tabs> -->
    <view class="list">
      <view class="list-item" v-for="item in appointList" :key="item.id">
        <view class="item-title">
          <!-- <view class="tag">直播</view> -->
          <view class="title">{{ item.name }}</view>
        </view>
        <view class="item-content">
          <view class="time-info"> 开播时间：{{ item.showDate }} </view>
          <view class="time-info"> 主播：{{ item.teacher_name }} </view>
          <view class="time-info"> 主题：{{ item.name }} </view>
        </view>

        <view class="item-bottom">
          <view class="button" @click="goLivePage(item)">查看推荐课程</view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
import dayjs from 'dayjs'
import { square } from '../../api'

export default {
  data() {
    return {
      tabList: [
        {
          name: '全部',
          id: 1
        },
        {
          name: '课程',
          id: 2
        }
      ],
      currentTab: 1,
      appointList: []
    }
  },
  onLoad() {
    this.getPageData()
  },
  methods: {
    click(index) {
      this.currentTab = index.id
    },
    getPageData() {
      square
        .getAppointList({
          goods_id: 5039439283,
          status: 1
        })
        .then(res => {
          if (res.data && res.data.list.length) {
            this.appointList = res.data.list.map(item => {
              const dayDate = dayjs(item.start_time).format('YYYY-MM-DD')
              item.oneTime = dayjs(item.start_time).format('HH:mm')
              item.twoTime = dayjs(item.end_time).format('HH:mm')
              item.showDate = `${dayDate} ${item.oneTime} - ${item.twoTime}`
              return item
            })
            console.log('预约列表', this.appointList)
          }
        })
    },

    goLivePage(item) {
      this.$xh.push('kaolaStudent', `pages/live/liveDetail?id=${item.id}`)
    }
  }
}
</script>
<style lang="scss" scoped>
.item-title {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 32rpx;
  border-bottom: 1px solid #e5e5e5;

  .title {
    color: #333;
    font-weight: 600;
    font-size: 34rpx;
    height: 48rpx;
    line-height: 48rpx;
  }
}

.appointment-list {
  background-color: #f4f5f7;
  padding: 32rpx;
}

.time-info {
  padding: 20rpx 0;
}
.list-item {
  padding: 25rpx;
  border-radius: 20rpx;
  background-color: #fff;
  width: 100%;
  margin-top: 32rpx;

  .item-bottom {
    border-top: 1px solid #e5e5e5;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 20rpx 10rpx;

    .button {
      background-color: #202334;
      color: #fff;
      display: inline-block;
      padding: 18rpx 30rpx;
      border-radius: 16rpx;
      font-size: 26rpx;
    }
  }
}
</style>
  
