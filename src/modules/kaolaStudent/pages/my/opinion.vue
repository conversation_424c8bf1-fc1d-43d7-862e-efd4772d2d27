<template>
  <view>
    <navbar class="navbar" title=" "></navbar>
    <view class="box" style="padding-right: 16px">
      <view class="name">问题描述</view>
      <view class="register-textarea">
        <textarea
          class="textarea"
          v-model="form.introduce"
          placeholder="请填写10个字以上的问题描述，以便我们提供更好的帮助"
          placeholder-style="font-size: 14px;color: rgba(34,35,51,0.45);"
          maxlength="300"
        ></textarea>
        <view class="count"
          ><text class="length">{{ form.introduce.length }}</text
          ><text class="total">/300</text></view
        >
      </view>
    </view>
    <view class="box" style="padding-right: 16px">
      <view class="name">截图 ({{ form.fileList.length }}/4)</view>
      <view class="upload-list">
        <view class="image" v-for="(item, index) of form.fileList">
          <image
            class="clear"
            src="/static/imgs/kaolaStudent/uploadClose.svg"
            @click="deletePic(index)"
          ></image>
          <image
            class="img"
            :src="$xh.completepath(item)"
            @click="previewImage(item)"
          ></image>
        </view>
        <view
          class="add image"
          @click="chooseMedia"
          v-if="form.fileList.length < 4"
        >
          <view class="icon"></view>
        </view>
      </view>
    </view>
    <view class="page-bottom-but page-bottom-but-fixed" @click="submit"
      >提交</view
    >
  </view>
</template>

<script>
import { config } from '../../api/index'
import { upLoad } from '../../utils'
import navbar from '../../components/commen/navbar.vue'
export default {
  components: {
    navbar
  },
  data() {
    return {
      form: {
        introduce: '',
        fileList: []
      }
    }
  },
  methods: {
    submit() {
      if (this.form.introduce.length == 0) {
        this.$xh.Toast('请输入问题描述')
        return
      }
      if (this.form.introduce.length < 10) {
        this.$xh.Toast('请填写10个字以上的问题描述')
        return
      }

      console.log(this.form)
      config
        .feedback({
          user_type: '1',
          description: this.form.introduce,
          attachment: this.form.fileList.join(',')
        })
        .then(res => {
          this.$xh.Toast('吐槽成功!')
          this.$xh.back()
        })
    },
    previewImage(url) {
      wx.previewImage({
        current: this.$xh.completepath(url),
        urls: this.form.fileList.map(s => this.$xh.completepath(s))
      })
    },
    chooseMedia() {
      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        maxDuration: 30,
        camera: 'back',
        success: res => {
          upLoad(res.tempFiles[0].tempFilePath)
            .then(url => {
              this.form.fileList.push(url)
            })
            .catch(res => {
              this.$xh.Toast('上传文件失败！')
            })
        }
      })
    },
    // 删除图片
    deletePic(index) {
      this.form.fileList.splice(index, 1)
    }
  }
}
</script>
<style lang="scss">
.upload-list {
  display: flex;
  flex-wrap: wrap;
  .add {
    display: flex;
    align-items: center;
    justify-content: center;
    .icon {
      position: relative;
      &::after {
        content: ' ';
        display: block;
        position: absolute;
        left: -7px;
        width: 16px;
        height: 2px;
        background: #d9d9d9;
        border-radius: 7px 7px 7px 7px;
      }
      &::before {
        content: ' ';
        display: block;
        position: absolute;
        top: -7px;
        width: 2px;
        height: 16px;
        background: #d9d9d9;
        border-radius: 7px 7px 7px 7px;
      }
    }
  }
  .image {
    overflow: hidden;
    margin-bottom: 9px;
    margin-right: 9px;
    width: 81px;
    height: 81px;
    background: #ffffff;
    border-radius: 12px 12px 12px 12px;
    position: relative;
    .clear {
      position: absolute;
      width: 20px;
      height: 20px;
      top: 0;
      right: 0;
      z-index: 1;
    }
    .img {
      width: 100%;
      height: 100%;
    }
  }
}
.box {
  padding: 0 12px;
  .name {
    padding: 12px 0;
    font-weight: 400;
    font-size: 16px;
    color: #222333;
    line-height: 19px;
  }
}
.register-textarea {
  width: 100%;
  padding: 14px 16px;
  background: #ffffff;
  border-radius: 10px 10px 10px 10px;
  height: 160px;
  position: relative;
  .textarea {
    width: 100%;
    height: 100%;
    font-weight: 400;
    font-size: 14px;
    color: rgba(34, 35, 51, 1);
  }
  .count {
    position: absolute;
    right: 16px;
    bottom: 14px;
    .total {
      font-weight: 400;
      font-size: 14px;
      color: rgba(34, 35, 51, 1);
    }
    .length {
      font-weight: 400;
      font-size: 14px;
      color: rgba(34, 35, 51, 0.6);
    }
  }
}
</style>
