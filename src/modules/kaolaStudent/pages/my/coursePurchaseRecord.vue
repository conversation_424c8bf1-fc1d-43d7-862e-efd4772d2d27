<template>
  <view class="page-coursePurchaseRecord">
    <pageList
      class="scrollpageList"
      :key="list.length"
      :loading="search.loading"
      @nextPage="getList"
      @refresh="refresh"
    >
      <view class="list" :key="list.length">
        <noData v-if="list.length == 0"></noData>
        <view class="item" v-for="item of list" @click="push(item)">
          <view class="head">
            <view class="l">
              <!-- item.teacher_img -->
              <image
                class="avatar"
                :src="
                  $xh.completepath(
                    item.teacher_avatar ||
                      'public/5ef917440086508568967_laoshitouxiang.png'
                  )
                "
              ></image>
              <view class="name">{{ item.teacher_name }}</view>
              <!-- <view class="label">清华大学</view> -->
              <image
                class="label"
                :src="$xh.completepath(item.teacher_school_logo)"
              ></image>
            </view>
            <view class="r">
              <view
                class="text"
                :class="{
                  s1: item.status == 1
                }"
                >{{ item.status_name }}</view
              >
            </view>
          </view>

          <view class="label-box">
            <view class="label-item">
              <image
                src="http://xy-kaola.oss-cn-beijing.aliyuncs.com/public/6eea174739755805728319_Frame%402x.png"
              ></image>
              <view>下单时间:</view>
              <view>{{ item.created_at }}</view>
            </view>
            <view class="label-item">
              <image
                src="http://xy-kaola.oss-cn-beijing.aliyuncs.com/public/5ca817473975768806916_Frame%402x%20(1).png"
              ></image>
              <view>消耗课次:</view>
              <view>{{ item.goods_num }}</view>
            </view>
          </view>

          <view class="payable_amount">¥{{ item.payable_amount }}</view>
        </view>
        <view style="height: 40px"></view>
      </view>
    </pageList>
  </view>
</template>

<script>
import noData from '../../components/commen/no-data.vue'
import { appoint } from '../../api/index'
import pageList from '../../components/commen/page-list.vue'
import { orderStatus } from '../../config'
export default {
  components: {
    pageList,
    noData
  },
  data() {
    return {
      search: {
        page: 1,
        size: 10,
        loading: false
      },
      list: []
    }
  },

  created() {
    this.refresh()
  },
  methods: {
    refresh() {
      this.search.page = 1
      this.list = []
      this.getList()
    },
    push(item) {
      if (item.status == '1') {
        this.$xh.push(
          'kaolaStudent',
          `pages/my/reservationOrder/detail?order_id=${item.id}&flow_id=${item.flow_id}&source=reservation`
        )
      } else {
        this.$xh.push(
          'kaolaStudent',
          `pages/my/reservationOrder/detail?order_id=${item.id}&flow_id=${item.flow_id}&source=reservation`
        )
      }
    },
    getList() {
      this.search.loading = true
      appoint.order
        .getList({
          page: this.search.page++,
          size: this.search.size
        })
        .then(res => {
          if (res.data?.list?.length) {
            this.list = this.list.concat(
              res.data.list.map(item => {
                item.status_name =
                  orderStatus[Number(item.status) - 1]?.name || ''
                return item
              })
            )
          }
          this.search.loading = false
        })
        .catch(() => {
          this.search.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.list {
  .item {
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;
    padding: 0 12px;
    margin: 8px 12px 0px 12px;
    .payable_amount {
      font-weight: 800;
      font-size: 14px;
      color: #332c22;
      height: 52px;
      line-height: 52px;
      text-align: right;
    }
    .label-box {
      display: flex;
      flex-direction: column;
      height: 76px;
      background: #f7f9fb;
      border-radius: 4px 4px 4px 4px;
      padding: 0 12px;
      // align-items: center;
      // border-bottom: 1px dashed rgba(214, 219, 227, 0.65);
      .label-item {
        padding-top: 8px;
        padding-bottom: 8px;
        display: flex;
        align-items: center;
        margin-right: 24px;
        flex-shrink: 0;
        image {
          width: 20px;
          height: 20px;
        }
        view:nth-child(2) {
          font-weight: 400;
          font-size: 14px;
          color: rgba(34, 35, 51, 0.65);
          line-height: 20px;
          margin-right: 12px;
          margin-left: 4px;
        }
        view:nth-child(3) {
          font-weight: 400;
          font-size: 14px;
          color: #222333;
          line-height: 20px;
        }
      }
    }
    .head {
      height: 53px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .l {
        display: flex;
        align-items: center;
        .avatar {
          width: 28px;
          height: 28px;
          border-radius: 50%;
        }
        .name {
          font-weight: 500;
          font-size: 15px;
          color: #222333;
          line-height: 20px;
          margin: 0 6px;
        }
        .label {
          width: 77px;
          height: 23px;
        }
        .label2 {
          width: 18px;
          height: 18px;
        }
      }
      .r {
        display: flex;
        align-items: center;
        .icon {
          width: 20px;
          height: 20px;
        }
        .text {
          font-weight: 400;
          font-size: 14px;
          color: rgba(34, 35, 51, 0.85);
          line-height: 20px;
        }
        .s1 {
          color: #ff6111;
        }
      }
    }
  }
}
.search {
  display: flex;
  justify-content: space-between;
  padding-right: 12px;
  padding-top: 14px;
  .search-item {
    // position: absolute;
    // top: 112px;
    // right: 12px;
    width: 78px;
    height: 32px;
    background: rgba(255, 255, 255, 0.32);
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #ffffff;
    display: flex;
    position: relative;
    align-items: center;
    justify-content: center;
    .img3 {
      margin-left: 4px;
      width: 10px;
      height: 10px;
    }
    view {
      font-weight: 400;
      font-size: 14px;
      color: #222333;
    }
  }
}

.page-coursePurchaseRecord {
  height: 100vh;

  .scrollpageList {
    height: 100%;
  }
}
</style>
