<template>
  <view class="wait-page">
    <view class="background">
      <image
        class="close"
        @click="$xh.back()"
        src="/static/imgs/kaolaStudent/close2.svg"
      ></image>
      <view class="content">
        <view class="role">
          <view class="tips"><view>正在全力为你锁定老师</view></view>
          <!-- https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/6324174557307533862553_BgSub_%E7%94%B7%E5%AD%A92.pic%402x.png -->
          <image
            class="img"
            src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/6324174557307533862553_BgSub_%E7%94%B7%E5%AD%A92.pic%402x.png"
          ></image>
        </view>
        <view class="question-box">
          <view class="title">
            <view>问题</view>
            <image
              src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/529e174548540895743798_Rectangle%20280%402x.png"
            ></image>
          </view>
          <view class="text">
            我有一道初二数学，反比例函数的问题需要解答，请帮我解释反比例函数和正比例函数的区别？
          </view>
          <view class="but">取消提问</view>
        </view>
        <view class="load-box">
          <view class="loading">
            <view></view>
          </view>
          <view class="tips">
            <image
              src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/52b1174548537547219914_Group%20248%402x.png"
            ></image>
            <view>预计等待 2 分钟</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {}
</script>

<style lang="scss" scoped>
.wait-page {
  width: 100vw;
  height: 100vh;
  // width: 375px;
  // height: 812px;
  background: linear-gradient(180deg, #d4ff00 0%, #ffffff 100%), #ffffff;
  // position: relative;
  .background {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    justify-content: flex-end;
    background-image: url(https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/3cac174557323293588407_Group%20256%402x.png);
    background-size: 100vw 157.86vw;
    background-repeat: no-repeat;
  }
  // border-radius: 0px 0px 0px 0px;
  .content {
    display: flex;
    flex-direction: column;
  }
  .close {
    position: fixed;
    left: 0;
    top: 40px;
    border: 16px solid rgba(111, 133, 5, 0);
    width: 24px;
    height: 24px;
    border-radius: 0px 0px 0px 0px;
  }
  .role {
    margin: 0 auto;
    padding-bottom: 32px;
    view {
      position: relative;
      z-index: 1;
    }
    .tips {
      padding: 11px 16px;
      background: rgba(34, 35, 51, 0.85);
      border-radius: 12px 12px 12px 12px;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      line-height: 22px;
      position: relative;
      &::before {
        content: ' ';
        position: absolute;
        bottom: -6px;
        left: calc(50% - 6px);
        display: block;
        transform: rotateZ(45deg);
        width: 12px;
        border-radius: 2px;
        height: 12px;
        background: rgba(34, 35, 51, 0.85);
      }
    }
    .img {
      margin-top: 34px;
      width: 152px;
      height: 242px;
      border-radius: 0px 0px 0px 0px;
    }
  }
}
.load-box {
  padding-bottom: 46px;
  padding-top: 42px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .loading {
    margin-bottom: 12px;
    width: 239px;
    height: 10px;
    background: #eff983;
    border-radius: 4px 4px 4px 4px;
    overflow: hidden;
    view {
      width: 174px;
      height: 100%;
      background: #222333;
      border-radius: 4px 4px 4px 4px;
    }
  }
  .tips {
    display: flex;
    align-items: center;
    view {
      font-weight: bold;
      font-size: 16px;
      color: #222333;
      line-height: 19px;
    }
    image {
      width: 22px;
      height: 22px;
      margin-right: 4px;
    }
  }
}
.question-box {
  margin: 0 auto;
  padding: 16px 16px 24px 16px;
  width: 343px;
  background: #ffffff;
  box-shadow: 0px 29px 45px -13px rgba(111, 133, 5, 0.2);
  border-radius: 16px 16px 16px 16px;
  display: flex;
  flex-direction: column;
  .title {
    position: relative;
    view {
      position: relative;
      z-index: 1;
      font-weight: bold;
      font-size: 16px;
      color: #222333;
      line-height: 16px;
    }
    image {
      position: absolute;
      left: 0;
      top: 0;
      width: 55.79px;
      height: 26px;
    }
  }
  .text {
    font-weight: 400;
    font-size: 14px;
    color: #222333;
    line-height: 20px;
    margin-top: 10px;
  }
  .but {
    margin: 0 auto;
    width: 131px;
    text-align: center;
    height: 44px;
    line-height: 44px;
    background: #222333;
    border-radius: 33px 33px 33px 33px;
    font-weight: bold;
    font-size: 14px;
    color: #ffffff;
    margin-top: 24px;
  }
}
</style>
