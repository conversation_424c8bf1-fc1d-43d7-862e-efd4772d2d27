<template>
  <view class="live-page back">
    <u-navbar
      title="推荐"
      bgColor="rgba(255,255,255,0)"
      @rightClick="rightClick"
      :autoBack="true"
    >
    </u-navbar>
    <view class="nav-block"></view>
    <u-search
      placeholder="搜索您想查找的内容"
      :showAction="false"
      bgColor="#ffffff"
      v-model="searchInput"
      @search="search"
    ></u-search>

    <view class="page-h-title">专题</view>
    <scroll-view class="live-list-x" :scroll-x="true">
      <view class="list-x-wrap">
        <view
          class="special-item"
          v-for="item in liveSpecialList"
          :key="item.id"
          @click="goSpecialPage(item)"
        >
          <view class="image-zoon">
            <image
              :src="$xh.completepath(item.cover_image_url)"
              class="cover-img"
              mode="aspectFill"
            ></image>
          </view>
          <view class="special-title">{{ item.name }}</view>
        </view>
      </view>
    </scroll-view>
    <!-- <u-tabs :list="tabList" @click="click"></u-tabs> -->
    <view class="tab-block">
      <view class="page-h-title">推荐</view>
    </view>

    <scroll-view
      :scroll-top="scrollTop"
      scroll-y="true"
      class="scroll-Y"
      @scrolltoupper="upper"
      @scrolltolower="lower"
      @scroll="scroll"
    >
      <view class="live-list">
        <view
          class="live-item"
          @click="goLiveDetail(item)"
          v-for="(item, index) in liveList"
          :key="item.id"
        >
          <view class="live-img">
            <view class="live-tag" v-if="item.status == 2">
              <text>进行中</text>
              <image
                class="live-tag-icon"
                src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/living.png"
              />
            </view>
            <view class="grey-bg" v-if="item.status == 1">
              <text>未开始</text>
            </view>
            <view class="grey-bg" v-if="item.status == 3">
              <text>已结束</text>
            </view>

            <image
              class="live-img-img"
              :src="customComplete(item.material_cover_path)"
              mode="aspectFill"
            />
            <view class="live-count">
              <u-icon name="eye"></u-icon>
              <text class="count-text">{{ item.pv }}</text>
            </view>
          </view>
          <view class="live-content">
            <view class="live-title">
              <text class="title-text u-line-1">{{ item.name }}</text>
              <view class="buy-tag" v-if="item.vip_level != 0"
                >购买会员可看</view
              >
            </view>
            <view class="live-time">{{ item.showDate }}</view>

            <view class="tearcher-wrap">
              <u-avatar
                :src="$xh.completepath(item.avatar)"
                :size="24"
              ></u-avatar>
              <text class="teacher-name">{{ item.teacher_name }}</text>
              <text class="teacher-name">{{ item.school_name }}</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>
<script>
import dayjs from 'dayjs'
import { teaching } from '../../api'

export default {
  name: 'livePage',
  data() {
    return {
      searchInput: '',

      tabList: [
        {
          name: '全部',
          id: 1
        },
        {
          name: '课程',
          id: 2
        }
      ],
      liveList: [],
      currentTab: 1,
      liveSpecialList: []
    }
  },
  onLoad() {
    this.getLiveSpecial()
    this.getLiveData()
  },

  methods: {
    getLiveSpecial() {
      teaching
        .getTeachingList({
          noloading: true,
          loading: true,
          teaching_type: 1,
          code: 'system',
          is_auth: 0,
          page: 1,
          size: 200
        })
        .then(res => {
          console.log('专题列表', res)
          this.liveSpecialList = res.data?.list
        })
    },
    search() {
      this.getLiveData()
    },
    customComplete(path) {
      if (path && path.startsWith('http')) {
        return path
      } else if (path) {
        return this.$xh.completepath(path)
      } else {
        return ''
      }
    },
    getLiveData() {
      teaching
        .getLiveList({
          name: this.searchInput
        })
        .then(res => {
          console.log('直播列表', res)
          this.liveList = res.data?.list.map(v => {
            const dateDay = v.start_time.split(' ')[0]
            const shortStartTime = dayjs(v.start_time).format('HH:MM')
            const shortEndTime = dayjs(v.end_time).format('HH:MM')
            return {
              ...v,
              showDate: `${dateDay} ${shortStartTime}-${shortEndTime}`
            }
          })
        })
    },
    click(index) {
      this.currentTab = index.id
    },
    clickTab(item) {
      console.log(item)
    },
    rightClick() {
      uni.navigateBack()
    },
    upper() {
      console.log('upper')
    },
    lower() {
      console.log('lower')
    },
    scroll() {},
    goLiveDetail(item) {
      this.$xh.push('kaolaStudent', 'pages/live/liveDetail?id=' + item.id)
    },
    goSpecialPage(item) {
      console.log(item, '跳转')
      this.$xh.push(
        'kaolaStudent',
        `pages/live/specialTopicList?type=1&id=${item.id}&title=${item.name}&image=${item.cover_image_url}`
      )
    }
  }
}
</script>
<style lang="scss" scoped>
.live-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10rpx;
}

.live-card {
  width: 100%;
  height: 27vh;
  border-radius: 6rpx;
  background-color: #fff;

  .card-title {
    font-weight: 600;
  }
}

.live-img-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.live-page {
  padding: 32rpx;

  &.back {
    background-image: url('https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/626f174562773210963083_Group%20272%402x.png');

    background-repeat: no-repeat;
    background-position: 12.92% -7.5%;
  }

  .nav-block {
    height: 152rpx;
  }

  .page-h-title {
    font-weight: 500;
    font-size: 32rpx;
    height: 52rpx;
    line-height: 52rpx;
    margin-bottom: 16rpx;
    margin-top: 32rpx;
  }

  .list-x-wrap {
    display: inline-block;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    height: 160rpx;
  }

  .special-item {
    width: 320rpx;
    // width: 170rpx;
    flex-shrink: 0;
    height: 160rpx;
    border-radius: 32rpx;
    background-color: #e6eaef;
    margin-right: 16rpx;
    padding: 16rpx;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .image-zoon {
      width: 128rpx;
      height: 128rpx;
      border-radius: 20rpx;
      background-color: #fff;
      margin-right: 16rpx;
      flex-shrink: 0;
    }
  }

  .live-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 32rpx;

    .live-item {
      width: 320rpx;
      // width: 170rpx;
      position: relative;
      background-color: #fff;
      border-radius: 32rpx;

      .live-time {
        font-weight: 400;
        font-size: 24rpx;
        line-height: 100%;
        letter-spacing: 0%;
        color: #666;
        padding-top: 16rpx;
      }

      .tearcher-wrap {
        padding-top: 16rpx;
        padding-bottom: 16rpx;
        font-size: 24rpx;
        color: #999;
        display: flex;
        justify-content: flex-start;
        align-items: center;

        .teacher-name {
          font-weight: 400;
          font-size: 24rpx;
          font-family: 'PuHuiTi_55_Regular';
          padding: 0 16rpx;
          color: #000;
        }
      }
    }

    .live-tag {
      position: absolute;
      top: 23rpx;
      right: 16rpx;
      border-radius: 100rpx;
      color: #333;
      font-size: 22rpx;
      font-weight: 600;
      z-index: 2;
      padding: 8rpx 16rpx;
      background: linear-gradient(90deg, #f2fb5d 0%, #baed26 100%);
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .grey-bg {
      position: absolute;
      top: 23rpx;
      right: 16rpx;
      border-radius: 100rpx;
      color: #333;
      font-size: 22rpx;
      font-weight: 600;
      z-index: 2;
      padding: 8rpx 16rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #f2f2f2;
    }

    .live-tag-icon {
      width: 18rpx;
      height: 20rpx;
      margin-left: 8rpx;
    }

    .live-count {
      position: absolute;
      z-index: 3;
      bottom: 16rpx;

      right: 16rpx;
      display: flex;
      justify-content: center;
      align-items: center;

      .count-text {
        color: #666;
        font-family: 'SourceHanSansCN-VF';
        font-weight: 500;
        font-style: Medium;
        font-size: 16rpx;
        letter-spacing: 5%;
        margin-left: 15rpx;
      }
    }

    .live-img {
      width: 100%;
      height: 254rpx;
      background-color: #f0f0f0;
      border-radius: 32rpx 32rpx 0 0;
      position: relative;
    }

    .buy-tag {
      font-weight: 500;
      width: 120rpx;
      flex-shrink: 0;
      font-size: 16rpx;
      padding: 6rpx 10rpx;
      border-radius: 6rpx;
      color: #000;
      background-color: #ffd7a4;
    }

    .live-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-text {
        font-family: 'SourceHanSansCN-VF';
        font-weight: 600;
        font-size: 24rpx;
        line-height: 42rpx;
        letter-spacing: 5%;
        color: #000;
        width: 176rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .live-content {
    padding: 16rpx;
  }

  .scroll-Y {
    height: 880rpx;
  }

  .special-title {
    font-size: 26rpx;
    font-weight: 400;
    letter-spacing: 1%;
    color: #000;
  }

  .cover-img {
    width: 100%;
    height: 100%;
    border-radius: 32rpx;
    object-fit: cover;
  }

  .tab-block {
    width: 100%;
  }
}
</style>
