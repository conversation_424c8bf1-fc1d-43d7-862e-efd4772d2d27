import { config } from '../api/index'
import Base64 from '@utlis/base64'
import $xh from '@utlis/index'
import store from '../../../store/index'
function systemMessage() {
  function m() {
    setTimeout(() => {
      config
        .message({
          noloading: true
        })
        .then(({ data }) => {
          if (data.type == 'joinroom') {
            store.commit('kaolaStudent/setSystemMessage', {
              ...data.message,
              isNavigate: true
            })
            uni.navigateTo({
              url: `/modules/kaolaStudent/pages/my/systemMessage`
            })
          }

          if (data.type == 'order') {
            store.commit('kaolaStudent/setSystemMessage', {
              ...data.message,
              isNavigate: false
            })
            // "flow_id":"564683885707796966","is_supervise_order":2,"order_id":"564683886026564070","pay_amount":10
            // flow_id=564683885707796966&is_supervise_order=2&order_id=564683886026564070&pay_amount=10
            uni.redirectTo({
              url: `/modules/kaolaStudent/pages/my/order/detail?flow_id=${data.message.flow_id}&is_supervise_order=${data.message.is_supervise_order}&order_id=${data.message.order_id}&pay_amount=${data.message.pay_amount}`
            })
          }

          if (data.type == 'appoint_lesson_end') {
            store.commit('kaolaStudent/setSystemMessage', {
              ...data.message,
              isNavigate: false
            })
            uni.redirectTo({
              url: `/modules/kaolaStudent/pages/my/reservationOrder/evaluate`
            })
          }
          m()
        })
        .catch(res => {
          console.log(res, 'res')
          m()
        })
    }, 2000)
  }
  m()
}
systemMessage()
