<template>
  <view class="wait-page">
    <view class="background">
      <image
        class="close"
        @click="$xh.back()"
        src="/static/imgs/kaolaStudent/close2.svg"
      ></image>

      <view style="height: 56.13vw"></view>
      <view class="teacher-info">
        <image
          class="backimg"
          src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/6e64174563071733084601_Group%20269%402x.png"
        ></image>
        <view class="avatar">
          <image class="img1" :src="$xh.completepath(info.avatar)"></image>
          <image
            v-if="info.sex == 1"
            class="img2"
            src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/3ab5174563172190027036_Group%20257%402x.png"
          ></image>
          <image
            v-if="info.sex == 2"
            class="img2"
            src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/8b74174563170016061969_Group%20257%402x(1).png"
          ></image>
        </view>

        <view class="teacher_name">
          <view class="name">{{ info.name }}</view>
          <!-- <view class="label">{{ info.school_id_name }}</view> -->
        </view>
        <view class="xinglabel">
          <view class="xing">
            <image
              src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/38a1174555253755312367_Star%2011%402x.png"
            ></image>
            <view>{{ info.lesson_score }}</view>
          </view>
          <image
            class="label"
            :src="$xh.completepath(info.school_logo)"
          ></image>
        </view>
        <view class="text">
          {{ info.introduce }}
        </view>

        <view class="label-box">
          <view class="item">
            <image
              src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/1456174563030489247359_Group%20258%402x.png"
            ></image>
            <view>{{ info.ranking_id_name }}</view>
          </view>
          <view class="item" style="width: 80px">
            <image
              src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/4e7717456304756695113_Group%20259%402x.png"
            ></image>
            <view>{{ info.score }}分</view>
          </view>
          <view class="item" style="width: 80px">
            <image
              src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/75f3174563049189437730_Group%20260%402x.png"
            ></image>
            <view>{{ info.question_num }}</view>
          </view>
        </view>
      </view>
      <view class="page-bottom-but-fixed" v-if="query.isCancel == '0'">
        <view class="cancel-but"> 已取消 </view>
      </view>

      <view class="page-bottom-but-fixed" v-else>
        <view class="page-bottom-but pay-but" @click="liveWebView()"
          >进入直播间</view
        >

        <view class="tips" @click="cancelTap">取消预约</view>
      </view>
    </view>

    <view class="open" v-show="cancel.show" @click="cancel.show = false">
      <view class="open-body" @click.stop="() => {}">
        <image
          class="title"
          src="http://xy-kaola.oss-cn-beijing.aliyuncs.com/public/699b174713813446769448_tititititi.png"
        ></image>

        <view class="list">
          <view
            class="item"
            v-for="item of cancel.options"
            @click="cancel.tabIndex = item.id"
            :class="{
              select: item.id == cancel.tabIndex
            }"
          >
            <view>{{ item.name }}</view>
            <image
              v-if="item.id == cancel.tabIndex"
              src="http://xy-kaola.oss-cn-beijing.aliyuncs.com/public/5253174713801354077585_Frame%402x.png"
            ></image>
          </view>
        </view>

        <view class="pay-but2" @click="cancelApi">确认取消</view>
      </view>
    </view>

    <view class="prompt" v-if="cancel.success">
      <view class="prompt-body">
        <view class="prompt-back">
          <view class="title">取消成功</view>
          <view class="text"
            >伴学费用将在3个工作日内退还到您的个人账户，请注意查收</view
          >
          <image
            class="img"
            src="http://xy-kaola.oss-cn-beijing.aliyuncs.com/public/2256174713900641081922_Group%20387%402x.png"
          ></image>
          <view class="but" @click="cancel.success = false">我知道了</view>
          <view class="phone"
            >如有疑问请联系服务热线
            <text @click="makePhoneCall()">{{ phone }}</text></view
          >
        </view>
      </view>
    </view>

    <view class="prompt prompt2" v-if="cancel.exceed">
      <view class="prompt-body">
        <view class="prompt-back">
          <view class="title">
            <text>温馨提示</text>
            <image
              src="http://xy-kaola.oss-cn-beijing.aliyuncs.com/public/4c03174713938894957833_Vector%203%402x.png"
            ></image>
          </view>
          <view class="text2" style="margin-top: 30px"
            >亲爱的学员：您预约的本次程已超出有效受理期。但请您放心，我们的客服团队会尽力为您提供帮助，欢迎致电
            <text @click="makePhoneCall()">{{ phone }}</text> 获取帮助。</view
          >
          <view class="text2">期待继续为您提供优质服务！</view>
          <view
            class="but"
            style="margin-top: 24px"
            @click="cancel.exceed = false"
            >我知道了</view
          >
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { config, appoint } from '../../api/index'

export default {
  data() {
    return {
      phone: '400-XXX-XXXX',
      info: {},
      time: 5,
      query: {
        isCancel: '0'
      },
      cancel: {
        exceed: false,
        success: false,
        show: false,
        options: [
          {
            name: '时间安排冲突',
            id: '1'
          },
          {
            name: '健康和个人原因',
            id: '2'
          },
          {
            name: '其它原因',
            id: '3'
          }
        ],
        tabIndex: '1'
      }
    }
  },
  onLoad(query) {
    this.query = query

    this.detail()
  },

  methods: {
    makePhoneCall(e) {
      // 弹窗确认
      wx.showModal({
        title: '确认拨号',
        content: `是否拨打 ${this.phone}？`, // 号码脱敏显示
        success: res => {
          if (res.confirm) {
            wx.makePhoneCall({
              phoneNumber: this.phone,
              success: () => console.log('调起拨号界面成功'),
              fail: err => {
                console.error('失败:', err)
                wx.showToast({ title: '无法拨号', icon: 'none' })
              }
            })
          }
        }
      })
    },
    cancelTap() {
      if (this.query.isCancel == '1') {
        this.cancel.show = true
      }
      if (this.query.isCancel == '2') {
        this.cancel.exceed = true
      }
    },
    cancelApi() {
      appoint.teacher
        .cancel({
          appoint_detail_id: this.query.id,
          reason: this.cancel.tabIndex
        })
        .then(res => {
          this.cancel.success = true
          this.cancel.show = false
          this.query.isCancel = '0'
        })
    },
    detail() {
      appoint.teacher
        .applyJoinDetail({
          // employee_id: '561855922658350517'
          employee_id: this.query.teacher_id
        })
        .then(res => {
          this.info = res.data
        })
    },
    close() {
      this.$xh.back()
    },
    liveWebView(item) {
      if (!this.query.live_url) {
        this.$xh.Toast('课程未开始')
        return
      }
      this.$xh.push(
        'kaolaStudent',
        `pages/companions/liveWebView?live_url=${this.query.live_url}`
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.prompt {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  .prompt-body {
    width: 311px;
    background: #ffffff;
    overflow: hidden;
    border-radius: 16px 16px 16px 16px;
    .prompt-back {
      background-image: url(http://xy-kaola.oss-cn-beijing.aliyuncs.com/public/1125174713899624662934_Group%20385%402x.png);
      background-size: 100% 147px;
      background-repeat: no-repeat;
      padding: 24px 26px;
      display: flex;
      flex-direction: column;
      align-items: center;
      .title {
        font-weight: 500;
        font-size: 20px;
        color: #222333;
        line-height: 20px;
        text-align: center;
        position: relative;
        text {
          position: relative;
          z-index: 1;
        }
        image {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 77px;
          height: 10px;
          z-index: -1;
        }
      }
      .text {
        font-weight: 400;
        font-size: 14px;
        color: rgba(34, 35, 51, 0.85);
        line-height: 20px;
        margin-top: 16px;
        margin-bottom: 15px;
        text-align: center;
      }
      .text2 {
        width: 100%;
        margin-top: 16px;
        font-weight: 400;
        font-size: 14px;
        color: rgba(34, 35, 51, 0.85);
        line-height: 20px;
        text {
          color: #006aff;
        }
      }
      .img {
        width: 183px;
        height: 111px;
      }
      .but {
        margin-bottom: 12px;
        width: 231px;
        text-align: center;
        height: 44px;
        line-height: 44px;
        background: #222333;
        border-radius: 33px 33px 33px 33px;
        font-weight: bold;
        font-size: 14px;
        color: #ffffff;
      }
      .phone {
        font-weight: 400;
        font-size: 12px;
        color: rgba(34, 35, 51, 0.45);
        text {
          color: #006aff;
        }
      }
    }
  }
}
.prompt2 {
  .prompt-back {
    background-image: url(http://xy-kaola.oss-cn-beijing.aliyuncs.com/public/38f0174713934319163780_Mask%20group%402x.png) !important;
    background-size: 100% 96px !important;
  }
}
.open {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1;
  background: rgba(0, 0, 0, 0.5);
  .open-body {
    position: absolute;
    left: 0;
    bottom: 0;
    // height: 296px;
    width: 100%;
    padding: 28px 12px 0 12px;
    display: flex;
    flex-direction: column;
    background-size: 100% 220px;
    background-image: url(http://xy-kaola.oss-cn-beijing.aliyuncs.com/public/5b0e174713806398851030_Mask%20group%402x.png);
    border-radius: 16px 16px 0px 0px;
    background-repeat: no-repeat;
    background-color: #fff;
    .title {
      margin-left: 12px;
      margin-bottom: 24px;
      width: 118px;
      height: 24px;
    }
    .pay-but2 {
      margin: 24px auto 40px auto;
      width: 231px;
      text-align: center;
      height: 44px;
      line-height: 44px;
      background: #222333;
      border-radius: 33px 33px 33px 33px;
      font-weight: bold;
      font-size: 14px;
      color: #ffffff;
    }
    .list {
      .item {
        margin: 0 14px 8px 14px;
        height: 52px;
        background: #ffffff;
        border-radius: 12px 12px 12px 12px;
        border: 1px solid rgba(0, 0, 0, 0.12);
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 16px;
        view {
          font-weight: 500;
          font-size: 14px;
          color: rgba(34, 35, 51, 0.85);
        }
        image {
          width: 16px;
          height: 16px;
        }
      }
      .select {
        border: 1px solid #222333;
      }
    }
  }
}
.cancel-but {
  width: 109px;
  text-align: center;
  height: 44px;
  background: #f0f5d8;
  border-radius: 33px 33px 33px 33px;
  font-weight: 400;
  font-size: 13px;
  color: rgba(34, 35, 51, 0.65);
  line-height: 44px;
  margin-bottom: 16px;
}
.page-bottom-but-fixed {
  display: flex;
  flex-direction: column;
  align-items: center;

  .tips {
    padding-top: 20px;
    text-align: center;
    font-weight: 400;
    font-size: 13px;
    color: rgba(34, 35, 51, 0.45);
  }
}
.label-box {
  margin: 0 20px;
  padding-top: 38px;
  padding-bottom: 32px;
  border-top: 1px dashed #e2eea9;
  display: flex;
  align-items: center;
  .item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 103px;
    height: 40px;
    background: #fbffe7;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #222333;
    position: relative;
    margin: 0 4px;
    image {
      position: absolute;
      top: -15px;
      left: 0;
      width: 43.7px;
      height: 29.84px;
    }
    view {
      font-weight: 400;
      font-size: 14px;
      color: #222333;
    }
  }
}
.teacher-info {
  margin: 0 24px;
  // width: 100%;
  // height: 400px;
  // background: linear-gradient(180deg, #d4ff00 0%, #ffffff 100%);
  background-color: #fff;
  box-shadow: 0px 29px 45px -13px rgba(111, 133, 5, 0.2);
  border-radius: 24px 24px 24px 24px;
  position: relative;
  display: flex;
  flex-direction: column;
  // padding-top: 60px;
  .backimg {
    position: absolute;
    left: 0;
    top: -56.13vw;
    width: 100%;
    height: 92.26666vw;
  }
  .xinglabel {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 10px;
    padding-bottom: 16px;
    .xing {
      display: flex;
      align-items: center;
      image {
        width: 16.17px;
        height: 15px;
      }
      view {
        font-weight: 500;
        font-size: 15px;
        color: #fb8105;
        line-height: 20px;
      }
    }
    .label {
      margin-left: 6px;
      width: 77px;
      height: 23px;
    }
  }
  .teacher_name {
    position: relative;
    margin: 0 auto;
    display: flex;
    align-items: center;
    padding-top: 18px;
    // padding-bottom: 18px;
    .name {
      font-weight: bold;
      font-size: 20px;
      color: #222333;
      margin-right: 5px;
    }
    .label {
      font-weight: bold;
      font-size: 14px;
      color: #222333;
      width: 68px;
      text-align: center;
      height: 24px;
      line-height: 24px;
      background: #d4ff00;
      border-radius: 4px 4px 4px 4px;
    }
  }
  .text {
    font-weight: 400;
    font-size: 14px;
    color: rgba(34, 35, 51, 0.85);
    line-height: 20px;
    margin: 0 24px;
    padding-bottom: 28px;
  }
  .avatar {
    position: relative;
    margin: 0 auto;
    width: 80px;
    height: 80px;
    background: #999faa;
    border: 4px solid #222333;
    border-radius: 50%;
    // overflow: hidden;
    position: relative;
    .img1 {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      overflow: hidden;
    }
    .img2 {
      position: absolute;
      bottom: -4px;
      right: -4px;
      width: 28px;
      height: 28px;
    }
  }
}
.wait-page {
  width: 100vw;
  height: 100vh;
  // width: 375px;
  // height: 812px;
  background: linear-gradient(180deg, #d4ff00 0%, #ffffff 100%), #ffffff;
  // position: relative;
  .background {
    height: 100%;
    width: 100%;
    background-image: url(https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/3cac174557323293588407_Group%20256%402x.png);
    background-size: 100vw 157.86vw;
    background-repeat: no-repeat;
  }
  // border-radius: 0px 0px 0px 0px;
  .content {
    display: flex;
    flex-direction: column;
  }
  .close {
    position: fixed;
    left: 0;
    top: 40px;
    border: 16px solid rgba(111, 133, 5, 0);
    width: 24px;
    height: 24px;
    border-radius: 0px 0px 0px 0px;
    z-index: 10;
  }
}
</style>
