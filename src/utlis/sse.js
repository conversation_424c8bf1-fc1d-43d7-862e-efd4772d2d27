// import {
// 	timeout,
// 	noToastUrl,
// 	// getBaseUrl,
// 	// app_id
// } from './httpRequestConfig'
import $xh from '@utlis/index'
import Base64 from '@utlis/base64'
import store from '../store/index'
// 认证变量
const biseKey = process.env.VUE_APP_BASICKEY
const biseValue = process.env.VUE_APP_BASICVALUE

let showLoading = function (loadingtext) {
  uni.showLoading({
    title: loadingtext ? loadingtext : '加载中..'
  })
}
let hideLoading = function () {
  uni.hideLoading()
}
// 设置认证
function setBasic(header = {}) {
  let base64 = Base64.encode(`${biseKey}:${biseValue}`)
  // header['Authorization'] = 'Basic ' + base64
  header['Authorization'] =
    'Basic czJiMmMyYjpxT0VmZk5sTDFINVFoIXdBQGVrTiVyeTd2NTIxTno5Nw=='
}

// 处理装态码
function handlerCode(
  data,
  normal_code = 100000,
  handlerStatusCode = {},
  requestData
) {
  let res_code = data.code
  if (res_code == normal_code || res_code == 100000) {
    // 正常
    return true
  } else {
    // 不正常 - 提示
    if (data.msg && data.msg.length) {
      if (typeof data.msg == 'string') {
        $xh.Toast(data.msg)
      } else {
        data.msg.forEach(msg => {
          $xh.Toast(msg)
        })
      }
    } else {
      $xh.Toast('接口返回异常，请打开网络选项检查！')
    }
    if (handlerStatusCode[res_code]) {
      // 有特殊装态吗处理特殊状态码
      return handlerStatusCode[res_code](data, requestData)
    }
    return false
  }
}
var requestNum = 0 // 请求次数: 网络同时多次请求 全部加载完成后取消loading
function request({
  url,
  _baseUrl, // 基础路径
  data = {},
  method = 'GET',
  hideloading = false,
  header = {},
  noToastUrl,
  noCheckoutLogin,
  timeout,
  normal_code,
  handlerStatusCode,
  checkLogin, // 检查登陆
  chunkReceived
}) {
  requestNum++
  let _header = {
    ...header
  }
  if (!noToastUrl.includes(url) && !data.noloading) {
    // !hideloading && showLoading(data.loadingtext)
  }
  if (!noCheckoutLogin.includes(url) && !checkLogin(_header, url, data)) {
    // 默认失败
    // $xh.Toast('登录失效！')
    console.log(`登录失效！，失效地址：${url}`)
    // wx.switchTab({
    //   url: '/modules/officialWebsite/pages/my/index'
    // })
    return Promise.reject('登录失效！')
  }
  return new Promise((resolve, reject) => {
    let _url
    let _data = JSON.parse(JSON.stringify(data))
    _url = _baseUrl + url
    setBasic(_header) // 设置认证

    if (!_header['content-type']) {
      if (method.toUpperCase() === 'POST' || method.toUpperCase() === 'PUT') {
        _header['content-type'] = 'application/x-www-form-urlencoded'
      } else {
        _header['content-type'] = 'application/text'
      }
    }

    let buffer = ''
    const requestTask = wx.request({
      url: _url,
      method,
      header: _header,
      data: _data,
      dataType: 'json', // 返回数据格式
      timeout,
      responseType: 'text',
      enableChunked: true, // 需要服务器支持分块传输
      success: res => {
        console.log('请求成功，但长连接需持续接收数据')
      },
      fail: err => console.error('请求失败:', err)
    })
    function arrayBufferToString(arrayBuffer) {
      const bytes = new Uint8Array(arrayBuffer)
      let str = ''
      for (let i = 0; i < bytes.length; i++) {
        str += String.fromCharCode(bytes[i])
      }
      return decodeURIComponent(escape(str)) // 关键转换步骤
    }
    // 监听分块数据接收
    requestTask.onChunkReceived(res => {
      // 1. 将 ArrayBuffer 转为字符串
      const chunkString = arrayBufferToString(res.data)
      // 2. 拼接数据到缓冲区
      buffer += chunkString
      // 3. 按 SSE 事件分隔符 "\n\n" 分割数据
      const events = buffer.split('\n\n')

      // 4. 处理最后一个可能不完整的事件（重新放回缓冲区）
      buffer = events.pop() || ''

      // 5. 遍历解析每个完整的事件
      events.forEach(event => {
        if (event.trim() === '') return // 跳过空事件
        const lines = event.split('\n')

        // 提取 "data:" 字段内容
        lines.forEach(line => {
          if (line.startsWith('data:')) {
            const payload = line.replace(/^data:\s*/, '').trim()
            if (payload != '[DONE]') {
              chunkReceived(JSON.parse(payload))
            } else {
              chunkReceived('[DONE]')
            }
            // 触发自定义事件或更新 UI
          }
        })
      })
    })
  })
}
// 函数柯里化
export default (function (_baseUrl) {
  return function ({
    noToastUrl = [],
    noCheckoutLogin = [],
    timeout = 500000,
    normal_code = 10000,
    handlerStatusCode = {},
    checkLogin = () => {
      return false
    }
  }) {
    return (params = {}) => {
      return request({
        ...params,
        timeout,
        _baseUrl: _baseUrl,
        noToastUrl,
        noCheckoutLogin,
        normal_code,
        handlerStatusCode,
        checkLogin
      })
    }
  }
})(process.env.VUE_APP_BASE_API)
// 返回请求实例
export function newRequest(_baseUrl = process.env.VUE_APP_BASE_API) {
  return function ({
    noToastUrl = [],
    noCheckoutLogin = [],
    timeout = 500000,
    normal_code = 10000,
    handlerStatusCode = {},
    checkLogin = () => {
      return false
    }
  }) {
    return (params = {}) => {
      return request({
        ...params,
        timeout,
        _baseUrl: _baseUrl,
        noToastUrl,
        noCheckoutLogin,
        normal_code,
        handlerStatusCode,
        checkLogin
      })
    }
  }
}
