<template>
  <view class="recorderManager">
    <view @touchstart="start" @touchend="end">按住说话</view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      recorderManager: '',
      timeout: null,
      status: 0
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      let plugin = requirePlugin('QCloudAIVoice')
      plugin.setQCloudSecret(appid, secretid, secretkey, openConsole) //设置腾讯云账号信息，其中appid（非微信appid）是数字，secret是字符串，
      // 若要支持临时密钥，需要传递token参数，示例如下：
      // plugin.setQCloudSecret(appid, secretid, secretkey, openConsole, token) // openConsole是布尔值(true/false)，为控制台打印日志开关

      //语音数据识别
      const buf = wx.arrayBufferToBase64(frameBuffer)
      plugin.sentenceRecognition({
        engSerViceType: '16k_zh', //引擎类型
        sourceType: 1, //1：语音数据
        voiceFormat: 'mp3',
        url: '',
        data: buf,
        dataLen: frameBuffer.byteLength,
        projectId: 0,
        // 以下为非必填参数，可跟据业务自行修改
        // hotwordId : '08003a00000000000000000000000000',
        // filterDirty: 0,
        // filterModal: 0,
        // filterPunc: 0,
        // convertNumMode : 0,
        success: function (data) {
          console.log('sentenceRecognition succ:', data)
        },
        fail: function (err) {
          console.log('sentenceRecognition error:', err)
        }
      })

      //url文件识别
      plugin.sentenceRecognition({
        engSerViceType: '16k_zh', //引擎类型
        sourceType: 0, //0：语音 URL
        voiceFormat: 'wav',
        url: 'https://asr-audio-1300466766.cos.ap-nanjing.myqcloud.com/test16k.wav',
        data: '',
        dataLen: 0,
        projectId: 0,
        // 以下为非必填参数，可跟据业务自行修改
        // hotwordId : '08003a00000000000000000000000000',
        // filterDirty: 0,
        // filterModal: 0,
        // filterPunc: 0,
        // convertNumMode : 0,
        success: function (data) {
          console.log('sentenceRecognition succ:', data)
        },
        fail: function (err) {
          console.log('sentenceRecognition error:', err)
        }
      })
    },
    start() {
      this.timeout = setTimeout(() => {
        const options = {
          duration: 100000,
          sampleRate: 44100,
          numberOfChannels: 1,
          encodeBitRate: 192000,
          format: 'aac',
          frameSize: 50
        }
        this.recorderManager.start(options)
        this.timeout = null
      }, 500)
    },
    end() {
      if (this.timeout) {
        clearTimeout(this.timeout)
      } else {
        this.recorderManager.stop()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.recorderManager {
  width: 100%;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  view {
    font-weight: 400;
    font-size: 16px;
    color: #ffffff;
  }
}
</style>
