<template>
  <view>
    <navbar class="navbar" title="课前提问"></navbar>
    <view v-if="isEdit == 2">
      <view class="box" style="padding-right: 16px">
        <view class="name">问题描述</view>
        <view class="register-textarea">
          <textarea
            class="textarea"
            v-model="form.introduce"
            placeholder="上课前有什么问题，快发给老师吧"
            placeholder-style="font-size: 14px;color: rgba(34,35,51,0.45);"
            maxlength="300"
          ></textarea>
          <view class="count"
            ><text class="length">{{ form.introduce.length }}</text
            ><text class="total">/300</text></view
          >
        </view>
      </view>
      <view class="box" style="padding-right: 16px">
        <view class="name">问题截图 </view>
        <view class="upload-list">
          <view class="image" v-for="(item, index) of form.fileList">
            <image
              class="clear"
              src="/static/imgs/kaolaStudent/uploadClose.svg"
              @click="deletePic(index)"
            ></image>
            <image class="img" :src="item" @click="previewImage(item)"></image>
          </view>
          <view
            class="add image"
            @click="chooseMedia"
            v-if="form.fileList.length < 1"
          >
            <view class="icon"></view>
          </view>
        </view>
      </view>
      <view class="page-bottom-but page-bottom-but-fixed" @click="submit"
        >提交
      </view>
    </view>

    <view v-if="isEdit == 1">
      <view class="question-box">
        <view class="question">
          <view class="head">
            <view class="avatar">
              <image
                class="img1"
                :src="$xh.completepath(userinfo2.avatar)"
              ></image>
              <image
                v-if="userinfo2.sex == 1"
                class="sex"
                src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/3ab5174563172190027036_Group%20257%402x.png"
              ></image>
              <image
                v-if="userinfo2.sex == 2"
                class="sex"
                src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/8b74174563170016061969_Group%20257%402x(1).png"
              ></image>
            </view>
            <view class="name">{{ userinfo2.name }}</view>
            <view class="label">
              <view v-if="userinfo2.stage_id_name">{{
                userinfo2.stage_id_name
              }}</view>
              <view>{{ userinfo2.subject_id_name }}</view>
            </view>
          </view>
          <image
            class="fen"
            src="http://xy-kaola.oss-cn-beijing.aliyuncs.com/public/943d174735924594090183_Line%2018%402x.png"
          ></image>
          <view class="content-box">
            <view class="title"
              ><view>课前问题</view>
              <image
                src="http://xy-kaola.oss-cn-beijing.aliyuncs.com/public/912d17466266120308353_Rectangle%20280%402x.png"
              ></image
            ></view>
            <view class="content" v-for="(message, index) of remark">
              <view
                class="text ellipsis"
                v-if="message.message_type == 'text'"
                >{{ message.message }}</view
              >
              <image
                class="image"
                v-if="message.message_type == 'image'"
                mode="center"
                show-menu-by-longpress
                :src="message.content"
                @click="previewImage(message.content)"
              ></image>
            </view>
          </view>
        </view>
      </view>

      <view class="page-bottom-but page-bottom-but-fixed" @click="editTap">
        编辑
      </view>
    </view>
  </view>
</template>

<script>
import { appoint } from '../../api/index'
import { upLoad } from '../../utils'
import navbar from '../../components/commen/navbar.vue'
import { message } from '@api/mock'
export default {
  components: {
    navbar
  },
  data() {
    return {
      isEdit: 0,
      form: {
        introduce: '',
        fileList: []
      },
      remark: [],
      picturesEmitName: 'companionsPictures'
    }
  },
  computed: {
    userinfo2() {
      return this.$store.state.kaolaStudent.userinfo2
    }
  },
  onLoad(query) {
    this.query = query
    this.getRemark()
    uni.$on(this.picturesEmitName, this.picturesSuccess)
  },
  methods: {
    editTap() {
      for (let item of this.remark) {
        if (item.message_type == 'text') {
          this.form.introduce = item.message
        }
        if (item.message_type == 'image') {
          this.form.fileList = [item.message]
        }
      }
      this.isEdit = 2
    },
    getRemark() {
      appoint.teacher
        .getRemark({
          id: this.query.id
        })
        .then(res => {
          this.remark = res.data?.remark
          if (this.remark.length > 0) {
            let remark = []
            for (let item of this.remark) {
              if (item.message_type == 'text') {
                remark[0] = item
              }
              if (item.message_type == 'image') {
                remark[1] = item
              }
            }
            this.remark = remark
            this.isEdit = 1
          } else {
            this.isEdit = 2
          }
        })
        .catch(() => {
          this.isEdit = 2
        })
    },
    chooseMedia() {
      this.$xh.push(
        'kaolaStudent',
        'pages/home/<USER>' + this.picturesEmitName
      )
    },

    previewImage(url) {
      wx.previewImage({
        current: url,
        urls: [url]
      })
    },
    submit() {
      if (this.form.introduce.length == 0) {
        this.$xh.Toast('请输入问题描述')
        return
      }
      if (this.form.introduce.length < 10) {
        this.$xh.Toast('请填写10个字以上的问题描述')
        return
      }

      console.log(this.form)

      const formArray = []
      // 添加文字描述
      if (this.form.introduce.length > 0) {
        formArray.push({
          content: this.form.introduce,
          message_type: 'text',
          message: this.form.introduce
        })
      }
      // 添加图片
      if (this.form.fileList.length > 0) {
        this.form.fileList.forEach(file =>{
          formArray.push({
            content: file,
            message_type: 'image',
            message: file
          })
        })
      }
      appoint.teacher
        .remark({
          id: this.query.id,
          remark: JSON.stringify(formArray)
        })
        .then(res => {
          this.$xh.Toast('提交成功!')
          this.$xh.back()
          // this.getRemark()
        })
      // this.form.fileList.forEach(e => {
      //   appoint.teacher
      //     .remark({
      //       id: this.query.id,
      //       content: e,
      //       message_type: 'image',
      //       message: e
      //     })
      //     .then(res => {
      //       this.getRemark()
      //     })
      // })
    },
    previewImage(url) {
      wx.previewImage({
        current: url,
        urls: this.form.fileList.map(s => s)
      })
    },
    picturesSuccess(fileUrl) {
      upLoad(fileUrl)
        .then(url => {
          this.form.fileList.push(this.$xh.completepath(url))
        })
        .catch(res => {
          this.$xh.Toast('上传文件失败！')
        })
    },
    // 删除图片
    deletePic(index) {
      this.form.fileList.splice(index, 1)
    }
  }
}
</script>
<style lang="scss">
.question-box {
  width: 100%;
  height: 480px;
  background-image: url(http://xy-kaola.oss-cn-beijing.aliyuncs.com/public/9811174735822955981909_Union%402x.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding-top: 49px;
}
.question {
  margin: 0px 16px 12px 16px;
  width: calc(100% - 32px);
  padding: 12px 16px 0 16px;
  // height: 236px;
  // background: #ffffff;
  // box-shadow: 0px 29px 45px -13px rgba(126, 113, 136, 0.21);
  // border-radius: 12px 12px 12px 12px;
  .fen {
    margin: 0 18px;
    width: calc(100% - 18px);
    height: 3px;
    margin-top: 30px;
  }
  .head {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: space-between;
    position: relative;
    // top: -40px;
    margin-top: -40px;
    .avatar {
      width: 80px;
      height: 80px;
      position: relative;
      .img1 {
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }
      .sex {
        position: absolute;
        right: -2px;
        bottom: -2px;
        width: 28px;
        height: 28px;
        z-index: 1;
      }
    }
    .name {
      font-weight: bold;
      font-size: 20px;
      color: #222333;
      line-height: 28px;
      margin: 12px 0;
    }
    .label {
      display: flex;
      align-items: center;
      view {
        margin-left: 6px;
        // width: 44px;
        text-align: center;
        padding: 0 8px;
        height: 28px;
        line-height: 28px;
        background: #faf1ff;
        border-radius: 8px 8px 8px 8px;
        font-weight: 500;
        font-size: 14px;
        color: #791cb5;
      }
    }
  }

  .content-box {
    position: relative;
    .title {
      width: 64px;
      margin: 0 auto;
      margin-top: 12px;
      margin-bottom: 8px;
      // padding-bottom: 10px;
      position: relative;
      image {
        position: absolute;
        width: 64px;
        height: 26px;
        left: 0;
        top: -4px;
      }
      view {
        position: relative;
        z-index: 1;
        font-weight: bold;
        font-size: 16px;
        color: #222333;
      }
    }
  }
  .content {
    padding: 4px 0px;
    .text {
      font-weight: 400;
      font-size: 14px;
      color: #222333;
      line-height: 20px;
    }
    .image {
      width: 100%;
      height: 80px;
      border-radius: 8px 8px 8px 8px;
    }
  }
}
.upload-list {
  display: flex;
  flex-wrap: wrap;
  .add {
    display: flex;
    align-items: center;
    justify-content: center;
    .icon {
      position: relative;
      &::after {
        content: ' ';
        display: block;
        position: absolute;
        left: -7px;
        width: 16px;
        height: 2px;
        background: #d9d9d9;
        border-radius: 7px 7px 7px 7px;
      }
      &::before {
        content: ' ';
        display: block;
        position: absolute;
        top: -7px;
        width: 2px;
        height: 16px;
        background: #d9d9d9;
        border-radius: 7px 7px 7px 7px;
      }
    }
  }
  .image {
    overflow: hidden;
    margin-bottom: 9px;
    margin-right: 9px;
    width: 81px;
    height: 81px;
    background: #ffffff;
    border-radius: 12px 12px 12px 12px;
    position: relative;
    .clear {
      position: absolute;
      width: 20px;
      height: 20px;
      top: 0;
      right: 0;
      z-index: 1;
    }
    .img {
      width: 100%;
      height: 100%;
    }
  }
}
.box {
  padding: 0 12px;
  .name {
    padding: 12px 0;
    font-weight: 400;
    font-size: 16px;
    color: #222333;
    line-height: 19px;
  }
}
.register-textarea {
  width: 100%;
  padding: 14px 16px;
  background: #ffffff;
  border-radius: 10px 10px 10px 10px;
  height: 160px;
  position: relative;
  .textarea {
    width: 100%;
    height: 100%;
    font-weight: 400;
    font-size: 14px;
    color: rgba(34, 35, 51, 1);
  }
  .count {
    position: absolute;
    right: 16px;
    bottom: 14px;
    .total {
      font-weight: 400;
      font-size: 14px;
      color: rgba(34, 35, 51, 1);
    }
    .length {
      font-weight: 400;
      font-size: 14px;
      color: rgba(34, 35, 51, 0.6);
    }
  }
}
</style>
