<template>
  <view class="pictures">
    <pictures @success="load"></pictures>
  </view>
</template>

<script>
import pictures from '../../components/home/<USER>/index.vue'
export default {
  components: {
    pictures
  },
  data() {
    return { emitName: '' }
  },
  onLoad(query) {
    this.emitName = query.emitName || 'pictures'
  },
  methods: {
    load(url) {
      uni.$emit(this.emitName, url)
      this.$xh.back()
    }
  }
}
</script>

<style lang="scss" scoped>
.pictures {
  height: 100vh;
  width: 100vw;
}
</style>
