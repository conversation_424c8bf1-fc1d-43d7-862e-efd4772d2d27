<template>
  <view class="protocol-container">
    <view class="protocol-header">
      <!-- 返回按钮 -->
      <uni-icons type="left" size="24" class="back-icon" @tap="goBack"></uni-icons>
      <text class="protocol-title">考好啦AI用户协议与隐私政策</text>
      <text class="protocol-date">更新日期：2025年7月15日</text>
      <text class="protocol-date">生效日期：2025年7月15日</text>
    </view>
    <scroll-view class="protocol-content" scroll-y="true">
      <view class="protocol-section">
        <text class="section-content">欢迎您使用由北京考好啦科技有限公司（以下简称"我们"或"考好啦AI"）提供的服务。在您注册、登录、使用本产品和服务前，请您仔细阅读并充分理解本协议的全部内容，尤其是以加粗方式标注的内容。若您不同意本协议任何条款，请不要使用考好啦AI相关服务。</text>
        <text class="section-content">一旦您点击"同意"或实际使用，即视为您已阅读并同意本协议。</text>
      </view>
      
      <view class="protocol-section">
        <text class="section-title">一、适用范围</text>
        <text class="section-content">本协议适用于考好啦AI通过网站、小程序、App等平台向您提供的所有产品与服务。若本协议与具体产品中的协议不一致，以具体产品协议为准。</text>
      </view>
      
      <view class="protocol-section">
        <text class="section-title">二、用户使用条款</text>
        <text class="section-subtitle">账号注册与使用</text>
        <text class="section-content">用户应保证所提供信息真实、准确、合法。</text>
        <text class="section-content">用户不得冒用他人信息注册账户，不得将账号借予、转让、出售或用于非法用途。</text>
        
        <text class="section-subtitle">使用规范</text>
        <text class="section-content">不得通过本平台发布、传播违法、暴力、低俗、侵权内容。</text>
        <text class="section-content">不得干扰系统运行、破坏数据、逆向工程等。</text>
        <text class="section-content">未经许可，禁止使用考好啦AI接口进行批量抓取、自动化调用等行为。</text>
        
        <text class="section-subtitle">未成年人使用</text>
        <text class="section-content">若您未满18周岁，应在法定监护人指导下使用服务。</text>
      </view>
      
      <view class="protocol-section">
        <text class="section-title">三、隐私政策与数据保护</text>
        <text class="section-content">我们重视用户隐私，遵守《中华人民共和国网络安全法》《数据安全法》《个人信息保护法》等相关法律法规。</text>
        
        <text class="section-subtitle">1. 我们收集的信息类型包括但不限于：</text>
        <text class="section-content">注册信息（如手机号、昵称、学校、地区）</text>
        <text class="section-content">用户输入内容（如提问记录、错题照片）</text>
        <text class="section-content">使用数据（如使用频次、访问时长、设备信息）</text>
        
        <text class="section-subtitle">2. 数据用途</text>
        <text class="section-content">我们收集的数据将用于：</text>
        <text class="section-content">提供服务、优化体验、个性化推荐；</text>
        <text class="section-content">客服与技术支持；</text>
        <text class="section-content">法律法规规定的其他用途。</text>
        
        <text class="section-subtitle">3. 第三方共享与披露</text>
        <text class="section-content">未经用户授权，我们不会将您的个人信息提供给第三方，除非为实现服务目的或符合法律规定。</text>
        <text class="section-content">我们可能使用第三方服务提供商（如短信、语音服务），其仅限在服务范围内使用信息。</text>
        
        <text class="section-subtitle">4. 数据安全</text>
        <text class="section-content">我们采用加密存储、访问控制、权限审计等手段保护您的数据，防止数据泄露、滥用或丢失。</text>
      </view>
      
      <view class="protocol-section">
        <text class="section-title">四、知识产权声明</text>
        <text class="section-content">考好啦AI平台的内容、页面设计、文字、图像、模型结果等均为我们或相关权利人合法拥有。</text>
        <text class="section-content">用户不得擅自复制、转载、传播、出售、镜像或用于其他商业用途。</text>
      </view>
      
      <view class="protocol-section">
        <text class="section-title">五、免责声明</text>
        <text class="section-content">考好啦AI根据算法自动生成的内容仅供参考，不对其准确性、完整性作出保证。</text>
        <text class="section-content">对于因网络、设备故障、不可抗力等导致的服务中断、数据丢失，我们将在合理范围内协助处理，但不赔偿责任。</text>
        <text class="section-content">因用户自身行为导致的损失（如密码泄露、内容误删）由用户自行承担。</text>
      </view>
      
      <view class="protocol-section">
        <text class="section-title">六、协议更新与通知</text>
        <text class="section-content">本协议可能因法律法规变化、业务调整而更新。</text>
        <text class="section-content">若有重大变更，我们将通过公告、弹窗或其他方式通知您。变更生效后，您继续使用即视为接受。</text>
      </view>
      
      <view class="protocol-section">
        <text class="section-title">七、联系方式</text>
        <text class="section-content">如您对本协议有任何疑问、建议或投诉，请通过以下方式联系我们：</text>
        <text class="section-content">公司名称：北京考好啦科技有限公司</text>
        <text class="section-content">联系电话：13260168343</text>
        <text class="section-content">联系地址：北京市海淀区中关村大街18号B座16层1622室</text>
      </view>
      
      <view class="protocol-section">
        <text class="section-title">八、法律适用与争议解决</text>
        <text class="section-content">本协议适用中华人民共和国法律。因本协议引发的争议，双方应友好协商解决；协商不成的，任一方可提交公司所在地人民法院诉讼解决。</text>
        <text class="section-content">感谢您信任并使用考好啦AI，我们致力于为您提供安全、智能、便捷的教育服务。</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  onLoad() {
    uni.setNavigationBarTitle({
      title: '用户协议与隐私政策'
    });
  },
  methods: {
    goBack() {
      console.log('openAgreement-back');
      // 确保返回功能生效
      uni.navigateBack({
        delta: 1
      });
    }
  }
}
</script>

<style scoped>
.protocol-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
}

.protocol-header {
  padding: 40px 20px 20px; /* 增加顶部padding使内容下移 */
  text-align: center;
  position: relative;
}

.back-icon {
  position: absolute;
  left: 20px;
  top: 40px; /* 调整top值使按钮下移 */
  color: #333;
}

.protocol-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
  display: block;
  margin-bottom: 15rpx;
}

.protocol-date {
  font-size: 26rpx;
  color: #666666;
  display: block;
  margin-bottom: 8rpx;
}

.protocol-content {
  flex: 1;
  padding: 30rpx;
  box-sizing: border-box;
}

.protocol-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 15rpx;
  display: block;
  padding-bottom: 10rpx;
  border-bottom: 1rpx solid #eeeeee;
}

.section-subtitle {
  font-size: 28rpx;
  font-weight: bold;
  color: #444444;
  margin: 20rpx 0 10rpx 0;
  display: block;
}

.section-content {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  display: block;
  margin-bottom: 10rpx;
}
</style>