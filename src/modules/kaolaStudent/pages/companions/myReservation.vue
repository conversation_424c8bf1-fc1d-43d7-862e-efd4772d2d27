<template>
  <div>
    <RichText :nodes="result" />
  </div>
</template>

<script>
import parse from '@rojer/katex-mini'
export default {
  data() {
    return {
      result: '',
      content: {
        grade: '初中',
        year: '七年级',
        subject: '数学',
        answer: 'x = -\\frac{9}{8}',
        analysis:
          '这是一个一元一次方程，求解过程如下：首先对原方程8x + 9 = 0进行移项，将常数项9移到等号右边，变为8x = -9；然后等号两边同时除以x的系数8，即x = -\\frac{9}{8}。'
      }
    }
  },
  onLoad() {
    this.result = parse(this.content.answer, {
      displayMode: true,
      throwOnError: false
    })
  }
}
</script>

<style lang="scss" scoped></style>
