<template>
  <view class="input-box">
    <view class="mask" v-if="mask"></view>
    <view class="icon" @click="phone.show = true">
      <image src="/static/imgs/kaolaStudent/dianhua.svg"></image>
    </view>
    <view class="input-content" v-if="inputType == 1">
      <textarea
        class="input"
        v-model="text"
        inputmode="text"
        type="text"
        confirm-type="send"
        auto-height
        @confirm="confirm"
        placeholder-style="placeholderStyle"
        adjust-keyboard-to="bottom"
        placeholder="自由输入..."
        cursor-spacing="25"
      >
      </textarea>
      <!-- :show-confirm-bar="false" -->
      <!-- confirm-type="return" -->
    </view>
    <view class="recorderManager" v-if="inputType == 2">
      <recorderManager
        @sentenceRecognition="sentenceRecognition"
      ></recorderManager>
    </view>
    <view class="icon" v-if="inputType == 1" @click="inputTypeChange(2)">
      <image src="/static/imgs/kaolaStudent/input.svg"></image>
    </view>
    <view class="icon" v-if="inputType == 2" @click="inputTypeChange(1)">
      <image src="/static/imgs/kaolaStudent/jianpan.svg"></image>
    </view>
    <view v-if="text.length == 0" class="icon" @click="chooseMedia">
      <image src="/static/imgs/kaolaStudent/zhaoxiang.svg"></image>
    </view>
    <view v-else class="icon" @click="confirm" style="color: #fff"> 发送 </view>
    <phone
      v-if="phone.show"
      @close="phone.show = false"
      @sentenceRecognition="sentenceRecognition"
    ></phone>

    <wait></wait>

    <askAbout
      v-if="askAbout.show"
      @success="askAboutTap"
      @close="askAbout.show = false"
    ></askAbout>
  </view>
</template>

<script>
import recorderManager from './recorderManager.vue'
import { student, aimessageStream } from '../../../api/index'
import { upLoad, json_parse, goToLogin } from '../../../utils'
import phone from './phone.vue'
import wait from './wait.vue'
import askAbout from './askAbout.vue'
export default {
  props: {},
  components: {
    recorderManager,
    phone,
    wait,
    askAbout
  },
  data() {
    return {
      aiTipsText: `👋Hey宝子～我是你的学习搭档小拉。
遇到难题别挠头，随时call我，火力全开帮你搞定！`.match(/.{1,2}/g),
      qingbeiTipsText:
        '👋Hey～是不是又遇到头疼的难题，小拉帮你召唤清北学霸帮帮团，助你秒变作业区最靓的崽💥'.match(
          /.{1,2}/g
        ),
      phone: {
        show: false
      },

      inputType: uni.getStorageSync('__inputType__') || 2,
      id: '',
      text: '',
      // 类型(text,image,video)
      message_type: '',
      lastTimeContent: '',
      mask: false,
      current: {},
      askAbout: {
        show: false,
        data: {}
      },
      picturesEmitName: 'homePictures'
    }
  },
  created() {
    this.historyAimessage()

    uni.$on(this.picturesEmitName, this.picturesSuccess)
  },
  computed: {
    stop_num() {
      return this.$store.state.kaolaStudent.chatting.stop_num
    },
    original_type() {
      return this.$store.state.kaolaStudent.original_type
    },
    token() {
      return this.$store.state.kaolaStudent.token
    }
  },
  watch: {
    stop_num() {
      this.stopChange(1)
    },
    original_type() {
      this.$store.commit('kaolaStudent/clearChatting')
      this.initTips()
    },
    token() {
      this.$store.commit('kaolaStudent/clearChatting')
      this.historyAimessage()
    }
  },
  methods: {
    initTips() {
      let content = {
        content: ''
      }
      this.pushChatting({
        type: 'text',
        source: 'AITips',
        content: content
      })
      let i = 0
      let text = []
      if (this.original_type == '1') {
        text = this.aiTipsText
      }
      if (this.original_type == '2') {
        text = this.qingbeiTipsText
      }
      let interval = setInterval(() => {
        if (text[i]) {
          content.content += text[i]
        } else {
          clearInterval(interval)
        }
        i++
      }, 100)
    },
    stopChange(type) {
      this.pushChatting({
        source: 'AI',
        type: 'report',
        content: {
          id: this.id
        }
      })
      this.id = ''
    },
    inputTypeChange(type) {
      this.inputType = type
      uni.setStorageSync('__inputType__', type)
    },
    historyAimessage() {
      student.question
        .lastai({})
        .then(res => {
          this.id = res.data.id
          let message = res.data.message
          if (message?.length) {
            // "role": "user", //消息角色(user:用户 assistant:AI)
            for (let item of message) {
              this.pushChatting({
                type: item.message_type == 'image' ? 'image' : 'text',
                source: item.role == 'user' ? 'user' : 'AI',
                original_type: '1',
                content: {
                  content: item.content
                }
              })
            }
          } else {
            this.initTips()
          }
        })
        .catch((err) => {
          console.log(err)
          if(this.pushChatting.length<1){
            this.initTips()
          }
          // 
        })
    },
    async aimessage(query) {
      if (!this.$store.state.kaolaStudent.token) {
        return
      }
      if (this.original_type == '1' || this.phone.show) {
        if (!this.id) {
          await student.question
            .lastai({
              original_type: this.original_type
            })
            .then(res => {
              this.id = res.data.id
            })
        }

        if (!query) {
          return
        }
        query.id = this.id
        let content = {
          content: ''
        }
        this.pushChatting({
          type: 'text',
          source: 'AI',
          content: content
        })
        let is_end = false
        this.mask = true
        //最后一个用户问题
        this.current = query
        aimessageStream(query, res => {
          if (res != '[DONE]') {
            content.content += res.data.content
            this.$store.commit('kaolaStudent/scrollTopChange')
            if (res.data.is_end == 1) {
              is_end = true
            }
          } else if (is_end) {
            this.stopChange(2)
          }
          if (res == '[DONE]') {
            this.mask = false
          }
        })
        setTimeout(() => {
          this.mask = false
        }, 1000 * 20)
      } else if (this.original_type == '2') {
        this.pushChatting({
          type: 'text',
          source: 'AI',
          content: {
            content: '好的，考拉这就为你接通老师进行答疑'
          }
        })
        this.askAbout.show = true
        this.askAbout.data = query
      }
    },
    askAboutTap() {
      this.askAbout.show = false
      this.$store.commit('kaolaStudent/setQingBeiAnswerin', {
        show: true,
        query: this.askAbout.data
      })
    },
    confirm() {
      if (this.text.length < 2) {
        this.$xh.Toast('输入文本过短！')
        return
      }
      if (this.lastTimeContent == this.text) {
        this.$xh.Toast('内容重复！')
        return
      }
      this.lastTimeContent = this.text
      let query = {
        content: this.text,
        message_type: 'text',
        message: this.text
      }
      this.pushChatting({
        type: 'text',
        source: 'user',
        content: query
      })
      this.aimessage(query)
      this.text = ''
    },
    sentenceRecognition(text) {
      let query = {
        content: text,
        message_type: 'text',
        message: text
      }
      this.pushChatting({
        type: 'text',
        source: 'user',
        content: query
      })
      this.aimessage(query)
    },
    picturesSuccess(fileUrl) {
      upLoad(fileUrl)
        .then(url => {
          let src = this.$xh.completepath(url)
          let query = {
            content: src,
            message_type: 'image',
            message: src
          }
          this.pushChatting({
            type: 'image',
            source: 'user',
            content: query
          })
          this.aimessage(query)
        })
        .catch(res => {
          this.$xh.Toast('上传文件失败！')
        })
    },
    chooseMedia() {
      this.$xh.push(
        'kaolaStudent',
        'pages/home/<USER>' + this.picturesEmitName
      )
      return
      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        maxDuration: 30,
        camera: 'back',
        success: res => {
          wx.cropImage({
            src: res.tempFiles[0].tempFilePath,
            cropScale: '4:3',
            success: res => {
              console.log(res)
              upLoad(res.tempFilePath)
                .then(url => {
                  let src = this.$xh.completepath(url)
                  let query = {
                    content: src,
                    message_type: 'image',
                    message: src
                  }
                  this.pushChatting({
                    type: 'image',
                    source: 'user',
                    content: query
                  })
                  this.aimessage(query)
                })
                .catch(res => {
                  this.$xh.Toast('上传文件失败！')
                })
            },
            fail(err) {
              console.log(err)
            }
          })
        }
      })
    },
    pushChatting(data) {
      this.$store.commit('kaolaStudent/pushChatting', data)
    }
  }
}
</script>

<style lang="scss" scoped>
.input-box {
  width: 351px;
  min-height: 50px;
  background: #222333;
  border-radius: 12px;
  padding: 0 8px;
  display: flex;
  align-items: flex-end;
  position: relative;
  .mask {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 9999;
  }
  .icon {
    flex-shrink: 0;
    padding: 8px;
    display: flex;
    align-items: center;
    height: 50px;
    image {
      width: 24px;
      height: 24px;
    }
  }
  .recorderManager {
    flex: 1;
    height: 100%;
    // display: flex;
  }
  .input-content {
    flex: 1;
    // min-height: 50px;
    // padding-top: 15px;
    display: flex;
    align-items: center;
    // padding: 10px 0;
    .input {
      padding: 16px 4px 14px 4px;
      width: 100%;
      min-height: 20px;
      font-size: 16px;
      color: #fff;
    }
    //  .input :focus {
    //   min-height: 35px;
    //   padding-bottom: 0;
    //  }
    :deep(.placeholderStyle) {
      font-weight: 400;
      font-size: 16px;
      color: rgba(255, 255, 255, 0.65);
    }
  }
}
</style>
