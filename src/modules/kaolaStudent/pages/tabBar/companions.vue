<template>
  <view class="my-page">
    <view
      class="back"
      :style="backgroundSize"
      :class="{
        back2: tabIndex == 2
      }"
    >
      <tabBar
        current="companions"
        position="relative"
        color="rgba(34,35,51,0.8)"
        activeColor="#222333"
        tabBarActive="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/1a5017441831167812547_Vector%202%402x%20(1).png"
      ></tabBar>
      <view class="head">
        <image
          @click="$xh.push('kaolaStudent', 'pages/my/coursePurchaseRecord')"
          class="order"
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/532e174684036315061084_Frame%402x.png"
        ></image>
        <view class="tabs">
          <view
            class="item"
            :class="{
              active: tabIndex == '1'
            }"
            @click="tabIndex = '1'"
          >
            <view>预约</view>
          </view>
          <view
            class="item"
            :class="{
              active: tabIndex == '2'
            }"
            @click="tabIndex = '2'"
          >
            <view>课表</view>
          </view>
        </view>
      </view>
      <view class="page-content">
        <reservation v-show="tabIndex == '1'"></reservation>
        <timetable v-show="tabIndex == '2'" :pageShow="pageShow" ref="timetable"></timetable>
      </view>
    </view>
    <unpaid :pageShow="pageShow"></unpaid>
    <!-- <view class="lb">
      <img src="https://scdn.winyoungreading.com/applet/weChat/test/lb-2.png" alt="lanh" mode="aspectFit">
    </view> -->
  </view>
</template>

<script>
import tabBar from '../../components/commen/tabBar.vue'
import reservation from '../../components/companions/reservation.vue'
import timetable from '../../components/companions/timetable/index.vue'
import unpaid from '../../components/commen/unpaid.vue'
export default {
  components: {
    tabBar,
    reservation,
    timetable,
    unpaid
  },
  data() {
    return {
      tabIndex: '1',
      pageShow: true,
      backgroundSize: ''
    }
  },
  computed: {
    token() {
      return this.$store.state.kaolaStudent.token
    }
  },
  onLoad() {
    let initbarheight = 45
    let BarHeight = wx.getSystemInfoSync().statusBarHeight || initbarheight
    this.backgroundSize = `background-size: 100vw ${
      282 + BarHeight - initbarheight
    }px;`
    uni.$on('timetableTabChange', index => {
      if (index == 2) {
        this.backgroundSize = `background-size: 100vw ${
          282 + BarHeight - initbarheight
        }px;`
      } else {
        this.backgroundSize = `background-size: 100vw ${
          282 + BarHeight - initbarheight - 20
        }px;`
      }
    })
    
  },
  onShow() {
    this.pageShow = true
  },
  onHide() {
    this.pageShow = false
  },
  methods: {},
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  },
  watch: {
    tabIndex(newVal) {
      if (newVal === '2' && this.$refs.timetable) {
        // 调用timetable组件的刷新方法
        this.$refs.timetable.refreshData();
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// 蓝湖样式测试
// .lb{
//   width: 375px;
//   height: 821px;
//   position: absolute;
//   top: 0;
//   left: 0;
//   z-index: 999;
//   img{
//     opacity: .5;
//     width: 375px;
//     height: 821px;
//   }
// }
.my-page {
  overflow-y: auto;
  background-color: #f3f5f8;
  .back {
    display: flex;
    flex-direction: column;
    height: 100vh;
    border-radius: 0px 0px 0px 0px;
    background-image: url(http://xy-kaola.oss-cn-beijing.aliyuncs.com/public/4dc7174705087755916354_Mask%20group%402x.png);
    background-repeat: no-repeat;
    background-size: 100vw 282px;
  }
  .back2 {
    background-image: url(http://xy-kaola.oss-cn-beijing.aliyuncs.com/public/6383174736861850949206_asdfgasdflkjsld.png);
    background-repeat: no-repeat;
    background-size: 100vw 282px;
  }
  .head {
    padding: 11px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    .order {
      position: absolute;
      left: 0;
      border: 12px solid rgba($color: #000000, $alpha: 0);
      width: 24px;
      height: 24px;
    }
  }
  .tabs {
    width: 94px;
    height: 32px;
    background: #dbf46b;
    border-radius: 27px 27px 27px 27px;
    border-radius: 27px 27px 27px 27px;
    display: flex;
    align-items: center;
    justify-content: center;
    .item {
      width: 44px;
      height: 26px;
      font-size: 14px;
      text-align: center;
      line-height: 26px;
      border-radius: 18px 18px 18px 18px;
      font-weight: 400;
      font-size: 12px;
      color: #222333;
    }
    .active {
      background: #222333;
      font-weight: 400;
      font-size: 12px;
      color: #ffffff;
      font-weight: bold;
    }
  }
  .page-content {
    flex: 1;
    height: 500;
    overflow-y: auto;
  }
}
</style>
