<template>
  <view class="input-box">
    <view class="input-content" v-if="inputType == 1">
      <textarea
        class="input"
        v-model="text"
        inputmode="text"
        type="text"
        confirm-type="send"
        auto-height
        @confirm="confirm"
        placeholder-style="placeholderStyle"
        adjust-keyboard-to="bottom"
        placeholder="自由输入..."
        cursor-spacing="25"
      >
      </textarea>
      <!-- :show-confirm-bar="false" -->
      <!-- confirm-type="return" -->
    </view>
    <view class="recorderManager" v-if="inputType == 2">
      <recorderManager
        @sentenceRecognition="sentenceRecognition"
      ></recorderManager>
    </view>
    <view class="icon" v-if="inputType == 1" @click="inputTypeChange(2)">
      <image src="/static/imgs/kaolaStudent/input.svg"></image>
    </view>
    <view class="icon" v-if="inputType == 2" @click="inputTypeChange(1)">
      <image src="/static/imgs/kaolaStudent/jianpan.svg"></image>
    </view>
    <view v-if="text.length == 0" class="icon" @click="chooseMedia">
      <image src="/static/imgs/kaolaStudent/zhaoxiang.svg"></image>
    </view>
    <view v-else class="icon" @click="confirm" style="color: #fff"> 发送 </view>
  </view>
</template>

<script>
import recorderManager from '../../home/<USER>/recorderManager.vue'
import { student, appoint } from '../../../api/index'
import { upLoad, json_parse, goToLogin } from '../../../utils'

export default {
  props: {
    question_id: {
      type: String,
      default: ''
    }
  },
  components: {
    recorderManager
  },
  data() {
    return {
      inputType: uni.getStorageSync('__inputType__') || 2,
      text: '',
      lastTimeContent: '',
      list: [],
      picturesEmitName: 'companionsPictures'
    }
  },
  created() {
    uni.$on(this.picturesEmitName, this.picturesSuccess)
  },

  methods: {
    initList(arr) {
      this.list.push(...arr)
    },
    inputTypeChange(type) {
      this.inputType = type
      uni.setStorageSync('__inputType__', type)
    },
    async aimessage(query) {
      if (!query) {
        return
      }
      query.id = this.question_id
      appoint.teacher.remark(query).then(res => {
        this.$emit('change', this.list)
      })
    },
    confirm() {
      if (this.text.length < 2) {
        this.$xh.Toast('输入文本过短！')
        return
      }
      if (this.lastTimeContent == this.text) {
        this.$xh.Toast('内容重复！')
        return
      }
      this.lastTimeContent = this.text
      let query = {
        content: this.text,
        message_type: 'text',
        message: this.text
      }
      this.pushChatting({
        type: 'text',
        source: 'user',
        content: query
      })
      this.aimessage(query)
      this.text = ''
    },
    sentenceRecognition(text) {
      let query = {
        content: text,
        message_type: 'text',
        message: text
      }
      this.pushChatting({
        type: 'text',
        source: 'user',
        content: query
      })
      this.aimessage(query)
    },
    picturesSuccess(fileUrl) {
      upLoad(fileUrl)
        .then(url => {
          let src = this.$xh.completepath(url)
          let query = {
            content: src,
            message_type: 'image',
            message: src
          }
          this.pushChatting({
            type: 'image',
            source: 'user',
            content: query
          })
          this.aimessage(query)
        })
        .catch(res => {
          this.$xh.Toast('上传文件失败！')
        })
    },
    chooseMedia() {
      this.$xh.push(
        'kaolaStudent',
        'pages/home/<USER>' + this.picturesEmitName
      )
      return
      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        maxDuration: 30,
        camera: 'back',
        success: res => {
          wx.cropImage({
            src: res.tempFiles[0].tempFilePath,
            cropScale: '4:3',
            success: res => {
              console.log(res)
              upLoad(res.tempFilePath)
                .then(url => {
                  let src = this.$xh.completepath(url)
                  let query = {
                    content: src,
                    message_type: 'image',
                    message: src
                  }
                  this.pushChatting({
                    type: 'image',
                    source: 'user',
                    content: query
                  })
                  this.aimessage(query)
                })
                .catch(res => {
                  this.$xh.Toast('上传文件失败！')
                })
            },
            fail(err) {
              console.log(err)
            }
          })
        }
      })
    },
    pushChatting(data) {
      this.list.push(data)
    }
  }
}
</script>

<style lang="scss" scoped>
.input-box {
  width: calc(100% - 30px);
  min-height: 50px;
  background: #222333;
  border-radius: 12px;
  padding: 0 8px;
  display: flex;
  align-items: flex-end;
  position: fixed;
  left: 15px;
  bottom: 20px;
  .mask {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 9999;
  }
  .icon {
    flex-shrink: 0;
    padding: 8px;
    display: flex;
    align-items: center;
    height: 50px;
    image {
      width: 24px;
      height: 24px;
    }
  }
  .recorderManager {
    flex: 1;
    height: 100%;
    // display: flex;
  }
  .input-content {
    flex: 1;
    // min-height: 50px;
    // padding-top: 15px;
    display: flex;
    align-items: center;
    // padding: 10px 0;
    .input {
      padding: 16px 4px 14px 4px;
      width: 100%;
      min-height: 20px;
      font-size: 16px;
      color: #fff;
    }
    //  .input :focus {
    //   min-height: 35px;
    //   padding-bottom: 0;
    //  }
    :deep(.placeholderStyle) {
      font-weight: 400;
      font-size: 16px;
      color: rgba(255, 255, 255, 0.65);
    }
  }
}
</style>
