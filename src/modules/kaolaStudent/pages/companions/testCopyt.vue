<template>
  <div>
    <view class="AI-text">{{ text }}</view>

    <image
      :src="latexToImageUrl('\(ax^{2}+bx + c = 0\)（\(a\neq0\)）')"
      style="width: 260px; height: auto"
      mode="widthFix"
    ></image>

    <image
      :src="latexToImageUrl('\(\Delta=b^{2}-4ac\)')"
      style="width: 260px; height: auto"
      mode="widthFix"
    ></image>
  </div>
</template>

<script>
export default {
  data() {
    return {
      imagePath:
        'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/803e174718652922139276_470d96ef6fc285015b6de274cefe6ac.png',

      text: `嘿，同学！我懂你想问啥啦😉。你先想想哦，一般的一元二次方程长什么样子呀？是不是可以写成\(ax^{2}+bx + c = 0\)（\(a\neq0\)）的形式呢？

那我们来进一步思考下，为什么要专门弄个根公式去求它的根呀？其实是因为不是所有一元二次方程都能很容易地用因式分解这些方法求解~

现在给你个小提示哦，推导根公式会用到配方法呢。对于方程\(ax^{2}+bx + c = 0\)（\(a\neq0\)），先把二次项系数化为\(1\)，然后进行配方操作。

你来总结总结，经过一系列推导后得到的那个可以直接求一元二次方程根的公式是什么样的呢🧐？

你还可以去想想，如果把根公式里判别式\(\Delta=b^{2}-4ac\)的值改变一下，比如\(\Delta>0\)、\(\Delta = 0\)、\(\Delta<0\)，方程的根分别会有什么情况哦。 `
    }
  },
  created() {
    console.log(this.splitLatexSegments(this.text))
  },
  methods: {
    splitLatexSegments(text) {
      // 定义匹配模式，按优先级排序（长格式优先）
      const patterns = [
        { regex: /(?<!\\)\$\$([\s\S]*?)(?<!\\)\$\$/g, type: 'display' }, // $$...$$
        { regex: /(?<!\\)\\\[([\s\S]*?)(?<!\\)\\\]/g, type: 'display' }, // \[...\]
        { regex: /(?<!\\)\$([\s\S]*?)(?<!\\)\$/g, type: 'inline' }, // $...$
        { regex: /(?<!\\)\\\(([\s\S]*?)(?<!\\)\\\)/g, type: 'inline' } // \(...\)
      ]

      let segments = [] // 存储分割后的片段
      let currentIndex = 0 // 当前处理位置

      while (currentIndex < text.length) {
        let earliestMatch = null
        let earliestType = null

        // 检查所有模式，找到当前最早的匹配
        for (const { regex, type } of patterns) {
          regex.lastIndex = currentIndex // 从当前位置开始匹配
          const match = regex.exec(text)
          if (
            match &&
            (earliestMatch === null || match.index < earliestMatch.index)
          ) {
            earliestMatch = match
            earliestType = type
          }
        }

        // 无匹配时，添加剩余文本并退出
        if (!earliestMatch) {
          segments.push({ type: 'text', content: text.slice(currentIndex) })
          break
        }

        // 添加匹配前的文本
        if (earliestMatch.index > currentIndex) {
          segments.push({
            type: 'text',
            content: text.slice(currentIndex, earliestMatch.index)
          })
        }

        // 添加公式片段
        segments.push({
          type: 'formula',
          content: earliestMatch[1].trim(), // 捕获组内容，去除首尾空格
          formulaType: earliestType
        })

        // 更新当前位置到匹配结束处
        currentIndex = earliestMatch.index + earliestMatch[0].length
      }

      return segments
    },
    latexToImageUrl(formula) {
      return `https://latex.codecogs.com/png.latex?\\dpi{150}${encodeURIComponent(
        formula
      )}`
    }
  }
}
</script>

<style lang="scss" scoped>
.AI-text {
  margin: 5px 0;
  padding: 8px 12px;
  font-weight: 400;
  font-size: 13px;
  color: #222333;
  line-height: 20px;
  text-align: left;
  max-width: 65.0666vw;
  background: rgba(34, 35, 51, 0.1);
  border-radius: 10px;
  border: 1px solid #ffffff;
  margin-right: auto;
  white-space: pre-line;
}
</style>
