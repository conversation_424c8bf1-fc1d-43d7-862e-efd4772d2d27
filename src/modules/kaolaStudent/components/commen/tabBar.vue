<template>
  <view
    class="tabBar"
    :style="{
      position: position
    }"
  >
    <view :style="statusBarHeight"></view>
    <view class="tabs">
      <view
        class="tab-item"
        :class="{
          current: current == item.id
        }"
        v-for="item of list"
        :key="item.id"
        @click="tap(item)"
      >
        <view
          class="item-text bold-font"
          :style="{
            color: current == item.id ? activeColor : color
          }"
          >{{ item.text }}</view
        >
        <image class="tab-image" v-if="current == item.id" :src="tabBarActive"></image>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    current: {
      type: String,
      default: 'home'
    },
    position: {
      type: String,
      default: 'fixed'
    },
    color: {
      type: String,
      default: '#ffffff'
    },
    activeColor: {
      type: String,
      default: '#ffffff'
    },
    tabBarActive: {
      type: String,
      default: '/static/imgs/kaolaStudent/tabBarActive.svg'
    }
  },
  data() {
    return {
      statusBarHeight: 'height:25px;',
      // tabBarActive: '/static/imgs/kaolaStudent/tabBarActive.svg',
      list: [
        {
          id: 'plaza',
          pagePath: 'pages/tabBar/plaza',
          text: '广场',
          iconPath: 'static/imgs/jintiku/home.png',
          selectedIconPath: 'static/imgs/jintiku/home-active.png'
        },
        {
          id: 'home',
          pagePath: 'pages/tabBar/home',
          text: '答疑',
          iconPath: 'static/imgs/jintiku/home.png',
          selectedIconPath: 'static/imgs/jintiku/home-active.png'
        },
        {
          id: 'companions',
          pagePath: 'pages/tabBar/companions',
          text: '伴学',
          iconPath: 'static/imgs/jintiku/examination.png',
          selectedIconPath: 'static/imgs/jintiku/examination-active.png'
        },
        {
          id: 'my',
          pagePath: 'pages/tabBar/my',
          text: '我的',
          iconPath: 'static/imgs/jintiku/examination.png',
          selectedIconPath: 'static/imgs/jintiku/examination-active.png'
        }
      ]
    }
  },
  created() {
    let BarHeight = (wx.getSystemInfoSync().statusBarHeight || 25) - 7
    this.statusBarHeight = `height:${BarHeight}px;`
    console.log(this.statusBarHeight)
  },
  methods: {
    tap(item) {
      uni.switchTab({
        url: `/modules/kaolaStudent/${item.pagePath}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.tabBar {
  position: fixed;
  z-index: 10;
  left: 0;
  top: 0;
  width: 100vw;
}
.tabs {
  padding: 0 10px;
  height: 54px;
  display: flex;
  align-items: center;
  .tab-item {
    height: 100%;
    width: 68px;
    display: flex;
    align-items: center;
    justify-content: center;

    .item-text {
      font-weight: 500;
      font-size: 20px;
      color: #ffffff;
    }
  }
  .current {
    position: relative;
    .bold-font {
      font-weight: bold;
    }
    .tab-image {
      position: absolute;
      left: calc(50% - 12px);
      bottom: 7px;
      width: 23.02px;
      height: 8px;
    }
  }
}
</style>
