<template>
  <view class="home-page">
    <tabBar
      current="home"
      color="rgba(34,35,51,0.8)"
      activeColor="#222333"
      tabBarActive="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/1a5017441831167812547_Vector%202%402x%20(1).png"
    ></tabBar>
    <!-- 空模版暂时没看见使用 -->
    <!-- <broadcast></broadcast> -->
     <!-- 人物形象 -->
    <theme></theme>
    <!-- 对话列表 -->
    <view class="chatting">
      <chatting></chatting>
    </view>
    <bottom></bottom>
    

    <liveStatusIcon></liveStatusIcon>
    <!-- <view class="test">
      <image class="img" src="/static/imgs/kaolaStudent/home-ui.png"></image>
    </view> -->
    <!-- <view style="display: none">
      <image v-for="s of loadImg"></image>
    </view> -->
    <unpaid :pageShow="pageShow"></unpaid>
  </view>
</template>

<script>
import bottom from '../../components/home/<USER>/index.vue'
import { goToLogin } from '../../utils/index'
import tabBar from '../../components/commen/tabBar.vue'
import broadcast from '../../components/home/<USER>/broadcast.vue'
import theme from '../../components/home/<USER>/theme.vue'
import chatting from '../../components/home/<USER>/index.vue'
import { config, activity } from '../../api/index'
import liveStatusIcon from '../../components/home/<USER>'
import unpaid from '../../components/commen/unpaid.vue'
export default {
  components: {
    bottom,
    tabBar,
    broadcast,
    theme,
    chatting,
    liveStatusIcon,
    unpaid
  },
  data() {
    return {
      chattingTopHeight: '',
      loadImg: [
        'https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/5090174566367495584184_Group%20243%402x.png'
      ],
      query: {},
      pageShow: true
    }
  },
  onLoad(query) {
    let height = (wx.getSystemInfoSync().statusBarHeight || 25) + 54 + 160//143  //- 7
    this.chattingTopHeight = `top: ${height}px;height: calc(100vh - ${height}px - 134px);`
    this.query = query
    console.log("home-query"+JSON.stringify(query))
  },
  onShow() {
    this.statistics()
    this.pageShow = true
    console.log("onShow-query:"+uni.getStorageSync('shareUserXyppid'))
  },
  onHide() {
    this.pageShow = false
  },
  methods: {
    statistics() {
      if (!this.$store.state.kaolaStudent.token) {
        if (this.query.scene) {
          uni.setStorageSync('shareUserXyppid', this.query.scene)
        }
        console.log("statistics-query:"+uni.getStorageSync('shareUserXyppid'))
        return
      }
      let shareUserXyppid =
        this?.query?.scene || uni.getStorageSync('shareUserXyppid')
      if (shareUserXyppid) {
        uni.removeStorageSync('shareUserXyppid')
        activity
          .add({
            xyppid: shareUserXyppid
          })
          .then(res => {
            console.log('记录xyppid成功:' + shareUserXyppid)
          })
      }
      if (!this.$store.state.kaolaStudent.xyppid) {
        activity
          .wxQrCode({
            activity_id: '562223936276794609',
            promotion_plan_id: '562223936897551601',
            is_student: '1',
            student_id: this.$store.state.kaolaStudent.userinfo.student_id
          })
          .then(res => {
            if(res.data && res.data.length){
              const e = res.data[0]
              this.$store.commit('kaolaStudent/setXyppid', e.xyppid)
            }
          })
      }
    }
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>

<style lang="scss" scoped>
// .test{
//   width: 375px;
//   height: 812px;
//   position: absolute;
//   left: 0;
//   top: 0;
//   // background-color: red;
//   z-index: 99;
//   .img{
//     width: 100%;
//     height: 100%;
//     opacity: .5;
//   }
// }
.chatting {
  position: fixed;
  // top: 149px;
  top: 243px;
  left: 0;
  z-index: 1;
  height: calc(100vh - 243px - 134px);
  width: 100vw;
}

</style>
