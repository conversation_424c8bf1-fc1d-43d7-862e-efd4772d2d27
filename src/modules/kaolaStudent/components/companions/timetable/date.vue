<template>
  <view class="calendar-container">
    <view class="week-header">
      <view v-for="(weekDay, i) in weekDays" :key="i">{{ weekDay }}</view>
    </view>
    <!-- 周历模式 -->
    <swiper
      v-if="!isExpanded"
      class="week-swiper"
      @change="onWeekChange"
      :current="weekCurrent"
    >
      <swiper-item v-for="(week, index) in weekList" :key="index">
        <view class="table-row">
          <view
            v-for="(day, i) in week.days"
            :key="i"
            class="day-cell"
            :class="{
              'selected-day': isSelected(day.date, selectedDate),
              status1: isData(day.date)
            }"
            @click="selectDate(day.date)"
          >
            <view class="text">
              {{ day.date.getDate() }}
            </view>
          </view>
        </view>
      </swiper-item>
    </swiper>

    <!-- 月历模式 -->
    <swiper
      v-else
      class="month-swiper"
      :style="{
        height: monthSwiperheight
      }"
      @change="onMonthChange"
      :current="monthCurrent"
    >
      <swiper-item v-for="(month, index) in monthList" :key="index">
        <view class="month-container">
          <view class="table-row">
            <view
              v-for="(day, i) in month.days"
              :key="i"
              class="day-cell"
              :class="{
                'other-month': !day.isCurrentMonth,
                'selected-day': isSelected(day.date, selectedDate),
                status1: isData(day.date)
              }"
              @click="selectDate(day.date)"
            >
              <view class="text">
                {{ day.date.getDate() }}
              </view>
            </view>
          </view>
        </view>
      </swiper-item>
    </swiper>

    <!-- 展开/收起按钮 -->
    <view class="toggle-btn" @click="toggleExpand">
      <!-- {{ isExpanded ? '收起' : '展开' }} -->
      <image
        :class="{
          hide: isExpanded
        }"
        class="img"
        src="/static/imgs/kaolaStudent/mwshow.svg"
      ></image>
    </view>
  </view>
</template>

<script>
const weekDays = ['一', '二', '三', '四', '五', '六', '日']
import dayjs from 'dayjs'
export default {
  props: {
    data: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      isExpanded: false,
      currentDate: new Date(),
      selectedDate: new Date(),
      weekList: [],
      monthList: [],
      weekCurrent: 0,
      monthCurrent: 0,
      weekDays
    }
  },
  created() {
    this.initCalendar()
  },
  computed: {
    monthSwiperheight() {
      const current = this.monthList[this.monthCurrent]
      if (current) {

        return `calc(24px + 33px * ${Math.floor(current.days.length / 7)})`
      } else {
        return 'calc(33px * 5 + 24px)'
      }
    }
  },
  methods: {
    isData(d1) {
      return !!this.data.find(d2 => dayjs(d1).format('YYYY-MM-DD') == d2)
    },
    isSelected(d1, d2) {
      return dayjs(d1).format('YYYY-MM-DD') == dayjs(d2).format('YYYY-MM-DD')
    },
    dateText(d1) {
      if (dayjs(d1).format('YYYY-MM-DD') == dayjs().format('YYYY-MM-DD')) {
        return '今天'
      } else {
        return d1.getDate()
      }
    },
    initCalendar() {
      this.generateWeekList()
      this.generateMonthList()
      this.onWeekChange({
        detail: {
          current: 0
        }
      })
    },

    // 生成周列表
    generateWeekList(baseDate = this.currentDate) {
      let weekList = []
      for (let i = -1; i < 5 * 7; ++i) {
        weekList.push(
          this.getWeekData(new Date(baseDate.getTime() + 604800000 * i))
        )
      }
      this.weekList = weekList
    },

    // 生成月列表
    generateMonthList(baseDate = this.currentDate) {
      let monthList = []
      for (let i = 0; i < 7; ++i) {
        monthList.push(
          this.getMonthData(
            new Date(baseDate.getFullYear(), baseDate.getMonth() + i, 1)
          )
        )
      }
      this.monthList = monthList
    },

    // 获取周数据
    getWeekData(date) {
      const days = []
      const day = date.getDay()
      const start = new Date(date.getTime())

      for (let i = 0; i < 7; i++) {
        const currentDate = new Date(start.getTime() + i * 86400000)
        days.push({
          date: currentDate
        })
      }

      return { days }
    },

    // 获取月数据
    getMonthData(date) {
      const year = date.getFullYear()
      const month = date.getMonth()
      const firstDay = new Date(year, month, 1)
      const lastDay = new Date(year, month + 1, 0)
      const days = []

      // 填充上月天数
      const startDay = firstDay.getDay() == 0 ? 6 : firstDay.getDay() - 1
      for (let i = 0; i < startDay; i++) {
        const d = new Date(year, month, -startDay + i + 1)
        days.push({
          date: d,
          isCurrentMonth: false
        })
      }

      // 填充本月天数
      for (let i = 1; i <= lastDay.getDate(); i++) {
        const d = new Date(year, month, i)
        days.push({
          date: d,
          isCurrentMonth: true
        })
      }

      // 填充下月天数
      const endDay = lastDay.getDay() - 1
      for (let i = 1; i < 7 - endDay; i++) {
        const d = new Date(year, month + 1, i)
        days.push({
          date: d,
          isCurrentMonth: false
        })
      }

      return { year, month, days }
    },

    // 切换展开状态
    toggleExpand() {
      this.isExpanded = !this.isExpanded

      if (this.isExpanded) {
        //周切月
        let d2 = this.weekList[this.weekCurrent].days[0]
        this.monthList.forEach((d, i) => {
          for (let d1 of d.days.filter(e => e.isCurrentMonth)) {
            if (this.isSelected(d1.date, d2.date)) {
              this.onMonthChange({
                detail: {
                  current: i
                }
              })
              // this.monthCurrent = i
            }
          }
        })
      } else {
        if (
          this.monthList[this.monthCurrent].days
            .filter(e => e.isCurrentMonth)
            .find(d1 => this.isSelected(d1.date, this.selectedDate))
        ) {
          this.weekList.forEach((d, i) => {
            for (let d1 of d.days) {
              if (this.isSelected(d1.date, this.selectedDate)) {
                this.weekCurrent = i

                this.onWeekChange({
                  detail: {
                    current: i
                  }
                })
              }
            }
          })
        } else {
          this.weekList.forEach((d, i) => {
            for (let d1 of d.days) {
              if (
                this.isSelected(
                  d1.date,
                  this.monthList[this.monthCurrent].days.filter(
                    e => e.isCurrentMonth
                  )[0].date
                )
              ) {
                this.onWeekChange({
                  detail: {
                    current: i
                  }
                })
              }
            }
          })
        }
      }
    },

    // 选择日期
    selectDate(date) {
      this.selectedDate = date
      this.$emit('select', dayjs(date).format('YYYY-MM-DD'))
      this.$emit('input', dayjs(date).format('YYYY-MM-DD'))
      if (this.isExpanded) {
        this.toggleExpand()
      }
    },

    // 周切换
    onWeekChange(e) {
      const current = this.weekList[e.detail.current]
      this.weekCurrent = e.detail.current
      this.currentDate = current.days[0].date
      this.$emit('change', {
        start: dayjs(current.days[0].date).format('YYYY-MM-DD'),
        end: dayjs(current.days[current.days.length - 1].date).format(
          'YYYY-MM-DD'
        )
      })
    },

    // 月切换
    onMonthChange(e) {
      const current = this.monthList[e.detail.current]
      this.monthCurrent = e.detail.current
      this.currentDate = new Date(current.year, current.month, 1)

      this.$emit('change', {
        start: dayjs(current.days[0].date).format('YYYY-MM-DD'),
        end: dayjs(current.days[current.days.length - 1].date).format(
          'YYYY-MM-DD'
        )
      })
    },

    // 判断是否为同一天
    isSameDay(d1, d2) {
      return (
        d1.getFullYear() === d2.getFullYear() &&
        d1.getMonth() === d2.getMonth() &&
        d1.getDate() === d2.getDate()
      )
    }
  }
}
</script>

<style scoped lang="scss">
.calendar-container {
  padding: 12px 0px 4px 0px;
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(4px);
}

.toggle-btn {
  height: 24px;
  line-height: 24px;
  color: #233;
  font-size: 12px;
  text-align: center;
  .img {
    width: 24px;
    height: 24px;
  }
  .hide {
    transform: rotateX(180deg);
  }
}

.week-container,
.month-grid {
  display: flex;
  flex-wrap: wrap;
}

.week-header {
  width: 100%;
  display: flex;
  view {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    flex: 1;
    height: 15px;
    font-weight: 400;
    font-size: 11px;
    color: rgba(34, 35, 51, 0.85);
  }
}
.table-row {
  width: 100%;
  display: flex;
  padding-top: 4px;
  padding-bottom: 6px;
  flex-wrap: wrap;
  .day-cell {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    // flex: 1;
    width: calc(100% / 7);

    .text {
      font-weight: 800;
      font-size: 14px;
      color: rgba(34, 35, 51, 0.85);
      width: 32px;
      height: 32px;
      // background: #dbf46b;
      border-radius: 8px 8px 8px 8px;
      line-height: 32px;
      text-align: center;
    }
  }
  .status1 {
    .text {
      background: rgba(219, 244, 107, 0.35);
      // border-radius: 50%;
    }
  }
  .selected-day {
    .text {
      background: #dbf46b;
      // border-radius: 50%;
    }
  }
  .other-month {
    .text {
      color: #ccc;
    }
  }
}

.week-swiper {
  height: 39px;
}
.month-swiper {
  height: calc(33px * 5 + 10px);
  .month-container {
    display: flex;
    flex-direction: column;
  }
}

.month-header {
  text-align: center;
  padding: 20rpx 0;
  font-weight: bold;
}
</style>
