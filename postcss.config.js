const path = require('path')
module.exports = {
  parser: require('postcss-comment'),
  plugins: [
    require('postcss-import')({
      resolve (id, basedir, importOptions) {
        if (id.startsWith('~@/')) {
          return path.resolve(process.env.UNI_INPUT_DIR, id.substr(3))
        } else if (id.startsWith('@/')) {
          return path.resolve(process.env.UNI_INPUT_DIR, id.substr(2))
        } else if (id.startsWith('/') && !id.startsWith('//')) {
          return path.resolve(process.env.UNI_INPUT_DIR, id.substr(1))
        }
        return id
      }
    }),
    require('autoprefixer')({
      remove: process.env.UNI_PLATFORM !== 'h5'
    }),
    require('@dcloudio/vue-cli-plugin-uni/packages/postcss'),
    // 添加 postcss-px-to-viewport 插件配置
    require('postcss-px-to-viewport')({
      unitToConvert: 'px', // 需要转换的单位，默认为 "px"
      viewportWidth: 375, //  375px 设计稿的视口宽度 蓝湖 *2  750 * 1624
      unitPrecision: 8, // 单位转换后保留的精度
      propList: ['*'], // 能转化为 vw 的属性列表，['*'] 表示所有属性
      viewportUnit: 'vw', // 希望使用的视口单位
      fontViewportUnit: 'vw', // 字体使用的视口单位
      selectorBlackList: [], // 需要忽略的 CSS 选择器，不会转为视口单位，使用原有的 px 等单位。
      minPixelValue: 1, // 设置最小的转换数值，如果小于这个数值，px 单位就不会被转换
      mediaQuery: false, // 媒体查询里的单位是否需要转换单位
      replace: true, // 是否直接更换属性值，而不添加备用属性
      exclude: [], // 忽略某些文件夹下的文件或特定文件，例如 'node_modules'
      // include: undefined, // 如果设置了include，那将只有匹配到的文件才会被转换
      landscape: false, // 是否添加根据 landscapeWidth 生成的媒体查询条件 @media (orientation: landscape)
      // landscapeUnit: 'vw', // 横屏时使用的单位
      // landscapeWidth: 568 // 横屏时使用的视口宽度
    })
  ]
}
