import Vue from 'vue'
import App from './App'
import store from './store'
import uView from 'uview-ui'

// import VConsole from 'vconsole'
import xhutlis from './utlis'
import './modules/kaolaStudent/utils/systemMessage'
Vue.config.productionTip = false
App.mpType = 'app'
// import lodash from 'lodash'
Vue.prototype.$store = store
// Vue.prototype._ = lodash
// 全局公共方法
Vue.prototype.$xh = {}

Vue.use(uView)
for (let fn in xhutlis) {
  Vue.prototype.$xh[fn] = xhutlis[fn]
}
if (process.env.NODE_ENV === 'development') {
  // new VConsole()
}
// #ifdef H5
//url中获取code,如果是推送，openid 在url 中
function getUrlParam(name) {
  let reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)')
  let r = window.location.search.substr(1).match(reg)
  if (r != null) return unescape(r[2])
  return null
}
let openId = getUrlParam('open_id')
openId ? sessionStorage.setItem('user_openid', openId) : ''

// #endif
const app = new Vue({
  render: h => h(App)
})
app.$mount()
