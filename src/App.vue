<script>
// import onLaunch from './utlis/onLaunch.js'
import store from './store'
import updateApp from './mixin/updateApp.js'
import uniPush from './mixin/uniPush.js'
export default {
  mixins: [updateApp, uniPush],
  onShow: function () {
    this._handlePush()
    // 计算屏幕长宽比 更新到全局
    try {
      const res = uni.getSystemInfoSync()
      let proportion =
        res.screenHeight > res.screenWidth
          ? res.screenHeight / res.screenWidth
          : res.screenWidth / res.screenHeight
      store.state.proportion = proportion
    } catch (e) {
      // error
    }
    console.log('App Show')
    let updateManager = uni.getUpdateManager()
    if (!updateManager) {
      return
    } else {
      //新版本更新
      if (uni.canIUse('getUpdateManager')) {
        //判断当前微信版本是否支持版本更新
        updateManager.onCheckForUpdate(function (res) {
          if (res.hasUpdate) {
            // 请求完新版本信息的回调
            updateManager.onUpdateReady(function () {
              uni.showModal({
                title: '更新提示',
                content: '新版本已经准备好，是否重启应用？',
                success: function (res) {
                  if (res.confirm) {
                    // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
                    updateManager.applyUpdate()
                  }
                }
              })
            })
            updateManager.onUpdateFailed(function () {
              uni.showModal({
                // 新的版本下载失败
                title: '已经有新版本了哟~',
                content:
                  '新版本已经上线啦~，请您删除当前小程序，到微信 “发现-小程序” 页，重新搜索打开哦~'
              })
            })
          } else {
          }
        })
      } else {
        uni.showModal({
          // 如果希望用户在最新版本的客户端上体验您的小程序，可以这样子提示
          title: '溫馨提示',
          content:
            '当前微信版本过低，部分功能无法使用，请升级到最新微信版本后重试。'
        })
      }
    }
  }
}
</script>
<style lang="scss">
@import url('./commen.scss');
@import url('./static/css/animate.css');
@import url('@rojer/katex-mini/dist/index.css');
</style>
