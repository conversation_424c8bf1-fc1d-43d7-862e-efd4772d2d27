<template>
  <view class="report" v-if="show">
    <view class="back">
      <view class="title">题目评价</view>
      <view class="text">
        {{ info.evaluate }}
      </view>
      <view class="but" @click="share()">诊断报告</view>
    </view>

    <shareReport
      v-if="shareReportData.show || shareReportData.init"
      :content="shareReportData.data"
      ref="shareReport"
    ></shareReport>
  </view>
</template>

<script>
import { student } from '../../../api/index'
import { json_parse } from '../../../utils'
import dayjs from 'dayjs'
import shareReport from './share-report.vue'
export default {
  components: {
    shareReport
  },
  props: {
    content: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      info: {},
      show: false,
      shareReportData: {
        show: false,
        init: false,
        data: {}
      }
    }
  },
  created() {
    this.detail()
  },
  methods: {
    share() {
      uni.showLoading({
        title: '生成中...'
      })
      if (this.shareReportData.init) {
        this.$refs.shareReport.share(2)
      } else {
        this.shareReportData.show = true
        this.shareReportData.data = { ...this.info, ...this.content }
        setTimeout(() => {
          this.$refs.shareReport.share(2)
          this.shareReportData.init = true
        }, 1000)
      }
    },
    detail() {
      student.question
        .report({
          id: this.content.id,
          noloading: true
        })
        .then(res => {
          if (res.code == '100101') {
            this.pushChatting({
              source: 'AI',
              type: 'tips',
              content: {}
            })
          } else {
            this.show = true
            let created_at = dayjs().format('YYYY/MM/DD HH:mm')
            let content = json_parse(res.data)
            content.created_at = created_at
            this.info = content
            this.pushChatting({
              source: 'AI',
              type: 'qingbei',
              content: {}
            })
          }
          this.$store.commit('kaolaStudent/scrollTopChange')
        })
    },
    pushChatting(data) {
      this.$store.commit('kaolaStudent/pushChatting', data)
    }
  }
}
</script>

<style lang="scss" scoped>
.report {
  width: 81.86vw;
  margin: 5px 0;
  background: linear-gradient(180deg, #d4ff00 0%, #ffffff 40%, #ffffff 100%);
  border-radius: 10px 10px 10px 10px;
  border: 1px solid #ffffff;
  .back {
    background-image: url(https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/182a174600598920085319_Frame%2026%402x.png);
    background-size: 100% 80px;
    background-repeat: no-repeat;
    padding: 0 16px 16px 16px;
    border-radius: 10px 10px 10px 10px;
    .title {
      font-weight: 500;
      font-size: 18px;
      color: #222333;
      line-height: 24px;
      padding: 12px 0;
    }
    .text {
      font-weight: 400;
      font-size: 15px;
      color: rgba(34, 35, 51, 0.85);
      line-height: 22px;
      padding-bottom: 16px;
    }
    .but {
      width: 100%;
      text-align: center;
      height: 32px;
      background: #222333;
      border-radius: 23px 23px 23px 23px;
      font-weight: 400;
      font-size: 15px;
      color: #ffffff;
      line-height: 32px;
    }
  }
}
</style>
