<template>
  <view class="course-list">
    <u-search placeholder="输入您想查找的内容" v-model="searchText"></u-search>
    <view class="list">
      <view class="list-item"></view>
      <view class="list-item"></view>
    </view>
  </view>
</template>

<script> 
export default {
  name: 'courseList',
  data() {
    return {
      searchText: ""
      
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
