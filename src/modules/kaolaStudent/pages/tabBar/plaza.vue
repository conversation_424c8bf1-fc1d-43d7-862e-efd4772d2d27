<template>
  <view class="plaza-page">
    <view class="back" :style="backgroundSize">
      <tabBar
        current="plaza"
        position="relative"
        color="rgba(34,35,51,0.8)"
        activeColor="#222333"
        tabBarActive="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/1a5017441831167812547_Vector%202%402x%20(1).png"
      >
      </tabBar>
      <view class="page-content">
        <view class="swiper-zoon">
          <u-swiper
            class="swiper-class"
            :height="193"
            :list="imageList"
            @click="clickSwiper"
          ></u-swiper>
        </view>
        <view class="card">
          <view class="card-top">
            <view class="title">今日预约</view>
            <view class="right-row" @click="goAppointment">
              <text class="all-appointment">全部预约</text>
              <u-icon name="arrow-right" color="#767781" size="14"></u-icon>
            </view>
          </view>
          <view class="card-info" v-if="todayAppoint.teacher_name">
            <view class="info-item"> 时间：{{ todayAppoint.showDate }} </view>
            <view class="info-item">
              老师：<text class="teacher-name">{{
                todayAppoint.teacher_name
              }}</text>
              <u-avatar
                :src="customComplete(todayAppoint.avatar)"
                :size="32"
              ></u-avatar>
            </view>
          </view>
          <view class="card-info" v-if="!todayAppoint.teacher_name">
            <view class="info-item no-appointment"> 今天还没有预约 </view>
          </view>
        </view>
        <view class="tab-wrap">
          <u-tabs
            :list="tabList"
            @click="clickTab"
            lineColor="#BCED28"
          ></u-tabs>
          <view class="right-row" @click="goMoreLivePage" v-if="pageType === 1">
            <text class="all-appointment">更多推荐</text>
            <u-icon name="arrow-right" color="#767781" size="14"></u-icon>
          </view>
          <view
            class="right-row"
            @click="goMoreCallbackPage"
            v-if="pageType === 2"
          >
            <text class="all-appointment">更多课程</text>
            <u-icon name="arrow-right" color="#767781" size="14"></u-icon>
          </view>
        </view>

        <!-- 课程列表 -->

        <scroll-view
          :scroll-top="scrollTop"
          scroll-y="true"
          :class="
            !todayAppoint.teacher_name ? 'no-record-scroll-y' : 'scroll-y'
          "
        >
          <view class="live-list" v-if="pageType === 1">
            <view
              class="live-item"
              v-for="item in liveList"
              :key="item.id"
              @click="goLiveDetail(item)"
            >
              <view class="live-img">
                <view class="live-tag" v-if="item.status == 2">
                  <text>进行中</text>
                  <image
                    class="live-tag-icon"
                    src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/living.png"
                  />
                </view>
                <view class="grey-bg" v-if="item.status == 1">
                  <text>未开始</text>
                </view>
                <view class="grey-bg" v-if="item.status == 3">
                  <text>已结束</text>
                </view>
                <image
                  class="live-img-img"
                  mode="aspectFill"
                  :src="customComplete(item.material_cover_path)"
                />

                <view class="live-count">
                  <u-icon name="eye"></u-icon>
                  <text class="count-text">{{ item.pv }}</text>
                </view>
              </view>
              <view class="live-content">
                <view class="live-title">
                  <text class="title-text u-line-1">{{ item.name }}</text>
                  <view class="buy-tag" v-if="item.vip_level != 0"
                    >购买会员可看</view
                  >
                </view>
                <view class="live-time">{{ item.showDate }}</view>

                <view class="tearcher-wrap">
                  <u-avatar
                    :src="customComplete(item.avatar)"
                    :size="24"
                  ></u-avatar>
                  <text class="teacher-name">{{ item.teacher_name }}</text>
                  <text class="teacher-name-short u-line-1">{{
                    item.school_name
                  }}</text>
                </view>
              </view>
            </view>
          </view>

          <view class="live-list" v-if="pageType === 2">
            <view
              class="live-item"
              @click="goCallbackDetail(item)"
              v-for="item in callbackList"
              :key="item.id"
            >
              <view class="live-img">
                <!-- <view class="live-tag" v-if="item.status == 3">
                <text>直播中</text>
                <image class="live-tag-icon" src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/living.png" />
              </view> -->

                <image
                  class="live-img-img"
                  mode="aspectFill"
                  :src="customComplete(item.material_cover_path)"
                />
                <view class="live-count">
                  <u-icon name="eye"></u-icon>
                  <text class="count-text">{{ item.pv }}</text>
                </view>
              </view>
              <view class="live-content">
                <view class="live-title">
                  <text class="title-text u-line-1">{{ item.name }}</text>
                  <view class="buy-tag" v-if="item.vip_level != 0"
                    >购买会员可看</view
                  >
                </view>
                <view class="live-time">{{ item.showDate }}</view>

                <!-- <view class="tearcher-wrap">
                <u-avatar :src="customComplete(item.avatar)" :size="24"></u-avatar>
                <text class="teacher-name">{{ item.teacher_name }}</text>
                <text class="teacher-name">{{ item.school_name }}</text>
              </view> -->
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

    <u-modal
      v-if="showTip"
      :show="true"
      title=" "
      content=""
      :showConfirmButton="false"
    >
      <view class="slot-content-my">
        <view class="tip-text">
          <text class="text">升级会员</text>
          <image
            class="bg-image"
            src="../../../../static/imgs/kaolaStudent/tip-title-bg.png"
          ></image>
        </view>
        <view class="tip-content">当前视频，需要成为会员，才可查看</view>
        <view class="update-btn" @click="goVipPage">升级会员</view>
        <view class="text-button" @click="showTip = false">我再想想</view>
      </view>
    </u-modal>
  </view>
</template>

<script>
import dayjs from 'dayjs'
import { square, teaching } from '../../api'
import tabBar from '../../components/commen/tabBar.vue'
export default {
  name: 'plaza',
  components: { tabBar },
  onLoad() {
    let initbarheight = 45
    let BarHeight = wx.getSystemInfoSync().statusBarHeight || initbarheight
    this.backgroundSize = `background-size: 100vw ${
      282 + BarHeight - initbarheight
    }px;`

    this.getSwiperImage()
    this.getLiveData()
    this.getTodayAppointList()
  },

  onShow() {
    console.log('onShow - 直播详情')
    if (this.showTip) {
      this.showTip = false
    }
  },
  // 添加下拉刷新方法
  onPullDownRefresh() {
    // 重新获取数据
    this.getSwiperImage()
    this.getLiveData()
    this.getTodayAppointList()

    // 如果当前显示的是录播页面，也重新获取录播数据
    if (this.pageType === 2) {
      this.getCallbackData()
    }

    // 数据加载完成后，结束下拉刷新状态
    setTimeout(() => {
      uni.stopPullDownRefresh()
    }, 500)
  },
  data() {
    return {
      scrollTop: 0,
      backgroundSize: '',
      avatarUrl:
        'https://xy-kaola.oss-cn-beijing.aliyuncs.com/merchantid_385164383640140890/389806835466550453/resumes/1757125666/583169428993085165.noRMX8htnQK76a9f4109dd80c4a8210d740a1e773482.png',
      tabList: [
        {
          name: '推荐',
          id: 1
        },
        {
          name: '课程',
          id: 2
        }
      ],
      imageList: [],
      liveList: [],
      todayAppoint: {
        showDate: ''
      },
      showTip: false,
      callbackList: [],
      pageType: 1
    }
  },
  methods: {
    getSwiperImage() {
      square.getSwiperList().then(res => {
        if (res && res.data) {
          this.imageList = res.data.map(item => {
            return this.customComplete(item.image_url)
          })
        }
      })
    },
    goVipPage() {
      this.$xh.push('kaolaStudent', 'pages/my/vip/index')
    },

    goLiveDetail(item) {
      this.$xh.push('kaolaStudent', 'pages/live/liveDetail?id=' + item.id)
    },

    goCallbackDetail(item) {
      const params = { goods_id: item.id }

      square.getCallbackDetail(params).then(res => {
        console.log('录播详情： ', res)
        if (Number(res.data.student_vip_level) < Number(res.data.vip_level)) {
          this.showTip = true
        } else {
          this.$xh.push('kaolaStudent', 'pages/live/callbackPlay?id=' + item.id)
        }
      })
    },
    getTodayAppointList() {
      square
        .getAppointList({
          status: 1,
          date: dayjs(new Date()).format('YYYY-MM-DD')
        })
        .then(res => {
          if (res.data && res.data.list.length) {
            this.todayAppoint = res.data.list[0]
            this.todayAppoint.date = dayjs(this.todayAppoint.start_time).format(
              'YYYY-MM-DD'
            )
            this.todayAppoint.oneTime = dayjs(
              this.todayAppoint.start_time
            ).format('HH:mm')
            this.todayAppoint.twoTime = dayjs(
              this.todayAppoint.end_time
            ).format('HH:mm')
            this.todayAppoint.showDate = `${this.todayAppoint.oneTime} - ${this.todayAppoint.twoTime}`
          }
        })
    },
    customComplete(path) {
      if (path && path.startsWith('http')) {
        return path
      } else if (path) {
        return this.$xh.completepath(path)
      } else {
        return ''
      }
    },
    getLiveData() {
      teaching
        .getLiveList({
          name: ''
        })
        .then(res => {
          this.liveList = res.data?.list.map(v => {
            const dateDay = v.start_time.split(' ')[0]
            const shortStartTime = dayjs(v.start_time).format('HH:mm')
            const shortEndTime = dayjs(v.end_time).format('HH:mm')
            console.log('直播数据', v)
            console.log('shortStartTime', shortStartTime)
            console.log('shortEndTime', shortEndTime)
            return {
              ...v,
              showDate: `${dateDay} ${shortStartTime}-${shortEndTime}`
            }
          })
        })
    },
    getCallbackData() {
      teaching
        .getCallbackList({
          name: ''
        })
        .then(res => {
          if (res && res.data) {
            this.callbackList = res.data.list.map(v => {
              let showDate = ''
              if (v.start_time) {
                const dateDay = v.start_time.split(' ')[0]
                const shortStartTime = dayjs(v.start_time).format('HH:MM')
                const shortEndTime = dayjs(v.end_time).format('HH:MM')
                showDate = `${dateDay} ${shortStartTime}-${shortEndTime}`
              }
              return {
                ...v,
                showDate: showDate
              }
            })
          }
        })
    },
    clickSwiper() {},
    clickTab(item) {
      this.pageType = item.id
      if (item.id == 1) {
        this.getLiveData()
      } else {
        this.getCallbackData()
      }
    },
    goMoreLivePage() {
      this.$xh.push('kaolaStudent', 'pages/live/livePage')
    },
    goMoreCallbackPage() {
      // 跳转更多录播页面
      this.$xh.push('kaolaStudent', 'pages/live/callbackPage')
    },
    goAppointment() {
      uni.navigateTo({
        url: '/modules/kaolaStudent/pages/appointment/appointmentList'
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.plaza-page {
  .back {
    display: flex;
    flex-direction: column;
    height: 100vh;
    border-radius: 0px 0px 0px 0px;

    background-image: url('https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/626f174562773210963083_Group%20272%402x.png');

    background-repeat: no-repeat;
    background-position: 12.92% -7.5%;
  }

  .slot-content-my {
    width: 624rpx;
    height: 440rpx;
    border-radius: 32rpx;
    padding: 64rpx !important;

    .tip-text {
      width: 162rpx;
      height: 64rpx;
      margin: 0 auto 0;
      text-align: center;
      font-weight: 700;
      font-size: 40rpx;
      letter-spacing: 0%;
      color: #222333;
      position: relative;

      .text {
        position: absolute;
        top: 0;
        width: 100%;
        text-align: center;
        height: 32rpx;
        left: 0;
        z-index: 2;
      }

      .bg-image {
        position: absolute;
        z-index: 2;
        bottom: 0;
        left: 0;
        height: 20rpx;
        width: 100%;
      }
    }

    .tip-content {
      font-weight: 400;
      font-size: 28rpx;
      color: #222333db;
      padding-top: 32rpx;
    }

    .update-btn {
      width: 496rpx;
      height: 88rpx;
      line-height: 88rpx;
      border-radius: 100rpx;
      background: #2d2a40;
      color: #fff;
      text-align: center;
      margin-top: 64rpx;
    }

    .text-button {
      font-weight: 400;
      font-size: 28rpx;
      color: #222333db;
      padding-top: 16rpx;
      text-align: center;
      margin: 0 auto;
    }
  }

  .tab-wrap {
    padding-top: 24rpx;
    padding-bottom: 16rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-right: 32rpx;
  }

  .right-row {
    display: flex;
    justify-content: center;
    align-items: center;

    .all-appointment {
      font-size: 28rpx;
      color: #767781;
      font-weight: 400;
      margin-right: 8rpx;
    }
  }

  .swiper-zoon {
    width: 100%;
    height: 386rpx;
    border-radius: 32rpx;
    // background-color: #fff;
  }

  .live-img-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }

  .swiper-class {
    width: 100%;
    height: 386rpx;
  }

  .page-content {
    padding: 32rpx;
  }

  .card {
    border-radius: 20rpx;
    background-color: #fff;
    margin-top: 32rpx;
    padding: 32rpx;

    .title {
      font-family: 'PuHuiTi_75_SemiBold';
      font-weight: 600;
      font-size: 42rpx;
      line-height: 100%;
      letter-spacing: 0%;
      color: #222333;
    }
  }

  .card-top {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 16rpx;
  }

  .info-item {
    color: #222333;
    font-size: 30rpx;
    font-weight: 400;
    padding-top: 16rpx;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    &.no-appointment {
      // 针对无预约的特定类
      text-align: center; // 居中对齐
      color: #767781; // 灰色，柔和
      font-size: 32rpx; // 略微增大字体
      font-weight: 500; // 加粗以突出
      padding: 32rpx 0; // 增加垂直间距，避免卡片太空
      display: flex;
      justify-content: center;
      align-items: center;

      // 可选：添加图标（假设有图标资源）
      &::before {
        content: '';
        display: inline-block;
        width: 40rpx;
        height: 40rpx;
        background: url('https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/no-data-two.png')
          no-repeat center/cover; // 替换为实际图标URL
        margin-right: 16rpx;
      }
    }

    .teacher-name {
      margin-right: 32rpx;
    }
    .teacher-name-short {
      margin-right: 32rpx;
      width: 87rpx;
    }
  }

  .live-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 32rpx;

    .live-item {
      width: 310rpx;
      // width: 170rpx;
      position: relative;
      background-color: #fff;
      border-radius: 32rpx;

      .live-time {
        font-weight: 400;
        font-size: 24rpx;
        line-height: 100%;
        letter-spacing: 0%;
        color: #666;
        padding-top: 16rpx;
      }

      .tearcher-wrap {
        padding-top: 16rpx;
        padding-bottom: 16rpx;
        font-size: 24rpx;
        font-weight: 400;
        color: #222333;
        display: flex;
        justify-content: flex-start;
        align-items: center;

        .teacher-name {
          font-weight: 400;
          font-size: 24rpx;
          font-family: 'PuHuiTi_55_Regular';
          padding: 0 16rpx;
          color: #000;
        }
      }
    }

    .live-tag {
      position: absolute;
      top: 23rpx;
      right: 16rpx;
      border-radius: 100rpx;
      color: #333;
      font-size: 22rpx;
      font-weight: 600;
      z-index: 2;
      padding: 8rpx 16rpx;
      background: linear-gradient(90deg, #f2fb5d 0%, #baed26 100%);
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .grey-bg {
      position: absolute;
      top: 23rpx;
      right: 16rpx;
      border-radius: 100rpx;
      color: #333;
      font-size: 22rpx;
      font-weight: 600;
      z-index: 2;
      padding: 8rpx 16rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #f2f2f2;
    }

    .live-tag-icon {
      width: 18rpx;
      height: 20rpx;
      margin-left: 8rpx;
    }

    .live-count {
      position: absolute;
      z-index: 3;
      bottom: 16rpx;

      right: 16rpx;
      display: flex;
      justify-content: center;
      align-items: center;

      .count-text {
        color: #666;
        font-family: 'SourceHanSansCN-VF';
        font-weight: 500;
        font-style: Medium;
        font-size: 16rpx;
        letter-spacing: 5%;
        margin-left: 8rpx;
        line-height: 32rpx;
      }
    }

    .live-img {
      width: 100%;
      height: 254rpx;
      background-color: #f0f0f0;
      border-radius: 32rpx 32rpx 0 0;
      position: relative;
    }

    .buy-tag {
      width: 120rpx;
      flex-shrink: 0;
      font-weight: 500;
      font-size: 16rpx;
      padding: 6rpx 10rpx;
      border-radius: 6rpx;
      color: #000;
      background-color: #ffd7a4;
    }

    .live-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-text {
        font-family: 'SourceHanSansCN-VF';
        font-weight: 600;
        font-size: 24rpx;
        line-height: 42rpx;
        letter-spacing: 5%;
        color: #000;
        width: 176rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .live-content {
    padding: 16rpx;
  }

  .scroll-y {
    height: 542rpx;
  }

  .no-record-scroll-y {
    height: 630rpx;
  }
}
</style>
