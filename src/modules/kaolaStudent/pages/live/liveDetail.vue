<template>
  <view class="live-detail">
    <u-navbar
      title="推荐详情"
      bgColor="rgba(255,255,255,0)"
      @rightClick="rightClick"
      :autoBack="true"
    >
    </u-navbar>
    <view class="page-wrap">
      <view class="page-top">
        <view class="img-wrap">
          <!-- 修改1: 更改图片模式为widthFix，确保图片完全显示且宽度填满容器 -->
          <image
            class="main-image"
            mode="widthFix"
            :src="getImageSrc()"
            @error="handleImageError"
          ></image>
        </view>
        <!-- 修改2: 将tag标签移到图片容器下方，作为单独一行 -->
        <view
          class="tag-wrapper"
          v-if="vip_level != 0 && (!userInfo || userInfo.vip_level == 0)"
        >
          <view class="tag">购买会员可看</view>
        </view>
        <view class="page-info">
          <view class="title">{{ info.name }}</view>
          <view class="tearcher-wrap">
            <u-avatar :src="avatarUrl" :size="28"></u-avatar>
            <text class="teacher-name">{{ info.teacher_name }}</text>
            <UniversityBadge :schoolName="info.school_name"></UniversityBadge>
          </view>

          <view class="live-content">
            <view>内容说明:</view>
            <view class="content">{{ info.description }}</view>
          </view>
        </view>
      </view>
      <view class="page-bottom">
        <view class="button-wrap">
          <view
            class="appointment-button"
            @click="bookLive"
            v-if="!(replay_url || live_room_id)"
          >
            <text class="button-text">
              {{ status == 1 ? '取消预约' : '预约' }}
            </text>
          </view>

          <view class="special-button" @click="goLiveRoom" v-if="!replay_url">
            <text class="button-text">查看推荐详情</text>
          </view>
          <view class="special-button" @click="goCallback" v-if="replay_url">
            <text class="button-text">查看回放</text>
          </view>
        </view>
      </view>
    </view>

    <u-modal :show="showTip" title=" " content="" :showConfirmButton="false">
      <view class="slot-content">
        <view class="tip-text">
          <text class="text">升级会员</text>
          <image
            class="bg-image"
            src="../../../../static/imgs/kaolaStudent/tip-title-bg.png"
          ></image>
        </view>
        <view class="tip-content">当前视频，需要成为会员，才可查看</view>
        <view class="update-btn" @click="goVipPage">升级会员</view>
        <view class="text-button" @click="showTip = false">我再想想</view>
      </view>
    </u-modal>
  </view>
</template>

<script>
import UniversityBadge from '../../components/UniversityBadge/index.vue'
import { square, teaching } from '../../api/index'
// 在export default对象中添加computed属性
export default {
  name: 'liveDetail',
  components: { UniversityBadge },
  data() {
    return {
      query: null,
      mainImage: '',
      replay_url: '',
      status: 0,
      info: {
        name: '',
        description: '',
        school_name: '',
        teacher_name: '',
        live_url: ''
      },
      teacher_id: 0,
      live_room_id: 0,
      vip_level: 0,
      showTip: false,
      goods_id: 0,
      avatarUrl: '',
      // 添加一个默认图片标志
      usingDefaultImage: false,
      // 添加图片错误处理的timeout变量
      imageErrorTimeout: null,
      imageErrorTimeout: null,
      // 添加重试次数计数器
      imageRetryCount: 0,
      // 设置最大重试次数
      maxImageRetryCount: 2
    }
  },
  // 添加computed属性，用于获取用户信息
  computed: {
    userInfo() {
      return uni.getStorageSync('__xingyun_userinfo2__')
    }
  },
  onLoad(query) {
    console.log('onLoad - 直播详情')
    this.query = query
    this.getDetail()
  },
  onShow() {
    console.log('onShow - 直播详情')
    console.log('this.showTip', this.showTip)
    if (this.showTip) {
      this.showTip = false
      this.getDetail()
    }
  },
  methods: {
    getDetail() {
      const params = {}
      if (this.query.id) {
        params.goods_id = this.query.id
      } else if (this.query.system_id) {
        params.system_id = this.query.system_id
      }

      square
        .getLiveDetail(params)
        .then(res => {
          console.log('直播详情返回', res.data)
          this.goods_id = res.data.id
          console.log('获取到', this.goods_id)

          if (res.data && res.data.material_cover_path) {
            // 1. 先尝试使用完整路径
            let imagePath = res.data.material_cover_path

            // 2. 如果路径不是以http开头，尝试使用completepath方法
            if (
              imagePath &&
              !imagePath.startsWith('http') &&
              this.$xh &&
              this.$xh.completepath
            ) {
              try {
                const processedPath = this.$xh.completepath(imagePath)
                console.log('处理后的图片路径:', processedPath)
                this.mainImage = processedPath
              } catch (error) {
                console.error('处理图片路径失败:', error)
                this.mainImage = imagePath
              }
            } else {
              this.mainImage = imagePath
            }

            console.log('最终使用的图片路径:', this.mainImage)
          } else {
            console.warn('未获取到图片路径')
            this.mainImage = ''
          }

          // 其他数据处理保持不变
          if (res.data.avatar) {
            this.avatarUrl = this.$xh.completepath
              ? this.$xh.completepath(res.data.avatar)
              : res.data.avatar
          }
          this.info.name = res.data.name || ''
          this.info.description = res.data.description || ''
          this.info.school_name = res.data.school_name || ''
          this.info.teacher_name = res.data.teacher_name || ''
          this.replay_url = res.data.replay_url || ''
          this.status = res.data.status || 0
          this.live_room_id = res.data.live_room_id || 0
          this.teacher_id = res.data.teacher_id || 0
          this.vip_level = res.data.vip_level || 0
        })
        .catch(error => {
          console.error('获取直播详情失败:', error)
          // 出错时也显示默认图片
          this.usingDefaultImage = true
        })
    },

    // 修改3: 添加获取图片源的方法
    getImageSrc() {
      return this.mainImage
    },

    // 修改4: 优化图片加载错误处理
    handleImageError(e) {
      console.error('图片加载失败:', e)
      // 添加防抖机制，防止短时间内重复触发
      if (this.imageErrorTimeout) {
        clearTimeout(this.imageErrorTimeout)
      }

      this.imageErrorTimeout = setTimeout(() => {
        // 增加重试逻辑
        if (this.imageRetryCount < this.maxImageRetryCount) {
          console.log(`图片加载失败，第${this.imageRetryCount + 1}次重试...`)
          // 重新设置图片路径，触发重新加载
          this.mainImage = ''
          this.$nextTick(() => {
            // 确保DOM更新后再设置回原图片路径
            this.mainImage = this.getOriginalImagePath()
            this.imageRetryCount++
          })
        } else {
          console.log('已达到最大重试次数')
          // 重置重试计数
          this.imageRetryCount = 0
        }
        // 强制刷新DOM
        this.$forceUpdate()
      }, 500) // 500毫秒防抖
    },
    // 添加获取原始图片路径的方法
    getOriginalImagePath() {
      // 这里应该返回处理前的原始图片路径
      // 实际应用中可能需要在数据处理时保存原始路径
      // 这里简化处理，直接返回当前mainImage
      return this.mainImage
    },

    goLiveRoom() {
      if (
        this.vip_level != 0 &&
        (!this.userInfo || this.userInfo.vip_level == 0)
      ) {
        this.showTip = true
        return
      }

      let student_id = ''
      if (this.userInfo) {
        student_id = this.userInfo.id
      }
      console.log('学生id:', student_id)
      if (this.live_room_id) {
        let goods_id = this.query.id
        if (this.goods_id) {
          goods_id = this.goods_id
        }
        square
          .getLiveUrl({
            live_room_id: this.live_room_id,
            goods_id,
            teacher_id: this.teacher_id
          })
          .then(res => {
            if (res.data.vip_level == 0) {
              square.addPv({
                live_room_id: this.live_room_id
              })
              this.$xh.push(
                'kaolaStudent',
                `pages/live/liveWebview?live_url=${encodeURIComponent(
                  res.data.live_url
                )}`
              )
            } else {
              this.showTip = true
            }
          })
      } else {
        this.$xh.Toast('推荐还未开始')
      }
    },
    goVipPage() {
      this.$xh.push('kaolaStudent', 'pages/my/vip/index')
    },
    goCallback() {
      if (
        this.vip_level !== 0 &&
        (!this.userInfo || this.userInfo.vip_level === 0)
      ) {
        this.showTip = true
        return
      }
      if (this.live_room_id) {
        square
          .getReplayUrl({
            live_room_id: this.live_room_id,
            teacher_id: this.teacher_id,
            goods_id: this.goods_id
          })
          .then(res => {
            console.log('回放地址： ', res)
            if (res.data.vip_level == 0) {
              this.$xh.push(
                'kaolaStudent',
                `pages/live/liveVideo?id=${this.query.id}&system_id=${this.query.system_id}&replay_url=${res.data.replay_url}`
              )
            } else {
              this.showTip = true
            }
          })
      }
    },
    bookLive() {
      const tipText = this.status == 1 ? '取消预约' : '预约'
      teaching
        .appoint({
          status: this.status == 1 ? 0 : 1,
          goods_id: this.query.id
        })
        .then(res => {
          if (res.data.vip_level == 0) {
            this.$xh.Toast(`${tipText}成功!`)
            this.getDetail()
          } else {
            this.showTip = true
          }
        })
    },

    rightClick() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.live-detail {
  width: 100%;
  height: 100vh;
  padding: 40rpx 14rpx;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: space-between;

  background-image: url('https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/626f174562773210963083_Group%20272%402x.png');
  background-repeat: no-repeat;
  background-position: 12.92% -7.5%;

  .page-wrap {
    width: 100%;
    display: flex;
    flex-direction: column;
    height: 100%;
    align-items: stretch;
    justify-content: space-between;
  }

  .custom-nav {
    background-color: transparent;
  }

  .page-top {
    margin-top: 182rpx;
    flex: 1;
  }

  .title {
    font-size: 34rpx;
    padding-top: 40rpx;
    font-weight: 600;
    color: #333;
    font-family: 'PuHuiTi_75_SemiBold';
  }

  .img-wrap {
    width: 100%;
    /* 修改2: 移除固定高度，让容器自适应图片高度 */
    min-height: 386rpx;
    max-height: 500rpx;
    border-radius: 32rpx;
    position: relative;
    background-color: #ffffff; /* 调整背景色与页面协调 */
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .main-image {
    /* 修改3: 设置宽度100%，高度自适应 */
    width: 100%;
    height: auto;
    /* 修改4: 使用contain确保图片完全显示 */
    object-fit: contain;
    border-radius: 32rpx;
  }

  .tearcher-wrap {
    padding-top: 40rpx;
    font-size: 28rpx;
    color: #999;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .teacher-name {
      font-weight: 400;
      font-size: 28rpx;
      font-family: 'PuHuiTi_55_Regular';
      padding: 0 16rpx;
      color: #000;
    }
  }

  .live-content {
    padding-top: 40rpx;
    color: #666;
    font-family: 'PuHuiTi_55_Regular';
    font-weight: 400;
    font-size: 26rpx;
  }

  .content {
    padding-top: 20rpx;
  }

  .page-info {
    padding: 0 20rpx;
  }

  .button-wrap {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 40rpx;
    padding-bottom: 72rpx;
  }

  .page-bottom {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .special-button {
    width: 462rpx;
    height: 84rpx;
    border-radius: 100rpx;
    background-color: #222333;
    display: flex;
    justify-content: center;
    align-items: center;

    .button-text {
      font-family: 'Inter-Regular';
      text-align: center;
      width: 100%;
      height: 84rpx;
      line-height: 84rpx;
      color: #fff;
      text-align: center;
      font-weight: 400;
      letter-spacing: 0;
      font-size: 32rpx;
    }
  }

  .appointment-button {
    width: 462rpx;
    height: 84rpx;
    border-radius: 100rpx;
    padding: 10rpx;
    background-color: #fff;
    border: 1px solid #222333;
    display: flex;
    justify-content: center;
    align-items: center;

    .button-text {
      font-family: 'Inter-Regular';
      text-align: center;
      width: 100%;
      height: 84rpx;
      line-height: 84rpx;
      color: #222333;
      text-align: center;
      font-weight: 400;
      letter-spacing: 0;
      font-size: 32rpx;
    }
  }

  .slot-content {
    width: 624rpx;
    height: 440rpx;
    border-radius: 32rpx;
    padding: 64rpx;

    .tip-text {
      width: 162rpx;
      height: 64rpx;
      margin: 0 auto 0;
      text-align: center;
      font-weight: 700;
      font-size: 40rpx;
      letter-spacing: 0%;
      color: #222333;
      position: relative;

      .text {
        position: absolute;
        top: 0;
        width: 100%;
        text-align: center;
        height: 32rpx;
        left: 0;
        z-index: 2;
      }

      .bg-image {
        position: absolute;
        z-index: 2;
        bottom: 0;
        left: 0;
        height: 20rpx;
        width: 100%;
      }
    }

    .tip-content {
      font-weight: 400;
      font-size: 28rpx;
      color: #222333db;
      padding-top: 32rpx;
    }

    .update-btn {
      width: 496rpx;
      height: 88rpx;
      line-height: 88rpx;
      border-radius: 100rpx;
      background: #2d2a40;
      color: #fff;
      text-align: center;
      margin-top: 64rpx;
    }

    .text-button {
      font-weight: 400;
      font-size: 28rpx;
      color: #222333db;
      padding-top: 16rpx;
      text-align: center;
      margin: 0 auto;
    }
  }
}

.tag-wrapper {
  width: 100%;
  margin-top: 24rpx;
  display: flex;
  justify-content: flex-start;
  padding-left: 20rpx;
  padding-right: 0;
}

.tag {
  /* 将背景色改为指定的 #ffd7a4 */
  background-color: #ffd7a4;
  /* 将文字颜色改为黑色 */
  color: #000000;
  font-size: 24rpx;
  font-weight: 500;
  padding: 8rpx 24rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 215, 164, 0.3);
  text-align: center;
  letter-spacing: 1rpx;
  border: none;
}
</style>
