<template>
  <div class="month">
    <calendar
      ref="calendar"
      v-model="date"
      :initDate="initDate"
      @change="my"
      :data="list.map(e => e.date)"
    ></calendar>

    <view class="month-body">
      <view class="head">
        <view class="title">
          <view
            >今天共
            <text style="margin: 0 4px">{{ showList.length }}</text> 节课</view
          >
          <image
            src="http://xy-kaola.oss-cn-beijing.aliyuncs.com/public/61fc174705682596474209_Rectangle%20281%402x.png"
          ></image>
        </view>
        <view class="status">
          <text>已完成</text><text>{{ status3NUm }}/{{ showList.length }}</text>
        </view>
      </view>
      <view class="list">
        <view class="item" v-for="item of showList">
          <view class="h">
            <view class="label">{{ item.minute }}分钟</view>
            <view class="text">上课时间</view>

            <!-- <view>距离上课还有 8分32秒</view> -->
            <countdown
              v-if="item.timeStatus == 1"
              :end-time="item.countdown"
              @finish="finish(item)"
            />
            <view class="status" v-else>{{ item.status_name }}</view>
          </view>
          <view class="b">
            <view class="start">{{ item.start_time }}</view>

            <view class="r" v-if="item.status != 4">
              <view
                v-if="item.timeStatus == '1'"
                class="but"
                @click="tap(item, 1)"
                >课前提问</view
              >
              <view
                class="but but1"
                v-if="!item.replay_url"
                @click="tap(item, 2)"
              >
                进教室</view
              >
              <view class="but" v-if="item.replay_url" @click="tap(item, 3)">
                看回放
              </view>
            </view>
          </view>

          <view class="info">
            <image
              class="avater"
              :src="
                $xh.completepath(
                  item.teacher_avatar ||
                    'public/5ef917440086508568967_laoshitouxiang.png'
                )
              "
            ></image>
            <view class="teacher_name">{{ item.teacher_name }}</view>
            <view class="fen">｜</view>
            <view class="name">{{ item.grade_name }}{{ item.subject_name}}</view>
            <view class="fen">｜</view>
            <view class="name">{{ item.start_time }}-{{ item.end_time }}</view>
          </view>
        </view>
      </view>
    </view>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import calendar from './calendar/index.vue'
import { appoint } from '../../../api/index'
import date from './date.vue'
import countdown from './countdown.vue'
export default {
  components: {
    calendar,
    date,
    countdown
  },
  props: {
    pageShow: {
      type: Boolean,
      default: true
    },
    currentDate: {
      type: String,
      default: ''
    },
    value: {
      type: String,
      defaul: ''
    },
    selectDate: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      hasLoadedInitialData: false, // 新增状态，防止重复加载
      status_name: ['', '预约中', '已预约', '已完成', '已取消', '进行中'],
      date: dayjs().format('YYYY-MM-DD'),
      initDate: '',
      list: [],
      search: {
        start: '',
        end: ''
      }
    }
  },

  computed: {
    token() {
      return this.$store.state.kaolaStudent.token;
    },
    showList() {
      return this.list.filter(e => e.date == this.date)
    },
    status3NUm() {
      return this.list.filter(e => e.date == this.date && e.status == '3')
        .length
    }
  },
  created() {
    this.date = this.selectDate;
    this.initDate = this.currentDate;
    // 如果 pageShow 为 true 且 token 存在，则在 created 时加载一次
    if (this.pageShow && this.token && !this.hasLoadedInitialData) {
      this.my(this.search);
      this.hasLoadedInitialData = true;
    }
  },
  watch: {
    pageShow(val) {
      // 当 pageShow 变为 true 且 token 存在，并且尚未加载过初始数据时，加载数据
      if (val && this.token && !this.hasLoadedInitialData) {
        this.my(this.search);
        this.hasLoadedInitialData = true;
      } else if (val && this.token && this.hasLoadedInitialData) {
        // 如果已经加载过，但页面再次显示，可以考虑是否需要刷新数据，或者依赖下拉刷新
        // this.my(this.search); // 如果需要每次显示都刷新，取消此行注释
      }
    },
    token(newToken) {
      // 当 token 从无到有时，且 pageShow 为 true，并且尚未加载过初始数据时，加载数据
      if (newToken && this.pageShow && !this.hasLoadedInitialData) {
        this.my(this.search);
        this.hasLoadedInitialData = true;
      }
    },
    date() {
      this.$emit('update:selectDate', this.date)
    }
  },
  methods: {
    tap(item, type) {
      if (type == '1') {
        this.$xh.push(
          'kaolaStudent',
          `pages/companions/preview?id=${item.id}&teacher_id=${item.teacher_id}`
        )
      }
      if (type == '2') {
        let num =
          dayjs(`${item.date} ${item.start_time}`).toDate().getTime() -
          new Date().getTime()
        let isCancel = 2
        if (num > 60 * 120 * 1000) {
          isCancel = 1
        }
        if (item.status == 4) {
          isCancel = '0'
        }
        this.$xh.push(
          'kaolaStudent',
          `pages/companions/detai?id=${item.id}&teacher_id=${
            item.teacher_id
          }&live_url=${encodeURIComponent(item.live_url)}&isCancel=${isCancel}`
        )
      }
      if (type == '3') {
        if (item.replay_url) {
          wx.previewMedia({
            sources: [
              {
                url: item.replay_url,
                type: 'video'
              }
            ],
            fail(err) {
              console.log(err)
            }
          })
        } else {
          this.$xh.Toast('暂未生成回放地址')
        }
      }
    },
    finish(item) {
      item.timeStatus = '2'
      item.status_name = '进行中'
    },
    my(date) {
      this.$emit('update:currentDate', dayjs(date.start).format('YYYY-MM-DD'))
      this.search = date
      appoint.student
        .my({
          start: dayjs(date.start).format('YYYY-MM-DD'),
          end: dayjs(date.end).format('YYYY-MM-DD')
        })
        .then(res => {
          this.list = res.data || []
          // console.log("this list++++++++++",this.list)
          this.list.forEach(e => {
            e.status_name = this.status_name[e.status]
            e.countdown = `${e.date} ${e.start_time}`
            let timeStatus = 1
            let current = new Date().getTime()
            let start = dayjs(`${e.date} ${e.start_time}`).toDate().getTime()
            let end = dayjs(`${e.date} ${e.end_time}`).toDate().getTime()
            if (current < start) {
              timeStatus = 1 //未开始
            } else if (current > end) {
              timeStatus = 3 //已结束
            } else {
              timeStatus = 2 //进行中
            }
            if (e.status == 4) {
              timeStatus = 3
            }
            e.timeStatus = timeStatus
            e.minute = Math.floor((end - start) / 1000 / 60)
          })
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.month {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  position: relative;
}
.month-body {
  flex: 1;
  height: 200px;
  overflow-y: auto;
  padding: 0 12px;
}
.head {
  padding: 20px 0;
  justify-content: space-between;
  display: flex;
  align-items: center;
  .title {
    position: relative;
    view {
      position: relative;
      z-index: 1;
      font-weight: 500;
      font-size: 16px;
      color: #222333;
    }
    image {
      position: absolute;
      top: -4px;
      left: 36px;
      width: 55.79px;
      height: 26.78px;
    }
  }
  .status {
    text:nth-child(1) {
      font-weight: 400;
      font-size: 14px;
      color: rgba(34, 35, 51, 0.4);
      margin-right: 8px;
    }

    text:nth-child(2) {
      font-weight: 400;
      font-size: 14px;
      color: #222333;
    }
  }
}
.list {
  padding-bottom: 32px;
  .item {
    background: #ffffff;
    padding: 12px;
    border-radius: 12px 12px 12px 12px;
    margin-bottom: 8px;
    .h {
      display: flex;
      align-items: center;
      .status {
        font-weight: 400;
        font-size: 12px;
        color: rgba(34, 35, 51, 0.6);
      }
      .label {
        flex-shrink: 0;
        width: 46px;
        text-align: center;
        height: 24px;
        line-height: 24px;
        background: #f3f5f8;
        border-radius: 6px 6px 6px 6px;
        font-weight: 400;
        font-size: 12px;
        color: rgba(34, 35, 51, 0.6);
        margin-right: 12px;
      }
      .text {
        font-weight: 400;
        font-size: 14px;
        color: #222333;
        margin-right: auto;
      }
    }
    .b {
      display: flex;
      justify-content: space-between;
      padding: 16px 0;
      .start {
        font-weight: 800;
        font-size: 28px;
        color: #222333;
      }
      .r {
        display: flex;
        align-items: center;
        .but {
          // width: 52px;
          padding: 0 12px;
          height: 32px;
          line-height: 32px;
          background: #eff1f4;
          border-radius: 10px 10px 10px 10px;

          font-weight: 500;
          font-size: 14px;
          color: #222333;
          margin-left: 8px;
        }
        .but1 {
          background: #dbf46b;
        }
      }
    }
    .info {
      display: flex;
      align-items: center;
      .avater {
        width: 22px;
        height: 22px;
        border-radius: 50%;
        background: #999faa;
      }
      .name,
      .teacher_name {
        margin: 0 8px;
        font-weight: 400;
        font-size: 12px;
        color: rgba(34, 35, 51, 0.6);
      }
      .teacher_name {
        max-width: 60px;
      }
      .fen {
        width: 12px;
        text-align: center;
        font-weight: 400;
        font-size: 12px;
        color: rgba(34, 35, 51, 0.35);
      }
    }
    .but {
    }
    .but {
      display: flex;
    }
  }
}
</style>
