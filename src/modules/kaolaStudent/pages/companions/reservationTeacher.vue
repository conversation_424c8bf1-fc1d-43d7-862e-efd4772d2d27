<template>
  <view>
    <view>
      <view class="teacher-box">
        <view class="teacher">
          <image
            class="avatar"
            :src="
              $xh.completepath(
                info.teacher_avatar ||
                  'public/5ef917440086508568967_laoshitouxiang.png'
              )
            "
          ></image>
          <view class="info">
            <view class="name">{{ info.teacher_name }}</view>
            <view class="subjects"
              >{{ info.stage_name }}<text style="margin: 3px">·</text
              >{{ info.subject_name }}</view
            >
          </view>
        </view>

        <view
          class="but"
          @click="
            $xh.push(
              'kaolaStudent',
              'pages/companions/teacherIntroduction?teacher_id=' +
                query.teacher_id
            )
          "
        >
          <text>老师简介</text>
          <image
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/80c8174684229459271174_Frame%402x%20(1).png"
          ></image>
        </view>
      </view>
      <appointmentDate
        v-model="date"
        v-if="query.teacher_id"
        :teacher_id="query.teacher_id"
        @select="getWorktimeList"
        @change="detail"
        :data="info.list"
      ></appointmentDate>
    </view>
    <view>
      <view class="worktimeList" v-for="box of boxList">
        <view class="box-head">
          <image
            class="backImage"
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/42b7174684369821791050_Rectangle%20281%402x.png"
          ></image>
          <text class="md">{{ box.dateName }}</text
          ><text class="yy">/{{ box.yearName }}</text
          ><text class="ww">{{ box.weekdayName }}</text>
        </view>
        <view class="box" v-if="box.timeSlot.length">
          <view class="ban" v-for="ban of box.timeSlot">
            <view class="t"> {{ ban.name }} </view>
            <view class="list">
              <view
                class="ee"
                :class="{
                  select: isSelect(item)
                }"
                @click="selectTap(item)"
                v-for="item of ban.list"
                >{{ item.start }}-{{ item.end }}</view
              >
            </view>
          </view>
          <view class="item" v-for="item of worktimeList">
            <view>{{ item.start }}-{{ item.end }}</view>
            <view @click="selectTap(item)">
              {{ statusName(item) }}
            </view>
          </view>
        </view>
      </view>
      <view style="height: 160px"></view>
    </view>

    <view class="bottom">
      <view class="list" v-if="selectList.length">
        <view class="list-body">
          <view class="item" v-for="(item, index) of selectList">
            <image
              class="clear"
              src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/4c97174684523675056570_Frame%402x%20(2).png"
              @click="del(index)"
            ></image>
            <view class="date">{{ item.dateName }} {{ item.weekdayName }}</view>
            <view class="time">{{ item.start }}-{{ item.end }}</view>
          </view>
        </view>
      </view>

      <view class="but-group" @click="verify">
        <viev class="text"
          >已选
          <text style="color: #222333; margin: 0 4px">{{
            selectList.length
          }}</text>
          课时
        </viev>
        <viev class="but">预约</viev>
      </view>
    </view>

    <view v-if="show" class="multiple-choice">
      <view class="multiple-choice-body">
        <view class="title">请选择</view>
        <view class="body">
          <view class="tree" v-for="item of info.stage">
            <view class="name">{{ item.name }}</view>
            <view class="subs">
              <view
                class="but"
                :class="{
                  select: form.grade_id == ee.id
                }"
                v-for="ee of item.subs"
                @click="
                  () => {
                    form.stage_id = ee.pid
                    form.grade_id = ee.id
                  }
                "
              >
                {{ ee.name }}
              </view>
            </view>
          </view>

          <view class="tree">
            <view class="name">科目</view>
            <view class="subs">
              <view
                class="but"
                :class="{
                  select: form.subject_id == ee.id
                }"
                v-for="ee of info.subject"
                @click="form.subject_id = ee.id"
              >
                {{ ee.name }}
              </view>
            </view>
          </view>
        </view>
        <view class="group-but">
          <view class="but2" @click="show = false">取消</view>
          <view @click="submit()">提交</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { appoint } from '../../api'
import dayjs from 'dayjs'
import appointmentDate from '../../components/companions/appointmentDate.vue'
export default {
  components: {
    appointmentDate
  },
  data() {
    return {
      show: false,
      selectList: [],
      list: [],
      worktimeList: {},
      date: '',
      query: {
        teacher_id: ''
      },
      info: {
        list: []
      },
      boxList: [],
      teacherInfo: {},
      tree: {},
      form: {
        subject_id: '',
        stage_id: '',
        grade_id: ''
      }
    }
  },
  onLoad(query) {
    this.query = query
    this.getWorktimeList(dayjs().format('YYYY-MM-DD'))
    this.detail({
      start: dayjs().format('YYYY-MM-DD'),
      end: dayjs().add(6, 'day').format('YYYY-MM-DD')
    })
  },
  methods: {
    selectTap(item) {
      if (item.status == '3') {
        this.$xh.Toast('已过期！')
        return
      }
      if (item.status == '2') {
        this.$xh.Toast('已约满！')
        return
      }
      let d = this.selectList.find(ee => ee.id == item.id)
      if (!d) {
        this.selectList.push(item)
      } else {
        let index = this.selectList.findIndex(ee => ee.id == item.id)
        this.del(index)
      }
    },
    del(index) {
      this.selectList.splice(index, 1)
    },
    // 1:正常 2:已预约 3:已过期
    statusName(item) {
      if (item.status == '3') {
        return '已过期'
      }
      if (item.status == '2') {
        return '已约满'
      }
      if (this.isSelect(item)) {
        return '已预约'
      } else {
        return '预约'
      }
    },
    isSelect(item) {
      let d = this.selectList.find(ee => ee.id == item.id)
      return !!d
    },
    detail(data) {
      appoint.teacher
        .detail({
          teacher_id: this.query.teacher_id,
          ...data
        })
        .then(res => {
          if (res.data.list) {
            res.data.list = res.data.list
              .filter(e => e.status == 3)
              .map(e => e.date)
          }

          this.info = res.data

          let subject = []
          let stage_name = []

          let stage = []
          if (this.info.subjects?.length) {
            this.info.subjects.forEach(ee => {
              if (!subject.find(e => e.id == ee.subject_id)) {
                subject.push({
                  id: ee.subject_id,
                  name: ee.subject_name
                })
              }
              if (!stage_name.find(s => s == ee.stage_name)) {
                stage_name.push(ee.stage_name)
              }
              let dd = stage.find(e => e.id == ee.stage_id)
              if (!dd) {
                stage.push({
                  id: ee.stage_id,
                  name: ee.stage_name,
                  subs: [
                    {
                      id: ee.grade_id,
                      pid: ee.stage_id,
                      name: ee.grade_name
                    }
                  ]
                })
              } else {
                if (!dd.subs.find(e => e.id == ee.grade_id)) {
                  dd.subs.push({
                    id: ee.grade_id,
                    name: ee.grade_name,
                    pid: ee.stage_id
                  })
                }
              }
            })

            let dd = this.info.subjects[0]
            this.form = {
              subject_id: dd.subject_id,
              stage_id: dd.stage_id,
              grade_id: dd.grade_id
            }
          }
          this.info.subject_name = subject.map(e => e.name).join('/')
          this.info.subject = subject
          this.info.stage_name = stage_name.join('/')
          this.info.stage = stage
        })
    },
    getWorktimeList(date) {
      appoint.teacher
        .worktimeList({
          teacher_id: this.query.teacher_id,
          date
        })
        .then(res => {
          if (res.data.list == null) {
            res.data.list = []
          }
          this.boxList = [
            {
              dateName: dayjs(date).format('MM月DD日'),
              yearName: dayjs(date).format('YYYY'),
              weekdayName: this.$xh.weekdayName(date),
              timeSlot: this.timeSlot(
                res.data.list.filter(e => e.status == '1')
              )
            }
          ]
        })
    },

    timeSlot(list) {
      let morning = []
      let afternoon = []
      let evening = []
      for (let item of list) {
        item.weekdayName = this.$xh.weekdayName(item.date)
        item.dateName = dayjs(item.date).format('MM/DD')
        let n = Number(item.start.split(':')[0])
        if (n < 12) {
          morning.push(item)
        } else if (n < 18) {
          afternoon.push(item)
        } else {
          evening.push(item)
        }
      }
      let arr = []
      if (morning.length) {
        arr.push({
          name: '上午',
          list: morning
        })
      }
      if (afternoon.length) {
        arr.push({
          name: '下午',
          list: afternoon
        })
      }
      if (evening.length) {
        arr.push({
          name: '晚上',
          list: evening
        })
      }
      return arr
    },
    verify() {
      if (this.selectList.length == 0) {
        this.$xh.Toast('请选择预约时间!')
        return
      }
      this.show = true
    },
    submit() {
      if (this.tap) {
        return
      }
      console.log('tap')
      setTimeout(() => {
        this.tap = false
      }, 3000)
      this.tap = true

      if (this.selectList.length == 0) {
        this.$xh.Toast('请选择预约时间!')
        return
      }
      const { subject_id, stage_id, grade_id } = this.form
      this.show = false
      appoint.teacher
        .post({
          worktime_id: this.selectList.map(e => e.id).join(','),
          teacher_id: this.query.teacher_id,
          subject_id,
          stage_id,
          grade_id
        })
        .then(res => {
          let data = res.data.OrderResult
          this.$xh.Toast('预约成功!')
          this.$xh.redirect(
            'kaolaStudent',
            `pages/my/reservationOrder/detail?order_id=${data.order_id}&flow_id=${data.flow_id}&source=reservation`
          )
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.multiple-choice {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 10;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  // align-items: center;
  // justify-content: end;
  .multiple-choice-body {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    background: #ffffff;
    overflow: hidden;
    border-radius: 16px 16px 0 0;
    .title {
      padding: 16px;
      font-weight: bold;
      font-size: 20px;
      color: #222333;
      line-height: 20px;
    }
    .body {
      max-height: 80vh;
      overflow-y: auto;
    }

    .tree {
      padding: 0 4px 0 16px;
      .name {
        font-weight: 400;
        font-size: 16px;
        color: #222333;
        padding: 12px 4px;
      }
      .subs {
        display: flex;
        flex-wrap: wrap;
        .but {
          max-width: calc((100% - 36px) / 3);
          width: 106px;
          text-align: center;
          height: 44px;
          line-height: 44px;
          background: #ffffff;
          border-radius: 22px;
          font-weight: 400;
          font-size: 14px;
          color: #222333;
          margin-bottom: 12px;
          margin-right: 12px;
        }
        .select {
          background: #222333;
          color: #fff;
        }
      }
    }

    .group-but {
      // position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      padding-bottom: 40px;
      padding-top: 8px;
      padding-left: 30px;
      padding-right: 30px;
      display: flex;
      justify-content: center;
      .but2 {
        color: #222333;
        background: #e6e8ec;
        width: 151px;
        margin-right: 13px;
      }
      view {
        width: 151px;
        // width: 231px;
        height: 44px;
        line-height: 44px;
        background: #222333;
        border-radius: 33px 33px 33px 33px;
        font-weight: bold;
        font-size: 14px;
        color: #ffffff;
        text-align: center;
      }
    }
  }
}

.bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 1;
  width: 100vw;
  background-color: #fff;

  // height: 160px;
  .list {
    margin: 10px 12px 0 12px;
    overflow-x: auto;
    .list-body {
      display: flex;
      .item {
        flex-shrink: 0;
        margin-right: 6px;
        width: 108px;
        // height: 62px;
        padding: 10px 0 12px 10px;
        background: #f7f9fb;
        border-radius: 8px 8px 8px 8px;
        border: 1px solid #d2ee56;
        position: relative;
        .clear {
          position: absolute;
          top: 0;
          right: 0;
          width: 16px;
          height: 16px;
          border-left: 20px solid rgba($color: #000000, $alpha: 0);
          border-bottom: 20px solid rgba($color: #000000, $alpha: 0);
        }
        .date {
          font-weight: 400;
          font-size: 11px;
          color: rgba(34, 35, 51, 0.6);
          margin-bottom: 8px;
        }
        .time {
          font-weight: 400;
          font-size: 14px;
          color: #222333;
          line-height: 18px;
        }
      }
    }
  }
  .but-group {
    margin: 0 12px;
    padding-top: 12px;
    padding-bottom: 32px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    z-index: 1;
    .text {
      font-weight: 400;
      font-size: 14px;
      color: rgba(34, 35, 51, 0.85);
      line-height: 20px;
    }
    .but {
      width: 239px;
      height: 44px;
      background: #ff6111;
      border-radius: 25px 25px 25px 25px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      font-size: 14px;
      color: #ffffff;
    }
  }
}
.teacher-box {
  padding: 0 12px 14px 12px;
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  .teacher {
    display: flex;
    .avatar {
      width: 40px;
      height: 40px;
      background: #999faa;
      border-radius: 50%;
      margin-right: 12px;
    }
    .info {
      .name {
        font-weight: 500;
        font-size: 16px;
        color: #222333;
        line-height: 20px;
        margin-bottom: 4px;
      }
      .subjects {
        font-weight: 400;
        font-size: 13px;
        color: rgba(34, 35, 51, 0.65);
      }
    }
  }
  .but {
    width: 84px;
    height: 32px;
    background: #eff1f4;
    border-radius: 23px 23px 23px 23px;
    display: flex;
    align-items: center;
    justify-content: center;
    text {
      font-weight: 500;
      font-size: 12px;
      color: #222333;
    }
    image {
      width: 16px;
      height: 16px;
    }
  }
}

.tabs {
  display: flex;
  height: 44px;
  justify-content: space-between;
  align-items: center;
  .item {
    flex: 1;
  }
  .current {
    background-color: red;
  }
}
.worktimeList {
  padding: 0 12px;
  .ban {
    padding-bottom: 4px;
    .t {
      font-weight: 400;
      font-size: 14px;
      color: rgba(34, 35, 51, 0.85);
      line-height: 20px;
      margin-bottom: 8px;
    }
    .list {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      .ee {
        margin-right: 8px;
        margin-bottom: 8px;
        width: calc((100% - 36px) / 3);
        max-height: 101px;
        text-align: center;
        height: 32px;
        line-height: 32px;
        background: #f7f9fb;
        border-radius: 8px 8px 8px 8px;
        font-weight: 400;
        font-size: 14px;
        color: #222333;
      }
      .select {
        color: #ffffff;
        background: #222333;
      }
    }
  }
  .item {
    margin: 0 12px;
    padding: 12px;
    display: flex;
    justify-content: space-between;
  }
  .box {
    margin-bottom: 4px;
    padding: 16px 0px 4px 16px;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
  }
  .box-head {
    display: flex;
    position: relative;
    // height: 44px;
    padding: 12px 0;
    align-items: flex-end;
    .backImage {
      position: absolute;
      left: 0;
      // top: 0;
      width: 55.79px;
      height: 26.78px;
      z-index: -1;
    }
    .md {
      font-weight: 500;
      font-size: 16px;
      color: #222333;
    }
    .yy {
      font-weight: 400;
      font-size: 12px;
      color: rgba(34, 35, 51, 0.65);
    }
    .ww {
      margin-left: 9px;
      font-weight: 400;
      font-size: 12px;
      color: rgba(34, 35, 51, 0.65);
    }
  }
}
</style>
