<template>
  <view class="bottom">
    <view class="mask" v-if="!token" @click="goToLogin()"></view>
    <view class="image-but">
      <business></business>
      <view class="but" @click="push()">
        <image src="/static/imgs/kaolaStudent/chongzhi.svg"></image>
      </view>
    </view>
    <informationInput></informationInput>
  </view>
</template>

<script>
import informationInput from './information-input.vue'
import business from './business.vue'
import { goToLogin } from '../../../utils/index'
export default {
  components: {
    informationInput,
    business
  },
  data() {
    return {}
  },
  computed: {
    token() {
      return this.$store.state.kaolaStudent.token
    }
  },
  methods: {
    push() {
      this.$xh.push('kaolaStudent', 'pages/home/<USER>')
    },
    goToLogin
  }
}
</script>

<style lang="scss" scoped>
.bottom {
  position: fixed;
  left: 0;
  width: 375px;
  bottom: 0;
  z-index: 10;
  padding: 0 12px 24px 12px;
  .mask {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 9999;
  }

  .image-but {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    height: 36px;
    .but {
      image {
        width: 28px;
        height: 28px;
      }
    }
  }
}
</style>
