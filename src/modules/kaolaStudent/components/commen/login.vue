<template>
  <view class="login" @click.stop="() => {}">
    <!-- 授权登录 -->
    <button
      v-if="(!upToDate || !isLogin) && enableWechatAuth"
      open-type="getPhoneNumber"
      class="button"
      @click="setLogin"
      @getphonenumber="getphonenumber"
    ></button>
    <view class="slot" @click="success">
      <slot />
    </view>
  </view>
</template>

<script>
import store from '../../../../store/index.js'
import { config, student } from '../../api/index.js'
export default {
  props: {
    upToDate: {
      type: Boolean,
      default: true
    },
    enableWechatAuth: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {}
  },
  computed: {
    isLogin: {
      get() {
        return !!this.$store.state.kaolaStudent.token
      },
      set(newValue) {
        return newValue
      }
    }
  },
  methods: {
    loginDialog() {
      this.showLogin = !this.showLogin
    },
    getphonenumber(e) {
      // 默认登录
      let code = e.detail.code

      if (!code) {
        this.$xh.Toast('请您授权手机号才可以操作哦！')
        return
      }
      this.$store
        .dispatch('kaolaStudent/LOGIN', code)
        .then(() => {
          student.detailv2().then(res => {
            if (res.data.stage_id != '0') {
              this.$emit('success')
            } else {
              this.$xh.redirect('kaolaStudent', 'pages/register/index')
            }
          })
        })
        .catch(err => {
          this.$emit('fail')
        })
    },
    success() {
      this.$emit('success')
    },
    setLogin() {
      // #ifdef H5
      // alert('登陆舰')
      this.showLogin = true
      // #endif
    }
  }
}
</script>

<style lang="scss" scoped>
.login {
  position: relative;
  display: inline-block;

  .button {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    z-index: 100;
    background: transparent;
    border: none;
  }

  .slot {
    position: relative;
    z-index: 10;
  }
}
</style>
