<template>
  <div>
    <view class="teacher-box">
      <image class="avatar"></image>
      <view class="teacher-info">
        <view class="name">
          <view>teacher_name</view>
          <image
            src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/8cea17455524882824282_Group%20190%402x.png"
          ></image>
        </view>
        <view class="data">
          <view class="xing">
            <image
              src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/38a1174555253755312367_Star%2011%402x.png"
            ></image>
            <view>4.9</view>
          </view>
          <view class="fen">｜</view>
          <view class="num">
            <view>答疑</view>
            <view>1022次</view>
          </view>
        </view>
      </view>
      <image
        class="xueixao"
        src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/36e2174555257039919651_Group%20261%402x.png"
      ></image>
    </view>

    <view class="order-info">
      <view class="text-item">
        <view class="label">
          <view> 基础费 </view> <view class="tips">默认时长15分钟</view></view
        >
        <view class="value">¥50.00</view>
      </view>
      <view class="text-item">
        <view class="label">
          <view> 延时费 (10分钟) </view>
          <view class="tips">每延长5分钟加收20元延时费用</view>
        </view>
        <view class="value">¥40.00</view>
      </view>
      <view class="text-item">
        <view class="label"> <view> 优惠券 </view></view>
        <view class="value empty">无可用</view>
      </view>
      <view class="text-item">
        <view class="label"> <view> 总计 </view> </view>
        <view class="value">¥90.00</view>
      </view>
    </view>
    <view>
      <!-- {{ info }} -->
    </view>
    <view class="pay" v-if="query.flow_id">
      <view class="price">
        <view class="actualPayment"
          ><text>总计</text><text style="margin: 0 2px">¥</text
          ><text>90.00</text></view
        >
        <view class="discount">已优惠0.00元</view>
      </view>
      <view class="but" @click="wechatapplet()">支付</view>
    </view>
  </div>
</template>

<script>
import { appoint, account } from '../../api'
import { app_id } from '../../config'
export default {
  components: {},
  data() {
    return {
      info: {
        list: []
      },
      query: {}
    }
  },
  onLoad(query) {
    this.query = query
    this.detail(query.id)
  },
  methods: {
    detail(id) {
      appoint.order.detail({ order_id: id }).then(res => {
        this.info = res.data
        appoint.teacher
          .applyJoinDetail({
            // employee_id: '561855922658350517'
            employee_id: res.data.teacher_id
          })
          .then(res => {
            this.info = res.data
          })
      })
    },
    wechatapplet() {
      account
        .payParams({
          flow_id: this.query.flow_id,
          order_id: this.query.id,
          goods_ids: this.query.goods_id
        })
        .then(res => {
          const payParams = {
            appId: app_id,
            timeStamp: res.data.time_stamp,
            nonceStr: res.data.nonce_str,
            signType: res.data.sign_type,
            paySign: res.data.pay_sign,
            package: res.data.package,
            finance_body_id: res.data.finance_body_id
          }

          this.$xh.pay(
            payParams,
            () => {
              this.$xh.Toast('支付成功！')
              this.$xh.back()
            },
            () => {
              this.$xh.Toast('支付失败！')
            }
          )
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.pay {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  background-color: #fff;
  padding: 12px 16px 32px 16px;
  display: flex;
  justify-content: space-between;
  .price {
    .actualPayment {
      display: flex;
      align-items: baseline;
      text:nth-child(1) {
        font-weight: 400;
        font-size: 14px;
        color: #222333;
      }
      text:nth-child(2) {
        font-weight: 500;
        font-size: 14px;
        color: #ff6111;
      }
      text:nth-child(3) {
        font-weight: 500;
        font-size: 20px;
        color: #ff6111;
      }
    }
    .discount {
      font-weight: 400;
      font-size: 12px;
      color: rgba(34, 35, 51, 0.65);
      line-height: 20px;
    }
  }
  .but {
    width: 188px;
    text-align: center;
    height: 44px;
    line-height: 44px;
    background: #ff6111;
    border-radius: 33px 33px 33px 33px;
    font-weight: bold;
    font-size: 14px;
    color: #ffffff;
  }
}
.order-info {
  padding: 6px 16px;
  margin: 0 12px;
  background: #ffffff;
  border-radius: 12px 12px 12px 12px;
  .text-item {
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .label {
      view {
        font-weight: 400;
        font-size: 15px;
        color: #222333;
        line-height: 20px;
      }
      .tips {
        margin-top: 4px;
        font-weight: 400;
        font-size: 12px;
        color: rgba(34, 35, 51, 0.35);
        line-height: 14px;
      }
    }
    .value {
      font-weight: 400;
      font-size: 15px;
      color: #222333;
      line-height: 20px;
    }
    .empty {
      color: rgba(34, 35, 51, 0.35);
    }
  }
}

.teacher-box {
  margin: 0 12px 8px 12px;
  padding: 15px 16px;
  background: #ffffff;
  border-radius: 12px 12px 12px 12px;
  display: flex;
  align-items: center;
  .avatar {
    width: 38px;
    height: 38px;
    border-radius: 50%;
    margin-right: 10px;
    flex-shrink: 0;
  }
  .xueixao {
    margin-left: auto;
    width: 68px;
    height: 24px;
  }
  .teacher-info {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .name {
      display: flex;
      align-items: center;
      view {
        font-weight: 500;
        font-size: 16px;
        color: #222333;
        line-height: 20px;
        margin-right: 4px;
      }
      image {
        width: 79px;
        height: 20px;
      }
    }
    .data {
      margin-top: 8px;
      display: flex;
      align-items: center;
      .xing {
        display: flex;
        align-items: center;
        image {
          width: 16.17px;
          height: 15px;
        }
        view {
          font-weight: 500;
          font-size: 15px;
          color: #fb8105;
          line-height: 20px;
        }
      }
      .fen {
        margin: 0 8px;
        font-size: 13px;
        color: rgba(34, 35, 51, 0.35);
        line-height: 15px;
      }
      .num {
        display: flex;
        align-items: center;
        view {
          font-weight: 400;
          font-size: 13px;
          color: rgba(34, 35, 51, 0.6);
          line-height: 16px;
        }
        view:nth-child(1) {
          color: #222333;
        }
      }
    }
  }
}
</style>
