<template>
  <scroll-view
    scroll-y
    class="chatting"
    :scrollTop="scrollTop"
    :scroll-with-animation="scrollWithAnimation"
  >
    <view class="chatting-content" :style="padding">
      <view class="one-text">
        <view></view>
        <view
          >课前准备问题是非常关键的! 1、让老师能对症下药，提高你的课堂效率,
          2、不要问过于复杂或含糊的问题,尽量简洁明了地表达你的疑问。
          请说出你的课前问题吧?
        </view>
      </view>
      <view class="chatting-item" v-for="(item, index) of list" :key="index">
        <text
          user-select
          v-if="item.source == 'AI' && item.type == 'text'"
          class="AI-text"
        >
          {{ item.content.content }}
        </text>
        <text
          user-select
          v-if="item.source == 'user' && item.type == 'text'"
          class="user-text"
        >
          {{ item.content.content }}
        </text>
        <view
          v-if="item.source == 'user' && item.type == 'image'"
          class="user-image"
        >
          <imagePre :src="item.content.content"></imagePre>
        </view>
      </view>
    </view>
  </scroll-view>
</template>

<script>
import imagePre from '../../home/<USER>/image-pre.vue'
export default {
  components: {
    imagePre
  },
  props: {
    list: {
      type: Array,
      default: []
    },
    padding: {
      type: String,
      default: 'padding: 12px 12px 0px 12px;'
    }
  },
  data() {
    return {
      scrollTop: 0,
      length: 1,
      scrollWithAnimation: false,
      text: '👋Hey～是不是又遇到头疼的难题，小拉帮你召唤清北学霸帮帮团，助你秒变作业区最靓的崽💥'.match(
        /.{1,2}/g
      ),
      showText: ''
    }
  },
  created() {
    this.textInit()
  },
  mounted() {},
  methods: {
    textInit() {
      let i = 0
      let interval = setInterval(() => {
        if (this.text[i]) {
          this.showText += this.text[i]
        } else {
          clearInterval(interval)
        }
        i++
      }, 100)
    }
  }
}
</script>

<style lang="scss" scoped>
.chatting {
  // position: fixed;
  // top: 129px;
  // left: 0;
  // z-index: 1;
  height: 100%;
  width: 100%;
  .chatting-content {
    // padding: 129px 12px 134px 12px;
    display: flex;
    flex-direction: column;
    .chatting-item {
      display: flex;
    }
  }
  .stop {
    width: 92px;
    display: flex;
    padding: 10px;
    margin-top: 3px;
    margin-bottom: 4px;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    image {
      margin-right: 4px;
      width: 16px;
      height: 16px;
    }
    text {
      font-weight: 400;
      font-size: 13px;
      color: #222333;
    }
  }
  .status-label {
    margin: 8px auto;
    font-weight: 400;
    font-size: 12px;
    color: #ffffff;
    width: 74px;
    text-align: center;
    height: 24px;
    line-height: 24px;
    background: rgba(34, 35, 51, 0.5);
    border-radius: 12px 12px 12px 12px;
  }
  .tips {
    padding: 20px 0;
    text-align: center;
    width: 100%;
    font-weight: 400;
    font-size: 12px;
    color: rgba(34, 35, 51, 0.45);
  }
  .AI-view {
    margin: 5px 0;
    padding: 10px 16px;
    font-weight: 400;
    font-size: 15px;
    color: #222333;
    line-height: 18px;
    text-align: left;
    max-width: 81.86vw;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    border: 1px solid #ffffff;
    margin-right: auto;
    white-space: pre-line;
    .RichText {
      width: 100%;
    }
  }
  .one-text {
    margin: 8px 0;
    margin-top: 0;
    padding: 12px 12px;
    font-weight: 500;
    // font-size: 13px;
    color: #222333;
    line-height: 20px;
    text-align: left;
    width: 100%;
    background: rgba(34, 35, 51, 0.1);
    border-radius: 10px;
    border: 1px solid #ffffff;
    margin-right: auto;
    white-space: pre-line;
    view:nth-child(1) {
      font-weight: 500;
      font-size: 16px;
      color: #222333;
      line-height: 19px;
      margin-bottom: 6px;
    }
  }
  .AI-text {
    margin: 5px 0;
    padding: 8px 12px;
    font-weight: 400;
    font-size: 13px;
    color: #222333;
    line-height: 20px;
    text-align: left;
    max-width: 65.0666vw;
    background: rgba(34, 35, 51, 0.1);
    border-radius: 10px;
    border: 1px solid #ffffff;
    margin-right: auto;
    white-space: pre-line;
  }
  .user-text {
    margin: 5px 0;
    margin-left: auto;
    padding: 8px 12px;
    font-weight: 400;
    font-size: 13px;
    color: #222333;
    line-height: 20px;
    text-align: left;
    max-width: 44vw;
    background: #dbf46b;
    border-radius: 10px;
    white-space: pre-line;
    word-wrap: break-word;
    word-break: break-word;
    white-space: normal;
  }
  .user-image {
    max-width: 200px;
    margin: 8px 0;
    margin-left: auto;
  }
}
</style>
