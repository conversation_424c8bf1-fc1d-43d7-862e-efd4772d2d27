<template>
  <div class="share-report">
    <snapshot mode="view" id="targetShare">
      <view class="body">
        <image
          class="logo"
          src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/8863174566363982078974_Group%20228%402x.png"
        ></image>
        <view class="title"> {学情诊断} </view>
        <view class="student">
          <image :src="$xh.completepath(userinfo2.avatar)"></image>
          <view>{{ userinfo2.name }} </view>
        </view>
        <view class="content">
          <view class="content-title">
            {{ content.grade || '' }}{{ content.subject || '' }}
          </view>
          <view class="item">
            <view class="item-head">
              <image
                src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/2b1a174566369502552546_Group%20225%402x.png"
              ></image>
              <view>问题</view>
            </view>
            <view class="text"> {{ content.question }}</view>
          </view>
          <view class="fen"></view>
          <view class="item">
            <view class="item-head">
              <image
                src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/2fea174566374633030718_Group%20226%402x.png"
              ></image>
              <view>讲解</view>
            </view>
            <view class="text"> {{ content.analysis }}</view>
          </view>
          <view class="fen"></view>
          <view class="item">
            <view class="item-head">
              <image
                src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/928b174566376047899967_Group%20227%402x.png"
              ></image>
              <view>评价</view>
            </view>
            <view class="text"> {{ content.evaluate }}</view>
          </view>
        </view>

        <view class="date"> 生成于 {{ content.created_at }} </view>
      </view>
    </snapshot>
  </div>
</template>

<script>
export default {
  data() {
    return {
      shareImgData: ""
    }
  },
  props: {
    content: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    userinfo2() {
      return this.$store.state.kaolaStudent.userinfo2
    }
  },
  created() {
    const fs = wx.getFileSystemManager()
    const rootPath = wx.env.USER_DATA_PATH // 文件存储根目录
    // 递归删除目录下的所有文件
    function clearDirectory(dirPath) {
      try {
        const files = fs.readdirSync(dirPath) // 读取目录下的文件列表
        for (const file of files) {
          const fullPath = `${dirPath}/${file}`
          const stat = fs.statSync(fullPath)
          if (stat.isDirectory()) {
            clearDirectory(fullPath) // 递归删除子目录
            fs.rmdirSync(fullPath) // 删除空目录
          } else {
            fs.unlinkSync(fullPath) // 删除文件
          }
        }
      } catch (error) {
        console.error('清理失败:', error)
      }
    }
    // 调用清理函数
    clearDirectory(rootPath)
  },
  methods: {
    loadImg(type) {
      // 成功回调函数，res中包含生成的图片数据
      // console.log('生成海报成功', res)
      let m = Math.floor(Math.random() * 100000)
      const f = `${wx.env.USER_DATA_PATH}/share${m}.png`
      const fs = wx.getFileSystemManager()
      console.log(f, 'f')
      // 将海报数据写入本地文件
      // fs.writeFileSync(f, res.data, 'binary')

      try {
        fs.writeFileSync(f, this.shareImgData, 'binary')
      } catch (error) {
        console.log(error, 'err')
      }
      // console.log(f)
      if (type == 1) {
        wx.saveImageToPhotosAlbum({
          filePath: f,
          success: () => {
            uni.hideLoading()
            uni.showToast({
              title: '图片保存成功！',
              icon: 'none'
            })
          },
          fail: res => {
            uni.hideLoading()
          }
        })
      }
      if (type == 2) {
        console.log(f)
        wx.showShareImageMenu({
          path: f,
          success: () => {
            uni.hideLoading()
          },
          fail: res => {
            uni.hideLoading()
          }
        })
      }
    },
    share(type = 2) {
      if (this.shareImgData) {
        this.loadImg(type)
        return
      }
      this.createSelectorQuery()
        .select('#targetShare')
        .node()
        .exec(res => {
          const node = res[0].node
          node.takeSnapshot({
            type: 'arraybuffer', // 截图类型，可选值为'arraybuffer'或'base64'
            format: 'png', // 图片格式，可选值为'jpg'或'png'
            success: res => {
              this.shareImgData = res.data
              this.loadImg(type)
            },
            fail: res => {
              uni.hideLoading()
              // 失败回调函数，res中包含错误信息
              console.error('生成海报失败', res)
            }
          })
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.share-report {
  position: fixed;
  left: -120vw;
  top: 0;
  z-index: 100;
  background-color: #000;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  snapshot {
    width: 100%;
  }
  .body {
    width: 100%;
    padding: 16px 16px 0 16px;
    background-image: url(https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/5090174566367495584184_Group%20243%402x.png);
    background-size: 100% 237px;
    background-repeat: no-repeat;
    background-color: #fff;
    .logo {
      width: 64px;
      height: 26px;
    }
    .content {
      margin-top: 16px;
      padding: 0 12px 24px 12px;
      background: rgba(238, 238, 238, 0.65);
      border-radius: 12px 12px 12px 12px;
      // border: 1px solid #ffffff;
      .fen {
        margin: 20px 0;
        border-bottom: 1px dashed rgba(214, 219, 227, 0.95);
      }
      .content-title {
        margin: 0 auto;
        width: 119px;
        height: 26px;
        font-weight: 500;
        font-size: 14px;
        color: #222333;
        line-height: 26px;
        text-align: center;
        background-image: url(https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/4bf6174566366014836615_Rectangle%20374%402x.png);
        background-size: 119px 26px;
      }
      .item {
        .item-head {
          display: flex;
          align-items: center;
          image {
            width: 20px;
            height: 20px;
            margin-right: 6px;
          }
          view {
            font-weight: 500;
            font-size: 18px;
            color: #222333;
          }
        }
        .text {
          font-weight: 400;
          font-size: 14px;
          color: rgba(34, 35, 51, 0.65);
          line-height: 24px;
        }
      }
    }
    .title {
      font-family: TBMCYXT, TBMCYXT;
      font-weight: 400;
      font-size: 49px;
      color: #222333;
      margin-top: 20px;
      margin-bottom: 25px;
    }
    .student {
      display: flex;
      align-items: center;
      image {
        width: 32px;
        height: 32px;
        background: #999faa;
        border-radius: 50%;
        margin-right: 4px;
      }
      view {
        font-weight: 400;
        font-size: 14px;
        color: rgba(34, 35, 51, 0.85);
        line-height: 24px;
      }
    }
    .date {
      padding: 16px 0;
      text-align: center;
      font-weight: 400;
      font-size: 12px;
      color: rgba(34, 35, 51, 0.8);
    }
  }
}
</style>
