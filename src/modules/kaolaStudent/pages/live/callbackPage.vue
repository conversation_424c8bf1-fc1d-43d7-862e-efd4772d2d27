<template>
  <view class="live-page back">
    <u-navbar
      title="课程"
      bgColor="rgba(255,255,255,0)"
      @rightClick="rightClick"
      :autoBack="true"
    >
    </u-navbar>
    <view class="nav-block"></view>

    <u-search
      placeholder="搜索您想查找的内容"
      :showAction="false"
      bgColor="#ffffff"
      v-model="searchInput"
      @search="getCallbackData"
    ></u-search>

    <view class="page-h-title">专题</view>
    <scroll-view class="live-list-x" :scroll-x="true">
      <view class="list-x-wrap">
        <view
          class="special-item"
          v-for="item in liveSpecialList"
          :key="item.id"
          @click="goSpecialPage(item)"
        >
          <view class="image-zoon">
            <image
              :src="$xh.completepath(item.cover_image_url)"
              mode="aspectFill"
              class="cover-img"
            ></image>
          </view>
          <view class="special-title">{{ item.name }}</view>
        </view>
      </view>
    </scroll-view>
    <!-- <u-tabs :list="tabList" @click="click"></u-tabs> -->
    <view class="tab-block">
      <view class="page-h-title">课程</view>
    </view>

    <scroll-view
      :scroll-top="scrollTop"
      scroll-y="true"
      class="scroll-Y"
      @scrolltoupper="upper"
      @scrolltolower="lower"
      @scroll="scroll"
    >
      <view class="live-list">
        <view
          class="live-item"
          @click="goCallbackDetail(item)"
          v-for="item in callbackList"
          :key="item.id"
        >
          <view class="live-img">
            <image
              class="live-img-img"
              mode="aspectFill"
              :src="customComplete(item.material_cover_path)"
            />
            <view class="live-count">
              <u-icon name="eye"></u-icon>
              <text class="count-text">{{ item.pv }}</text>
            </view>
          </view>
          <view class="live-content">
            <view class="live-title">
              <text class="title-text u-line-1">{{ item.name }}</text>
              <view class="buy-tag" v-if="item.vip_level != 0"
                >购买会员可看</view
              >
            </view>
            <view class="live-time">{{ item.showDate }}</view>

            <!-- <view class="tearcher-wrap">
              <u-avatar :src="customComplete(item.avatar)" :size="24"></u-avatar>
              <text class="teacher-name">{{ item.teacher_name }}</text>
              <text class="teacher-name">{{ item.school_name }}</text>
            </view> -->
          </view>
        </view>
      </view>
    </scroll-view>

    <u-modal :show="showTip" title=" " content="" :showConfirmButton="false">
      <view class="slot-content">
        <view class="tip-text">
          <text class="text">升级会员</text>
          <image
            class="bg-image"
            src="../../../../static/imgs/kaolaStudent/tip-title-bg.png"
          ></image>
        </view>
        <view class="tip-content">当前视频，需要成为会员，才可查看</view>
        <view class="update-btn" @click="goVipPage">升级会员</view>
        <view class="text-button" @click="showTip = false">我再想想</view>
      </view>
    </u-modal>
  </view>
</template>
<script>
import { square, teaching } from '../../api'

export default {
  name: 'livePage',
  data() {
    return {
      searchInput: '',
      showTip: false,

      tabList: [
        {
          name: '全部',
          id: 1
        },
        {
          name: '课程',
          id: 2
        }
      ],
      currentTab: 1,
      liveSpecialList: [],
      callbackList: []
    }
  },
  onLoad() {
    this.getLiveSpecial()
    this.getCallbackData()
  },

  onShow() {
    console.log('onShow - 直播详情')
    if (this.showTip) {
      this.showTip = false
    }
  },

  methods: {
    rightClick() {
      uni.navigateBack()
    },
    goVipPage() {
      this.$xh.push('kaolaStudent', 'pages/my/vip/index')
    },
    getCallbackData() {
      teaching
        .getCallbackList({
          name: this.searchInput
        })
        .then(res => {
          if (res && res.data) {
            this.callbackList = res.data.list.map(v => {
              let showDate = ''
              if (v.start_time) {
                const dateDay = v.start_time.split(' ')[0]
                const shortStartTime = dayjs(v.start_time).format('HH:MM')
                const shortEndTime = dayjs(v.end_time).format('HH:MM')
                showDate = `${dateDay} ${shortStartTime}-${shortEndTime}`
              }
              return {
                ...v,
                showDate
              }
            })
          }
        })
    },
    goCallbackDetail(item) {
      const params = { goods_id: item.id }

      square.getCallbackDetail(params).then(res => {
        console.log('录播详情： ', res)
        if (Number(res.data.student_vip_level) < Number(res.data.vip_level)) {
          this.showTip = true
        } else {
          this.$xh.push('kaolaStudent', 'pages/live/callbackPlay?id=' + item.id)
        }
      })
    },

    getLiveSpecial() {
      teaching
        .getTeachingList({
          noloading: true,
          loading: true,
          teaching_type: 3,
          code: 'system',
          is_auth: 0,
          page: 1,
          size: 200
        })
        .then(res => {
          console.log('专题列表', res)
          this.liveSpecialList = res.data?.list
        })
    },
    click(index) {
      this.currentTab = index.id
    },
    clickTab(item) {
      console.log(item)
    },
    upper() {
      console.log('upper')
    },

    customComplete(path) {
      if (path && path.startsWith('http')) {
        return path
      } else if (path) {
        return this.$xh.completepath(path)
      } else {
        return ''
      }
    },
    lower() {
      console.log('lower')
    },
    scroll() {},
    goLiveDetail() {
      this.$xh.push('kaolaStudent', 'pages/live/liveDetail')
    },
    goSpecialPage(item) {
      console.log('专题信息', item)
      this.$xh.push(
        'kaolaStudent',
        `pages/live/specialTopicList?id=${item.id}&type=3&title=${item.name}&image=${item.cover_image_url}`
      )
    }
  }
}
</script>
<style lang="scss" scoped>
.live-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10rpx;
}

.live-card {
  width: 100%;
  height: 27vh;
  border-radius: 6rpx;
  background-color: #fff;

  .card-title {
    font-weight: 600;
  }
}

.live-img-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.live-page {
  padding: 32rpx;

  &.back {
    background-image: url('https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/626f174562773210963083_Group%20272%402x.png');

    background-repeat: no-repeat;
    background-position: 12.92% -7.5%;
  }

  .nav-block {
    height: 152rpx;
  }

  .slot-content {
    width: 624rpx;
    height: 440rpx;
    border-radius: 32rpx;
    padding: 64rpx;

    .tip-text {
      width: 162rpx;
      height: 64rpx;
      margin: 0 auto 0;
      text-align: center;
      font-weight: 700;
      font-size: 40rpx;
      letter-spacing: 0%;
      color: #222333;
      position: relative;

      .text {
        position: absolute;
        top: 0;
        width: 100%;
        text-align: center;
        height: 32rpx;
        left: 0;
        z-index: 2;
      }

      .bg-image {
        position: absolute;
        z-index: 2;
        bottom: 0;
        left: 0;
        height: 20rpx;
        width: 100%;
      }
    }

    .tip-content {
      font-weight: 400;
      font-size: 28rpx;
      color: #222333db;
      padding-top: 32rpx;
    }

    .update-btn {
      width: 496rpx;
      height: 88rpx;
      line-height: 88rpx;
      border-radius: 100rpx;
      background: #2d2a40;
      color: #fff;
      text-align: center;
      margin-top: 64rpx;
    }

    .text-button {
      font-weight: 400;
      font-size: 28rpx;
      color: #222333db;
      padding-top: 16rpx;
      text-align: center;
      margin: 0 auto;
    }
  }

  .page-h-title {
    font-weight: 500;
    font-size: 32rpx;
    height: 52rpx;
    line-height: 52rpx;
    margin-bottom: 16rpx;
    margin-top: 32rpx;
  }

  .list-x-wrap {
    display: inline-block;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    height: 160rpx;
  }

  .special-item {
    width: 320rpx;
    flex-shrink: 0;
    height: 160rpx;
    border-radius: 32rpx;
    background-color: #e6eaef;
    margin-right: 16rpx;
    padding: 16rpx;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .image-zoon {
      width: 128rpx;
      height: 128rpx;
      border-radius: 20rpx;
      background-color: #fff;
      margin-right: 16rpx;
      flex-shrink: 0;
    }
  }

  .live-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 32rpx;

    .live-item {
      width: 320rpx;
      // width: 170rpx;
      position: relative;
      background-color: #fff;
      border-radius: 32rpx;

      .live-time {
        font-weight: 400;
        font-size: 24rpx;
        line-height: 100%;
        letter-spacing: 0%;
        color: #666;
        padding-top: 16rpx;
      }

      .tearcher-wrap {
        padding-top: 16rpx;
        padding-bottom: 16rpx;
        font-size: 24rpx;
        color: #999;
        display: flex;
        justify-content: flex-start;
        align-items: center;

        .teacher-name {
          font-weight: 400;
          font-size: 24rpx;
          font-family: 'PuHuiTi_55_Regular';
          padding: 0 16rpx;
          color: #000;
        }
      }
    }

    .live-tag {
      position: absolute;
      top: 23rpx;
      right: 16rpx;
      border-radius: 100rpx;
      color: #333;
      font-size: 22rpx;
      font-weight: 600;
      z-index: 2;
      padding: 8rpx 16rpx;
      background: linear-gradient(90deg, #f2fb5d 0%, #baed26 100%);
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .live-tag-icon {
      width: 18rpx;
      height: 20rpx;
      margin-left: 8rpx;
    }

    .live-count {
      position: absolute;
      z-index: 3;
      bottom: 16rpx;

      right: 16rpx;
      display: flex;
      justify-content: center;
      align-items: center;

      .count-text {
        color: #666;
        font-family: 'SourceHanSansCN-VF';
        font-weight: 500;
        font-style: Medium;
        font-size: 16rpx;
        letter-spacing: 5%;
        margin-left: 15rpx;
      }
    }

    .live-img {
      width: 100%;
      height: 254rpx;
      background-color: #f0f0f0;
      border-radius: 32rpx 32rpx 0 0;
      position: relative;
    }

    .buy-tag {
      font-weight: 500;
      width: 120rpx;
      flex-shrink: 0;
      font-size: 16rpx;
      padding: 6rpx 10rpx;
      border-radius: 6rpx;
      color: #000;
      background-color: #ffd7a4;
    }

    .live-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-text {
        font-family: 'SourceHanSansCN-VF';
        font-weight: 600;
        font-size: 24rpx;
        line-height: 42rpx;
        letter-spacing: 5%;
        color: #000;
        width: 176rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .live-content {
    padding: 16rpx;
  }

  .scroll-Y {
    height: 880rpx;
  }

  .special-title {
    font-size: 26rpx;
    font-weight: 400;
    letter-spacing: 1%;
    color: #000;
  }

  .cover-img {
    width: 100%;
    height: 100%;
    border-radius: 32rpx;
    object-fit: cover;
  }

  .tab-block {
    width: 100%;
  }
}
</style>
