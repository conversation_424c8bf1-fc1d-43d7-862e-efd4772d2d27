<template>
  <div class="theme-page">
    <!-- <image class="backImage" :src="backgroup_img"></image> -->

    <view class="topBack"></view>
    <view v-if="imageLoading" class="loading-indicator">加载中...</view>
    <image class="topimg2" mode="heightFix" :src="backgroup_img" @load="onImageLoad" @error="onImageError" v-show="!imageLoading"></image>
    <!-- <view class="endBack"></view> -->
  </div>
</template>

<script>
export default {
  data() {
    return {
      imageLoading: true,
      top: '',
      img2: 'https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/5613174479339904841767_BgSub_%E7%94%B7%E5%AD%A92.pic%402x.png',
      img1: 'https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/7524174479334697112551_BgSub_20701744698010_.pic%201%402x.png'
    }
  },
  methods: {
    onImageLoad() {
      this.imageLoading = false;
    },
    onImageError() {
      this.imageLoading = false;
      // 你可以在这里添加一些错误处理逻辑，比如显示一个加载失败的提示
      console.error('Image failed to load');
    }
  },
  created() {
    let top = (wx.getSystemInfoSync().statusBarHeight || 25) - 7
    this.top = `top:${top + 54}px;`
    // console.log('top - 需要根据更新换取图片')
  },
  computed: {
    backgroup_img() {
      const use =  this.$store.getters['kaolaStudent/getAiAvatar'];
      if(!use){
        return this.img2
      }
      if(use.id==2){
        return this.img1
      }else{
        return this.img2
      }
      // return this.$store.getters['kaolaStudent/getAiAvatar'].backgroup_img
    }
  }
}
</script>

<style lang="scss" scoped>
.topBack {
  position: absolute;
  left: 0;
  top: 0;
  width: 375px;
  height: 242px;
  // width: 100%;
  // height: 100%;
  // height: 64.533vw;
  // background: linear-gradient(180deg, #d4ff00 0%, #f7f9fb 100%),
  //   linear-gradient(180deg, #d4ff00 0%, #f3f5f8 100%), #eceef1;
  border-radius: 0px 0px 0px 0px;
  background-size: 100% 100%;
  background-image: url(https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/626f174562773210963083_Group%20272%402x.png);
}

.topBack2 {
  position: absolute;
  left: -60px;
  top: 38px;
  width: 123px;
  height: 123px;
  // background: rgba(255, 225, 252, 0.86);
  // border-radius: 0px 0px 0px 0px;
  filter: blur(20.0999984741211px);
  background-image: radial-gradient(
    circle,
    rgba(255, 225, 252, 0.86),
    rgba(255, 225, 252, 0)
  );
  z-index: 1;
}
.loading-indicator {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  color: #888;
  font-size: 14px;
  z-index: 2; /* Ensure loading is above the image placeholder if any */
}

.topimg2 {
  position: absolute;
  // top: calc(70px + 26px);
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
  // width: 149px;
  height: 143px;
  // border: 1px solid rgb(98, 0, 255);
}
.endBack {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 134px;
  background: linear-gradient(180deg, rgba(107, 115, 157, 0) 0%, #6b739d 100%);
  border-radius: 0px 0px 0px 0px;
}
.backImage {
  width: 100vw;
  height: 216.53vw;
}
.theme-page {
  position: fixed;
  left: 0;
  // bottom: 0;
  width: 100vw;
  height: 242px;
  // height: 100vh;
  // border: 1px solid red;
  overflow-y: hidden;
  background: linear-gradient(180deg, #d4ff00 0%, #f7f9fb 129px), #eceef1;
}
</style>
