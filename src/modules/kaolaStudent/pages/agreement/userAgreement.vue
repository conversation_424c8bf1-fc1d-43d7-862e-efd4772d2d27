<template>
  <view class="protocol-container">
    <view class="protocol-header">
      <!-- 返回按钮 -->
      <uni-icons
        type="left"
        size="24"
        class="back-icon"
        @tap="goBack"
      ></uni-icons>
      <text class="protocol-title">考好啦AI用户服务协议</text>
      <text class="protocol-date">更新日期：2025年08月25日</text>
      <text class="protocol-date">生效日期：2025年08月25日</text>
    </view>
    <scroll-view class="protocol-content" scroll-y="true">
      <view class="protocol-section">
        <text class="section-content"
          >欢迎访问考好啦AI！北京考好啦科技有限公司（下称"考好啦AI"）负责考好啦AI网站及考好啦AI微信小程序、手机应用软件的运营、管理。</text
        >
        <text class="section-content"
          >本协议由您与考好啦AI共同缔结，本协议具有合同效力。</text
        >
        <text class="section-content">本协议中协议双方合称协议方。</text>
        <text class="section-content"
          >本协议内容包括协议正文及所有考好啦AI已经发布的或将来可能发布的各类规则，所有规则为本协议不可分割的组成部分，与协议正文具有同等法律效力。</text
        >
        <text class="section-content"
          >本协议阐述之条款和条件适用于考好啦AI网站和考好啦AI微信小程序、手机应用软件的全部学习服务，包括但不限于各种课程视频、学习内容、资料及相应服务。</text
        >
      </view>

      <view class="protocol-section">
        <text class="section-content"
          >您应当在使用考好啦AI服务之前认真阅读全部协议内容（未成年人应在法定监护人陪同下阅读）。如对协议有任何疑问，应向考好啦AI咨询，咨询邮箱：<EMAIL>。</text
        >
        <text class="section-content"
          >但无论您事实上是否在使用考好啦AI服务之前认真阅读了本协议内容，只要使用考好啦AI及相关服务，则本协议即对您产生约束，届时您不应以未阅读本协议的内容或者未获得考好啦AI对您问询的解答等理由，主张本协议无效，或要求撤销本协议。您承诺接受并遵守本协议的约定。如果您不同意本协议的约定，应立即停止注册程序或停止使用考好啦AI服务。</text
        >
      </view>

      <view class="protocol-section">
        <text class="section-content"
          >考好啦AI团队有权根据相关法律法规的变更或业务变化等不时地制订、修改本协议及/或各类规则，并将调整后的协议公布于考好啦AI官网或者考好啦AI程序中。如您不同意相关变更，应当立即停止使用考好啦AI服务。若您继续使用考好啦AI服务，即表示您接受经修订的协议。</text
        >
        <text class="section-content"
          >考好啦AI将通过弹窗、站内信、短信等显著方式向用户告知更新内容，更新内容自告知之日起7日后生效。</text
        >
      </view>

      <view class="protocol-section">
        <text class="section-title">一、名词解释</text>
        <text class="section-content">为避免主体混乱，特作如下定义：</text>
        <text class="section-content"
          >•学生：指购买并使用考好啦AI课程服务的个人用户。</text
        >
        <text class="section-content"
          >•教师：指经考好啦AI认证，为学生提供授课、辅导等服务的人员。</text
        >
        <text class="section-content"
          >•用户：除非另有明确说明，本协议中的"用户"特指学生。涉及教师权利义务时，将单独说明。</text
        >
        <text class="section-content"
          >•订单：指用户通过考好啦AI下单购买的服务，包括但不限于课程、资料、答疑、直播辅导。</text
        >
        <text class="section-content"
          >•数字化商品：指通过网络交付的内容，包括课程视频、录播、直播、电子讲义等。</text
        >
        <text class="section-content"
          >•不可抗力：指不可预见、不可避免、不可克服的客观情况，如地震、火灾、洪水、战争、政府行为等。</text
        >
      </view>

      <view class="protocol-section">
        <text class="section-title">二、基本规则</text>
        <text class="section-content"
          >用户充分了解并同意：用户必须为自己注册账号下的一切行为负责，包括用户所传送的任何内容以及由此产生的任何结果。</text
        >
        <text class="section-content"
          >产品中考好啦AI的原创内容，版权完全归考好啦AI所有，用户不得拷贝他用，不得上传到其他网络平台。考好啦AI保留对此种非法行为进行追究的权利。</text
        >
        <text class="section-content"
          >用户不得利用考好啦AI进行违反国家法律法规、侵犯他人权益的行为。考好啦AI可依其合理判断，对违反有关法律法规或本协议约定的用户进行处理，并且依据法律法规保存有关信息并向有关部门报告。</text
        >
      </view>

      <view class="protocol-section">
        <text class="section-title">三、考好啦AI使用规则</text>

        <text class="section-subtitle">用户注册</text>
        <text class="section-content"
          >用户需使用本人常用手机号码注册并设置密码，通过验证码验证完成注册。在个人中心里，用户可设置相关个人信息并查询课程记录和资金情况。</text
        >

        <text class="section-subtitle">账户管理</text>
        <text class="section-content"
          >用户应妥善保管账户及密码信息，不得转让、出借、出租或共享给他人使用。</text
        >
        <text class="section-content"
          >严禁以任何盈利或非盈利目的倒卖、出租、共享账户，一经查明，考好啦AI有权采取包括但不限于封号、封禁IP、追责等措施。</text
        >

        <text class="section-subtitle">订单及支付</text>
        <text class="section-content"
          >"订单"指用户根据自身需求与考好啦AI达成的在线教学服务协议。订单在以下条件同时满足时成立：</text
        >
        <text class="section-content"
          >1）用户在考好啦AI选购符合需求的课程；</text
        >
        <text class="section-content">2）用户完成支付。</text>
        <text class="section-content"
          >用户在下单时应仔细确认以下信息：
          主讲教师、课程名称、课程时长、课程价格
          。此外，如遇法律规定的其他情形，应遵循法律要求。</text
        >
        <text class="section-content"
          >数字化商品：若已一次性交付且用户确认收货，则 不适用七日无理由退货
          。但如存在课程质量问题，用户可申请退货或退款。</text
        >

        <text class="section-subtitle">上课流程与服务约定</text>
        <text class="section-content"
          >授课方式包括在线直播、视频录播，用户可在 规定的回放时限
          内观看课程（例如3天、1年或永久，具体以课程页面公示为准）。</text
        >
        <text class="section-content">上课时间或教师变更：</text>
        <text class="section-content"
          >学生/教师因事申请 ：需说明原因，系统弹窗提示并后台备案。</text
        >
        <text class="section-content"
          >教师不合格
          ：如考核不合格或出现异常，平台须提前与学生沟通并更换。</text
        >

        <text class="section-subtitle">退款与定价政策</text>
        <text class="section-content"
          >学生因故未能完成课程，可依规则申请调班或退款。</text
        >
        <text class="section-content"
          >若课程讲义已发出，退款时将扣除讲义成本。</text
        >
        <text class="section-content"
          >定价依据包括教师学历、教学反馈、学生评价等，考好啦AI保留定价权。</text
        >
        <text class="section-content"
          >建议退费规则单独成协议，突出用户核心关切。</text
        >

        <text class="section-subtitle">账户注销</text>
        <text class="section-content"
          >符合注销条件时，用户可申请注销，注销即视为协议终止。注销后用户信息将
          立即删除，不留存备份 。</text
        >

        <text class="section-subtitle">防止教师跳单</text>
        <text class="section-content"
          >教师与学生之间的所有沟通、授课、答疑、课后辅导，均应通过考好啦AI平台内置的通信与教学系统进行。若教师存在任何形式的"跳单"行为（包括但不限于直接或间接提供个人联系方式、引导学生家长绕过平台交易），平台将视情节严重程度采取以下措施：</text
        >

        <text class="section-subtitle">轻度违规（初次发现）</text>
        <text class="section-content">警告与提醒；</text>
        <text class="section-content"
          >扣除信用分，减少课程推荐和流量曝光；</text
        >
        <text class="section-content">记入教师违规档案。</text>

        <text class="section-subtitle"
          >中度违规（再次发现或存在明显引导行为）</text
        >
        <text class="section-content">暂停教师授课权限（7—30日）；</text>
        <text class="section-content">扣除当月部分课酬（比例 20%—50%）；</text>
        <text class="section-content">平台停止发放相关奖金、激励。</text>

        <text class="section-subtitle"
          >严重违规（多次跳单或成功绕过平台交易）</text
        >
        <text class="section-content">永久封禁账号；</text>
        <text class="section-content">扣除全部未结算课酬及保证金；</text>
        <text class="section-content"
          >追回因违约给平台造成的直接经济损失（包括订单佣金、服务费、市场推广成本等），并加收违约金（通常为违规交易金额的
          3—5倍 ）。</text
        >

        <text class="section-subtitle">法律追责</text>
        <text class="section-content"
          >平台保留提起民事诉讼的权利，要求违约教师赔偿全部直接和间接损失（包括律师费、公证费等维权成本）；</text
        >
        <text class="section-content"
          >若涉及侵犯商业秘密、恶意串通扰乱市场等，平台将依法移交监管机关处理。</text
        >
      </view>

      <view class="protocol-section">
        <text class="section-title">四、用户权利与义务</text>
        <text class="section-content"
          >用户享有《民法典》赋予的权利，包括知情权、受教育权、知识产权保护权。</text
        >
        <text class="section-content">隐私权相关内容以《隐私协议》为准。</text>
        <text class="section-content"
          >用户仅可将所购课程用于本人学习，禁止录屏、下载、上传或分发。</text
        >
        <text class="section-content"
          >用户可发表评论或意见，但不得含有违法、侮辱、诽谤或虚假内容。</text
        >
      </view>

      <view class="protocol-section">
        <text class="section-title">五、用户隐私保护条款</text>

        <text class="section-subtitle">信息收集原则</text>
        <text class="section-content"
          >考好啦AI严格遵守《中华人民共和国个人信息保护法》，遵循合法、正当、必要、诚信及最小化收集原则，仅在提供服务所必需范围内收集用户个人信息。</text
        >

        <text class="section-subtitle">收集与使用范围</text>
        <text class="section-content"
          >用户在注册、使用服务时，可能需提供包括但不限于：姓名、联系方式、年级、科目、支付信息、设备信息。考好啦AI不会收集与服务无关的敏感信息，除非经用户单独同意。</text
        >

        <text class="section-subtitle">权限与使用场景</text>
        <text class="section-content" style="margin-bottom: 20rpx;">
          考好啦AI在获取相关系统权限时，将在前端以弹窗方式明示。详情如下：
        </text>
        
        <!-- 权限表格 -->
        <view class="permission-table">
          <!-- 表头 -->
          <view class="table-header">
            <view class="table-cell type-cell">权限类型</view>
            <view class="table-cell usage-cell">使用场景</view>
            <view class="table-cell required-cell">是否必须</view>
          </view>
          
          <!-- 表格内容行 -->
          <view class="table-row">
            <view class="table-cell type-cell">相册权限</view>
            <view class="table-cell usage-cell">上传头像、作业资料</view>
            <view class="table-cell required-cell">否</view>
          </view>
          
          <view class="table-row">
            <view class="table-cell type-cell">摄像头权限</view>
            <view class="table-cell usage-cell">在线答疑、视频互动</view>
            <view class="table-cell required-cell">否</view>
          </view>
          
          <view class="table-row">
            <view class="table-cell type-cell">麦克风权限</view>
            <view class="table-cell usage-cell">课堂连麦、语音互动</view>
            <view class="table-cell required-cell">否</view>
          </view>
          
          <view class="table-row">
            <view class="table-cell type-cell">通讯录权限</view>
            <view class="table-cell usage-cell">推荐邀请好友功能</view>
            <view class="table-cell required-cell">否</view>
          </view>
        </view>

        <text class="section-subtitle">信息共享与披露</text>
        <text class="section-content"
          >除以下情形外，考好啦AI不会向任何第三方分享或披露用户个人信息：</text
        >
        <text class="section-content">事先取得用户的明确同意；</text>
        <text class="section-content">根据法律法规或政府机关要求；</text>
        <text class="section-content"
          >为实现核心功能且第三方仅限于必要范围（如支付机构、快递公司）；</text
        >
        <text class="section-content">用户主动要求的共享或分发。</text>

        <text class="section-subtitle">信息存储与删除</text>
        <text class="section-content"
          >用户信息存储期限为实现服务所必需的最短时间。用户注销账号后，考好啦AI将立即删除其个人信息，且不再保留任何备份。</text
        >

        <text class="section-subtitle">未成年人保护</text>
        <text class="section-content"
          >考好啦AI特别重视未成年人信息保护。18周岁以下用户使用服务前，应取得监护人书面同意。平台对未成年人信息将采取加密存储、限制访问等措施，并在官网设立《儿童隐私保护声明》供查询。</text
        >
      </view>

      <view class="protocol-section">
        <text class="section-title">六、知识产权</text>

        <text class="section-subtitle">版权归属</text>
        <text class="section-content"
          >考好啦AI网站、应用及所提供的所有内容（包括但不限于课程视频、音频、图片、文字、代码、软件、架构设计），版权均归考好啦AI或其合作方所有，受《中华人民共和国著作权法》及相关国际条约保护。</text
        >

        <text class="section-subtitle">使用授权</text>
        <text class="section-content"
          >考好啦AI授予用户一项 非独占、不可转让、仅限于个人学习目的
          的使用权。未经书面许可，用户不得以任何形式复制、传播、展示、修改、出租、售卖、翻译、汇编或制作衍生作品。</text
        >

        <text class="section-subtitle">禁止行为</text>
        <text class="section-content"
          >用户不得实施以下行为，否则考好啦AI有权采取包括法律追责在内的措施：</text
        >
        <text class="section-content">擅自下载、录屏或传播课程内容；</text>
        <text class="section-content"
          >以商业目的复制、售卖、转发学习资料；</text
        >
        <text class="section-content">删除、篡改任何版权声明或权利标识；</text>
        <text class="section-content"
          >使用任何程序爬取或窃取考好啦AI数据与资料。</text
        >

        <text class="section-subtitle">知识产权侵权救济</text>
        <text class="section-content"
          >若用户侵犯考好啦AI或第三方的知识产权，考好啦AI有权立即删除相关内容、暂停或终止用户账号，并保留追究民事、行政或刑事责任的权利。</text
        >
      </view>

      <view class="protocol-section">
        <text class="section-title">七、法律责任与免责</text>

        <text class="section-subtitle">用户违约责任</text>
        <text class="section-content"
          >用户违反本协议约定、法律法规或侵犯第三方合法权益的，应当独立承担全部法律责任，并赔偿因此给考好啦AI造成的全部损失（包括直接损失、间接损失、维权费用、律师费等）。</text
        >

        <text class="section-subtitle">平台免责条款</text>
        <text class="section-content"
          >在法律允许的范围内，因下列原因导致的服务中断或损失，考好啦AI不承担责任：</text
        >
        <text class="section-content">用户个人操作不当或使用不当；</text>
        <text class="section-content">用户设备、网络故障；</text>
        <text class="section-content">第三方通讯、技术服务中断；</text>
        <text class="section-content"
          >黑客攻击、计算机病毒入侵等非平台原因。</text
        >

        <text class="section-subtitle">不可抗力条款</text>
        <text class="section-content"
          >因地震、火灾、洪水、战争、政府行为等不可抗力事件导致的服务中断，考好啦AI不承担违约责任。但及时通知用户并采取合理措施减少损失。</text
        >

        <text class="section-subtitle">责任限制</text>
        <text class="section-content"
          >在任何情况下，因使用本服务产生的赔偿责任总额不超过用户已支付的服务费用。</text
        >
      </view>

      <view class="protocol-section">
        <text class="section-title">八、服务终止</text>

        <text class="section-subtitle">用户主动终止</text>
        <text class="section-content"
          >用户可随时申请注销账号，注销后本协议即终止，考好啦AI将在合理期限内清除其数据。</text
        >

        <text class="section-subtitle">平台有权终止</text>
        <text class="section-content"
          >若用户存在以下情形，考好啦AI有权随时终止提供服务：</text
        >
        <text class="section-content">提供虚假信息或冒用他人身份；</text>
        <text class="section-content"
          >恶意攻击系统、传播病毒或干扰平台运营；</text
        >
        <text class="section-content">侵犯他人合法权益或发布违法信息；</text>
        <text class="section-content">多次违规、被投诉或严重违反本协议。</text>

        <text class="section-subtitle">通知与救济</text>
        <text class="section-content"
          >考好啦AI将在终止服务前，通过注册手机号、邮箱等联系方式通知用户。用户可在7日内提出申诉，平台将在15日内给予书面处理结果。</text
        >

        <text class="section-subtitle">服务终止的法律后果</text>
        <text class="section-content"
          >用户因终止前存在的违约行为，仍需承担相应责任；</text
        >
        <text class="section-content"
          >平台有权依据法律保留相关日志记录以备查询；</text
        >
        <text class="section-content"
          >用户已支付但未使用完的服务，平台将根据退费规则进行结算。</text
        >
      </view>

      <view class="protocol-section">
        <text class="section-title">九、其他条款</text>
        <text class="section-content"
          >本协议任一条款部分或全部无效，不影响其他条款效力。</text
        >
        <text class="section-content"
          >本协议适用中华人民共和国法律。若发生争议，应先协商，协商不成的，提交北京市有管辖权的人民法院。</text
        >
        <text class="section-content"
          >本协议版权归北京考好啦科技有限公司所有，公司保留解释权和修改权。</text
        >
        <text class="section-content">北京考好啦科技有限公司</text>
        <text class="section-content"
          >地址：北京市海淀区中关村大街18号B座16层1622室</text
        >
        <text class="section-content">邮箱：<EMAIL></text>
      </view>

      <!-- 底部留出空白，确保内容不被遮挡 -->
      <view class="bottom-space"></view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  onLoad() {
    uni.setNavigationBarTitle({
      title: '用户服务协议'
    })
  },
  methods: {
    goBack() {
      console.log('userAgreement-back')
      // 确保返回功能生效
      uni.navigateBack({
        delta: 1
      })
    }
  }
}
</script>

<style scoped>
.protocol-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
}

.protocol-header {
  padding: 40px 20px 20px; /* 增加顶部padding使内容下移 */
  text-align: center;
  position: relative;
}

.back-icon {
  position: absolute;
  left: 20px;
  top: 40px; /* 调整top值使按钮下移 */
  color: #333;
}

.protocol-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
  display: block;
  margin-bottom: 15rpx;
}

.protocol-date {
  font-size: 26rpx;
  color: #666666;
  display: block;
  margin-bottom: 8rpx;
}

.protocol-content {
  flex: 1;
  padding: 30rpx;
  box-sizing: border-box;
}

.protocol-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 15rpx;
  display: block;
  padding-bottom: 10rpx;
  border-bottom: 1rpx solid #eeeeee;
}

.section-subtitle {
  font-size: 28rpx;
  font-weight: bold;
  color: #444444;
  margin: 20rpx 0 10rpx 0;
  display: block;
}

.section-content {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  display: block;
  margin-bottom: 10rpx;
}

.bottom-space {
  height: 60rpx;
}

/* 权限表格样式 */
.permission-table {
  width: 100%;
  border-collapse: collapse;
  margin: 20rpx 0 40rpx 0;
  border: 1rpx solid #eeeeee;
  border-radius: 10rpx;
  overflow: hidden;
  font-size: 26rpx;
}

.table-header {
  display: flex;
  background-color: #f7f7f7;
  border-bottom: 1rpx solid #eeeeee;
}

.table-row {
  display: flex;
  border-bottom: 1rpx solid #eeeeee;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  word-break: break-word;
}

.type-cell {
  flex: 1;
  font-weight: bold;
  color: #333333;
}

.usage-cell {
  flex: 2;
  color: #666666;
}

.required-cell {
  flex: 1;
  color: #666666;
}

/* 底部留出空白，确保内容不被遮挡 */
.bottom-space {
  height: 60rpx;
}
</style>