<template>
  <view class="business">
    <view class="but but1" v-if="!show" @click="show = true">
      <image src="/static/imgs/kaolaStudent/shou.svg"></image>
      <view>{{ list[original_type - 1].text }}</view>
    </view>
    <view class="list" v-if="show">
      <image
        @click="show = false"
        class="showbusiness"
        src="/static/imgs/kaolaStudent/showbusiness.svg"
      ></image>
      <view class="item" v-for="item of list" @click="tap(item)">
        <image :src="item.icon"></image>
        <text>{{ item.text }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {},
  data() {
    return {
      show: false,
      list: [
        {
          icon: '/static/imgs/kaolaStudent/business1.svg',
          text: 'AI答疑',
          id: '1'
        },
        {
          icon: '/static/imgs/kaolaStudent/business2.svg',
          text: '清北答疑',
          id: '2'
        }
        // {
        //   icon: '/static/imgs/kaolaStudent/business3.svg',
        //   text: '清北伴学',
        //   id: '3'
        // }
      ]
    }
  },
  computed: {
    original_type() {
      return this.$store.state.kaolaStudent.original_type
    }
  },
  methods: {
    tap(item) {
      if (item.id == '1') {
        this.$store.commit('kaolaStudent/setOriginalType', item.id)
        this.show = false
      }
      if (item.id == '2') {
        this.$store.commit('kaolaStudent/setOriginalType', item.id)
        this.show = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.business {
  display: flex;
  align-items: center;
}
.list {
  display: flex;
  align-items: center;
  .item {
    margin-left: 4px;
    background: #ffffff;
    border-radius: 12px;
    height: 36px;
    width: 91px;
    // padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    image {
      width: 16px;
      height: 16px;
      margin-right: 4px;
    }
    text {
      font-weight: 400;
      font-size: 12px;
      color: #222333;
    }
  }
  .showbusiness {
    width: 24px;
    height: 24px;
  }
}
.but1 {
  // width: 86px;
  height: 36px;
  background: #ffffff;
  border-radius: 10px;
  display: flex;
  align-items: center;
  padding-left: 4px;
  padding-right: 12px;
  view {
    font-weight: 500;
    font-size: 12px;
    color: #222333;
  }
  image {
    width: 28px;
    height: 28px;
  }
}
</style>
