<!-- 直播间回放的页面 -->
<template>
  <view class="course-play">
    <u-navbar
      title="查看详情"
      bgColor="rgba(255,255,255,0)"
      @rightClick="rightClick"
      :autoBack="true"
    >
    </u-navbar>
    <view class="nav-block"></view>
    <view class="video-box">
      <video
        id="video"
        v-if="videoUrl"
        class="video-box"
        :src="videoUrl"
        controls
        autoplay
      ></video>
    </view>
    <view class="play-bottom">
      <!-- 
      <view class="play-count">
        <u-icon name="eye" color="#999"></u-icon>
        <text class="count-class">{{ pv }}</text>
      </view> -->

      <view class="course-info">
        <view class="title">{{ info.name }}</view>

        <view class="tearcher-wrap">
          <u-avatar :src="avatarUrl" :size="28"></u-avatar>
          <text class="teacher-name">{{ info.teacher_name }}</text>
          <UniversityBadge :schoolName="info.school_name"></UniversityBadge>
        </view>
        <view class="course-content">
          <view>内容说明:</view>
          <view class="content">{{ info.description }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import UniversityBadge from '../../components/UniversityBadge/index.vue'
import { square } from '../../api'

export default {
  name: 'coursePlay',
  components: {
    UniversityBadge
  },
  data() {
    return {
      query: null,
      pv: 0,
      avatarUrl: '',
      videoUrl: '',
      mainImage: '',
      status: 0,
      info: {
        name: '',
        description: '',
        school_name: '',
        teacher_name: '',
        live_url: ''
      },
      teacher_id: 0,
      live_room_id: 0,
      vip_level: 0
    }
  },
  onLoad(query) {
    console.log(query)
    this.query = query
    this.videoUrl = this.customComplete(this.query.replay_url)
    this.getLiveDetail()
  },
  methods: {
    getLiveDetail() {
      const params = {}
      if (this.query.id) {
        params.goods_id = this.query.id
      } else if (this.query.system_id) {
        params.system_id = this.query.system_id
      }

      square.getLiveDetail(params).then(res => {
        this.mainImage = this.$xh.completepath(res.data.material_cover_path)
        this.avatarUrl = this.$xh.completepath(res.data.avatar)
        this.info.name = res.data.name
        this.info.description = res.data.description
        this.info.school_name = res.data.school_name
        this.info.teacher_name = res.data.teacher_name
        this.replay_url = res.data.replay_url
        this.status = res.data.status
        this.live_room_id = res.data.live_room_id
        this.teacher_id = res.data.teacher_id
        this.vip_level = res.data.vip_level
      })
    },
    customComplete(path) {
      if (path && path.startsWith('http')) {
        return path
      } else if (path) {
        return this.$xh.completepath(path)
      } else {
        return ''
      }
    },
    rightClick() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.video-box {
  width: 100%;
  height: 56.25vw;
}

.course-play {
  width: 100%;
  height: 100vh;
  padding: 40rpx 14rpx;

  background-image: url('https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/626f174562773210963083_Group%20272%402x.png');
  background-repeat: no-repeat;
  background-position: 12.92% -7.5%;

  .title {
    font-size: 34rpx;
    padding-top: 40rpx;
    font-weight: 600;
    color: #333;
    font-family: 'PuHuiTi_75_SemiBold';
  }

  .nav-block {
    height: 142rpx;
  }

  .tearcher-wrap {
    padding-top: 40rpx;
    font-size: 28rpx;
    color: #999;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .teacher-name {
      font-weight: 400;
      font-size: 28rpx;
      font-family: 'PuHuiTi_55_Regular';
      padding: 0 16rpx;
      color: #000;
    }
  }

  .live-content {
    padding-top: 40rpx;
    color: #666;
    font-family: 'PuHuiTi_55_Regular';
    font-weight: 400;
    font-size: 26rpx;
  }

  .content {
    padding-top: 20rpx;
  }
}

.play-bottom {
  padding: 32rpx;

  .play-count {
    font-size: 28rpx;
    color: #999;
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  .count-class {
    margin-left: 16rpx;
  }

  .teacher-wrap {
    padding-top: 30rpx;
    font-size: 28rpx;
    color: #999;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .teacher-name {
      padding-right: 10rpx;
      margin-left: 8rpx;
    }
  }

  .info-title {
    margin-top: 30rpx;
    font-weight: 600;
    height: 48rpx;
    line-height: 48rpx;
    font-size: 34rpx;
    color: #333;
  }

  .course-content {
    font-size: 26rpx;
    padding-top: 32rpx;
    color: #666;

    .content {
      font-size: 26rpx;
      padding-top: 14rpx;
    }
  }
}
</style>
