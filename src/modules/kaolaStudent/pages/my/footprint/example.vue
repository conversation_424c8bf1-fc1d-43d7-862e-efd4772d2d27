<template>
  <view class="example-page">
    <view class="top">
      <view class="label">题目{{ index + 1 }}/{{ list.length }}</view>
      <view class="label2" v-if="false"
        ><view> 难度 </view>
        <image
          src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/405d174438548352625487_Group%2093%402x.png"
        ></image>
        <image
          src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/295e174438551379952679_Group%2094%402x.png"
        ></image>
      </view>
    </view>
    <swiper class="body" :current="index" @change="swiperChange">
      <swiper-item v-for="(info, infoIndex) of list" :key="infoIndex">
        <view class="scroll">
          <view v-if="false">
            <view class="question-box">
              <view class="title"> 2023的相反数是( ) </view>

              <view class="select">
                <view class="item">
                  <view class="icon">A</view>
                  <view class="text">5555</view>
                  <image
                    class="img"
                    src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/3789174438751958626301_Frame%402x.png"
                  ></image>
                </view>
                <view class="item correct">
                  <view class="icon">B</view>
                  <view class="text">5555</view>
                  <image
                    class="img"
                    src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/3789174438751958626301_Frame%402x.png"
                  ></image>
                </view>
                <view class="item">
                  <view class="icon">C</view>
                  <view class="text">5555</view>
                  <image
                    class="img"
                    src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/3789174438751958626301_Frame%402x.png"
                  ></image>
                </view>
                <view class="item error">
                  <view class="icon">D</view>
                  <view class="text">5555</view>
                  <image
                    class="img"
                    src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/3789174438751958626301_Frame%402x.png"
                  ></image>
                </view>
              </view>
            </view>
            <view class="fengexian">
              <view class="content">
                <image
                  class="img1"
                  src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/8912174438570549592080_Star%202%402x.png"
                ></image>
                <view class="text">答对了！</view>
                <image
                  class="img2"
                  src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/8cb8174438571712399461_Union%402x.png"
                ></image>
              </view>
            </view>
            <view>
              <view class="box-head">
                <view>正确答案</view>
              </view>
              <view class="answer">B</view>
            </view>
          </view>

          <view>
            <view class="box-head">
              <view>分析与解答</view>
            </view>
            <view class="text-box">
              <view class="head"> 【问题】 </view>
              <view class="text" v-html="info.question"> </view>
            </view>
            <view class="text-box">
              <view class="head"> 【讲解】 </view>
              <view class="text" v-html="info.answer"> </view>
            </view>
            <view class="text-box">
              <view class="head"> 【评价】 </view>
              <view class="text" v-html="info.analysis"> </view>
            </view>
          </view>
        </view>
      </swiper-item>
    </swiper>

    <view class="bottom" v-if="query.title == '举一反三'">
      <view class="but-group">
        <view class="but" @click="push(1)">
          <image
            src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/4581174438509520165784_Frame%402x(1).png"
          ></image>
          <view>AI 答疑</view>
        </view>
        <view class="but" @click="push(2)">
          <image
            src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/1fa3174438510806832675_Frame%402x(2).png"
          ></image>
          <view>清北答疑</view>
        </view>
      </view>
      <view class="next-but" v-if="index != 0" @click="change(1)">上一题</view>
      <view class="next-but" v-if="index != list.length - 1" @click="change(2)"
        >下一题</view
      >
    </view>
  </view>
</template>

<script>
import { student } from '../../../api/index'
import { json_parse } from '../../../utils'
export default {
  data() {
    return {
      query: {
        id: ''
      },
      list: [],

      index: 0
    }
  },
  onLoad(query) {
    this.query = query
    wx.setNavigationBarTitle({
      title: query.title
    })
    if (query.type == '1') {
      this.relationdata()
    }
    if (query.type == '2') {
      this.report()
    }
  },
  methods: {
    push(type) {
      this.$store.commit('kaolaStudent/setOriginalType', type)
      uni.switchTab({
        url: `/modules/kaolaStudent/pages/tabBar/home`
      })
    },
    swiperChange({ detail: { current } }) {
      console.log(current, 'current')
      this.index = current
    },
    change(type) {
      if (type == 1) {
        this.index--
      }
      if (type == 2) {
        this.index++
      }
      if (this.index <= 0) {
        this.index = 0
      }
      if (this.index >= this.list.length) {
        this.index = this.list.length - 1
      }
    },
    relationdata() {
      student.question
        .relationdata({
          id: this.query.id
        })
        .then(res => {
          let dd = res.data
          if (Array.isArray(dd)) {
            this.list = dd
          } else {
            this.list = [dd]
          }
        })
    },
    report() {
      student.question
        .report({
          id: this.query.id
        })
        .then(res => {
          this.list = [json_parse(res.data)]
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.question-box {
  padding: 10px 0;
  .title {
    font-weight: 400;
    font-size: 15px;
    color: rgba(34, 35, 51, 0.85);
    line-height: 20px;
    padding-bottom: 16px;
  }
  .select {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .item {
      margin-bottom: 12px;
      width: 166px;
      background: #ffffff;
      border-radius: 8px 8px 8px 8px;
      border: 1px solid rgba(34, 35, 51, 0.35);
      padding: 10px;
      display: flex;
      align-items: center;

      .icon {
        width: 24px;
        text-align: center;
        height: 24px;
        line-height: 24px;
        background: #eaedf1;
        border-radius: 4px 4px 4px 4px;
        font-weight: 500;
        font-size: 14px;
        color: #222333;
        margin-right: 12px;
      }
      .text {
        font-weight: 400;
        font-size: 14px;
        color: rgba(34, 35, 51, 0.85);
        line-height: 20px;
      }
      .img {
        display: none;
      }
    }
    .correct {
      border: 1px solid #60d631;
      .icon {
        background: #60d631;
        color: #ffffff;
      }
    }
    .error {
      position: relative;
      border: 1px solid #ff3c1a;
      .icon {
        background: #ff3c1a;
        color: #ffffff;
      }
      .img {
        display: block;
        width: 16px;
        height: 16px;
        position: absolute;
        bottom: 0;
        right: 0;
      }
    }
  }
}
.box-head {
  background-image: url(https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/6468174438619927734982_Rectangle%20280%402x.png);
  background-repeat: no-repeat;
  background-size: 55.79px 26.78px;
  background-position-y: 5px;
  padding: 8px 0;
  view {
    font-weight: 500;
    font-size: 16px;
    color: #222333;
    line-height: 20px;
  }
}
.answer {
  padding: 8px 0;
  font-weight: 500;
  font-size: 14px;
  color: rgba(34, 35, 51, 0.85);
  line-height: 20px;
}
.fengexian {
  width: 100%;
  border-bottom: 1px dashed rgba(214, 219, 227, 0.95);
  position: relative;
  margin-top: 15px;
  margin-bottom: 15px;
  .content {
    background-color: #fff;
    position: absolute;
    left: 50%;
    top: -14px;

    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    // width: 84px;
    height: 29px;
    background: #ffffff;
    border-radius: 0px 0px 0px 0px;
    .text {
      font-weight: 500;
      font-size: 14px;
      color: #222333;
      line-height: 20px;
    }
    .img1 {
      width: 16.73px;
      height: 16.73px;
    }
    .img2 {
      position: relative;
      top: -5px;
      left: -4px;
      width: 12.98px;
      height: 16.86px;
    }
  }
}
.example-page {
  height: 100vh;
  overflow-y: auto;
  background-color: #fff;
  .body {
    height: calc(100% - 44px - 88px);
    .scroll {
      padding: 0 16px 0 16px;
      height: 100%;
      overflow-y: auto;
    }
  }
  .top {
    padding: 10px 16px;
    display: flex;
    align-items: center;
    .label {
      margin-right: 8px;
      width: 68px;
      text-align: center;
      height: 24px;
      line-height: 24px;
      background: #eaff00;
      border-radius: 4px 4px 4px 4px;
      font-weight: bold;
      font-size: 14px;
      color: #222333;
    }
    .label2 {
      display: flex;
      align-items: center;
      // min-width: 76px;
      height: 24px;
      background: #eaedf1;
      border-radius: 4px 4px 4px 4px;
      padding: 0 8px;
      view {
        font-weight: bold;
        font-size: 14px;
        color: #222333;
        line-height: 20px;
      }
      image {
        width: 16px;
        height: 16px;
      }
    }
  }
  .bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100vw;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    padding: 12px 16px 32px 16px;
    z-index: 1;
    box-shadow: 0px -1px 0px 0px rgba(233, 233, 233, 0.6);
    .but-group {
      display: flex;
      align-items: center;
      .but {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        margin-right: 24px;
        image {
          width: 16px;
          height: 16px;
          margin-right: 4px;
        }
        view {
          font-weight: 400;
          font-size: 13px;
          color: #222333;
          line-height: 20px;
        }
      }
    }
    .next-but {
      margin-left: 8px;
      width: 88px;
      text-align: center;
      height: 44px;
      line-height: 44px;
      background: #353648;
      border-radius: 33px 33px 33px 33px;
      font-weight: bold;
      font-size: 14px;
      color: #ffffff;
    }
  }
  .text-box {
    padding-top: 8px;
    font-weight: 400;
    font-size: 14px;
    color: #222333;
    line-height: 22px;
    padding-bottom: 8px;
    .head {
      position: relative;
      left: -6px;
      font-weight: bold;
      font-size: 14px;
      color: #222333;
      line-height: 20px;
      margin-bottom: 4px;
    }
    .text {
      white-space: pre-line;
    }
  }
}
</style>
