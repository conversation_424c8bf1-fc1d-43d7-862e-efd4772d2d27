<template>
  <view class="login-mask" v-if="true">
    <view class="login-box">
      <view class="close" @click="close"></view>
      <view class="title">验证码登录/注册</view>
      <view class="input">
        <input
          type="text"
          v-model="form.phone"
          placeholder="请输入手机号"
          placeholder-class="input-placeholder"
        />
      </view>
      <view class="input">
        <input
          type="text"
          v-model="form.imgCodeValue"
          placeholder="请输入图片码"
        />
        <text class="code img-code" @click="getCodeImg">
          <img :src="codeImgUrl" alt="" />
        </text>
      </view>
      <view class="input">
        <input type="text" v-model="form.code" placeholder="请输入验证码" />
        <text class="code" @click="getCode">{{ tip }}</text>
      </view>
      <view class="button" @click="login"></view>
      <view class="yinsi">
        <u-checkbox-group v-model="form.checked" placement="column">
          <u-checkbox
            activeColor="#ff6616"
            :customStyle="{ marginBottom: '4rpx' }"
          >
          </u-checkbox>
        </u-checkbox-group>

        <view class="tip">
          <text>进入即代表已同意</text>
          <text>《用户协议》</text><text>《隐私政策》</text>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
import { myApi } from '../../api/index'
export default {
  name: 'login-h5',
  props: {
    show: {
      default: false
    }
  },
  data() {
    return {
      codeImgUrl: '',
      codeImgId: '',
      form: {
        phone: '',
        imgCodeValue: '', // 用户输入的图片验证码
        checked: [],
        code: ''
      },
      tip: '获取验证码',
      time: 60
    }
  },
  mounted() {
    this.getCodeImg()
  },
  methods: {
    expPhone() {
      //定义正则表达式
      let reg =
        '^((13[0-9])|(14[5,7])|(15[0-3,5-9])|(17[0,3,5-9])|(18[0-9])|166|198|199|191|(147))\\d{8}$'
      //创建正则表达式对象
      let regExp = new RegExp(reg)
      //使用test()函数验证数据是否匹配正则表达式，匹配返回true,否则返回false
      return regExp.test(this.form.phone)
    },
    close() {
      this.$emit('close')
    },
    login() {
      // 校验
      if (!this.form.phone) {
        this.message('请输入手机号')
        return
      }
      if (!this.expPhone()) {
        this.message('请输入正确的手机号')
        return
      }
      if (!this.form.imgCodeValue) {
        this.message('请输入图片验证码')
        return
      }
      if (!this.form.imgCodeValue) {
        this.message('请输入图片验证码')
        return
      }
      if (!this.form.checked.length) {
        this.message('请阅读并同意协议')
        return
      }

      // // 登录
      myApi
        .loginH5({
          phone: this.form.phone,
          code: this.form.code
        })
        .then(data => {
          this.message('登录成功！')
          this.$store.commit('kaolaStudent/setToken', data.data.token)
          this.$emit('close')
        })
    },
    setTimer() {
      this.time--
      this.tip = `${this.time}s后重新获取`
      setTimeout(() => {
        if (this.time <= 0) {
          this.time = 0
          this.tip = '获取验证码'
        } else {
          this.setTimer()
        }
      }, 1000)
    },
    getCode() {
      // 校验
      if (!this.form.phone) {
        this.message('请输入手机号')
        return
      }
      if (!this.expPhone()) {
        this.message('请输入正确的手机号')
        return
      }
      if (!this.form.imgCodeValue) {
        this.message('请输入图片验证码')
        return
      }

      myApi
        .sendCode2({
          phone: this.form.phone,
          scene: 2,
          captcha_id: this.codeImgId,
          captcha_val: this.form.imgCodeValue
        })
        .then(res => {
          this.message('验证码发送成功！')
          this.setTimer()
        })
        .catch(err => {
          this.form.imgCodeValue = ''
        })
      if (this.time < 60) {
        return
      }
      // 获取验证码
    },

    message(title, time = 1500) {
      uni.showToast({
        icon: 'none',
        title: title,
        duration: time
      })
    },
    getCodeImg() {
      myApi.getCodeImg({ mode: 'digit' }).then(res => {
        this.codeImgUrl = res.data.captcha
        this.codeImgId = res.data.captcha_id
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.login-mask {
  width: 100%;
  height: 100%;
  position: fixed;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  .login-box {
    width: 600rpx;
    background-color: #fff;
    border-radius: 15rpx;
    position: relative;
    padding: 34rpx 64rpx;
    .close {
      width: 48rpx;
      height: 48rpx;
      background: url('https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/4763174382170172365845_16924138399652c4f169241383996585621_close.png')
        no-repeat;
      background-size: cover;
      position: absolute;
      right: 32rpx;
      top: 32rpx;
    }
    .title {
      text-align: center;
      font-size: 44rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 800;
      color: #000000;
      line-height: 60px;
    }
    .input {
      position: relative;
      input {
        width: 100%;
        height: 88rpx;
        background: #f3f5f9;
        border-radius: 16rpx;
        padding: 0 32rpx;
        font-size: 28rpx;
        margin-bottom: 32rpx;
      }
      .input-placeholder {
        font-size: 28rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(3, 32, 61, 0.45);
        line-height: 40px;
      }
      .code {
        position: absolute;
        right: 32rpx;
        top: 0;
        bottom: 0;
        margin: auto 0;
        font-size: 26rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ff6616;
        line-height: 88rpx;
      }
      .img-code {
        right: 0;
        border-radius: 0 15rpx 15rpx 0;
        overflow: hidden;
        img {
          width: 150rpx;
          height: 100%;
        }
      }
    }
    .button {
      height: 89rpx;
      background: url('https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/43a0174382167140791565_16924144327251f2c169241443272555103_logon.png')
        no-repeat;
      background-size: contain;
    }
    .yinsi {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 26rpx;
      .checkbox {
        width: 24rpx;
        height: 24rpx;
        border: 1px solid #ff6616;
        margin-right: 12rpx;
      }
      .tip {
        display: flex;
        align-items: center;
        color: #5b6e81;
        font-size: 24rpx;
        text {
          flex-shrink: 0;
          color: #ff6616;
        }
        text:first-child {
          color: #5c6f82;
        }
      }
    }
  }
}
</style>
