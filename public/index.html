<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title><%= htmlWebpackPlugin.options.title %></title>
    <!-- 企微应用sdk -->
    <!-- <script
      src="https://res.wx.qq.com/open/js/jweixin-1.2.0.js"
      type="text/javascript"
      async="false"
    ></script> -->
    <!--    <script-->
    <!--      src="https://res.wx.qq.com/wwopen/js/jsapi/jweixin-1.0.0.js"-->
    <!--      type="text/javascript"-->
    <!--      async="false"-->
    <!--    ></script>-->
    <!-- <script
      src="https://res.wx.qq.com/wwopen/js/jsapi/jweixin-1.0.0.js"
      type="text/javascript"
    ></script>
    <script
      src="https://res.wx.qq.com/open/js/jweixin-1.4.0.js"
      type="text/javascript"
      async="false"
    ></script> -->

    <script>
      document.addEventListener('DOMContentLoaded', function () {
        document.documentElement.style.fontSize =
          document.documentElement.clientWidth / 20 + 'px'
      })
      var coverSupport =
        'CSS' in window &&
        typeof CSS.supports === 'function' &&
        (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'))
      document.write(
        '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
          (coverSupport ? ', viewport-fit=cover' : '') +
          '" />'
      )
    </script>

    <link
      rel="stylesheet"
      href="<%= BASE_URL %>static/index.<%= VUE_APP_INDEX_CSS_HASH %>.css"
    />
  </head>

  <body>
    <noscript>
      <strong>Please enable JavaScript to continue.</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
  </body>
  <!-- uni 的 SDK -->
  <!--    <script type="text/javascript" src="../src/modules/xingyunapp_gzh/static/uni.webview.js"></script>-->
  <!-- 引入微信小程序兼容文件，官网上还有其他平台的兼容文件，这里只用了微信小程序，所以就不判断环境了 -->
  <!-- <script type="text/javascript">
    document.write(
      '<script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.4.0.js"><\/script>'
    )
  </script> -->
  <!-- 需要下载 uni.webview.1.5.5.js 并修改源码,前面笔者有提供修改文件 -->
  <script
    type="module"
    src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/17230083657661951172300836576624740_uni.webview.1.5.5.js.js"
  ></script>
</html>
