<template>
  <view class="information-page">
    <navbar class="navbar" title=" "></navbar>
    <view class="scroll">
      <view class="avatar-box">
        <view class="avatar" @click="chooseMedia">
          <image class="img" :src="$xh.completepath(form.avatar)" mode="aspectFill"></image>
          <image
            class="icon"
            src="/static/imgs/kaolaStudent/xiangjiavatar.svg"
          ></image>
        </view>
      </view>
      <view class="form-item">
        <view class="label">姓名</view>
        <view class="content">
          <input
            v-model="form.name"
            placeholder="请输入"
            placeholder-style="font-size: 14px;color: rgba(34,35,51,0.45);"
          />
        </view>
      </view>

      <view class="form-item">
        <view class="label">性别</view>
        <view class="content">
          <view class="sex">
            <view
              class="ee"
              :class="{
                active: form.sex == 1
              }"
              @click="form.sex = 1"
              >男生
              <image
                class="sexselectIcon"
                src="/static/imgs/kaolaStudent/sexselectIcon.svg"
              >
              </image
            ></view>
            <view
              class="ee"
              :class="{
                active: form.sex == 2
              }"
              @click="form.sex = 2"
              >女生
              <image
                class="sexselectIcon"
                src="/static/imgs/kaolaStudent/sexselectIcon.svg"
              >
              </image>
            </view>
          </view>
        </view>
      </view>

      <view class="form-item">
        <view class="label">生日</view>
        <view class="content">
          <picker
            mode="date"
            v-model="form.birthday"
            start="1975-01-01"
            end="2030-01-01"
            @change="({ detail: { value } }) => (form.birthday = value)"
          >
            <view class="select">
              <view v-if="form.birthday" class="value">{{
                form.birthday
              }}</view>
              <view v-else class="placeholder">请选择</view>
              <image class="pushIcon" :src="pushIcon"></image> </view
          ></picker>
        </view>
      </view>

      <view class="form-item">
        <view class="label">年级</view>
        <view class="content">
          <selectStageGrade
            v-model="form.stageGrade"
            :name.sync="stageGradeName"
          >
            <view class="select">
              <view v-if="stageGradeName.length" class="value"
                >{{ stageGradeName[0] }}/{{ stageGradeName[1] }}
              </view>
              <view v-else class="placeholder">请选择</view>
              <image class="pushIcon" :src="pushIcon"></image> </view
          ></selectStageGrade>
        </view>
      </view>

      <view class="form-item">
        <view class="label">目标学科</view>
        <view class="content">
          <pickerSelectCode
            v-model="form.subject_id"
            :name.sync="form.subject_id_name"
            code="subject"
          >
            <view class="select">
              <view v-if="form.subject_id_name" class="value"
                >{{ form.subject_id_name }}
              </view>
              <view v-else class="placeholder">请选择</view>
              <image class="pushIcon" :src="pushIcon"></image> </view
          ></pickerSelectCode>
        </view>
      </view>

      <view class="form-item">
        <view class="label">教师偏好</view>
        <view
          class="content"
          @click="$xh.push('kaolaStudent', 'pages/register/index?step=2')"
        >
          <view class="select">
            <view v-if="form.teach_style_ids_name" class="value ellipsis-row1"
              >{{ form.teach_style_ids_name }}
            </view>
            <view v-else class="placeholder">请选择</view>
            <image class="pushIcon" :src="pushIcon"></image>
          </view>
        </view>
      </view>

      <view class="form-item">
        <view class="label">自我评价</view>
        <view
          class="content"
          @click="$xh.push('kaolaStudent', 'pages/register/index?step=2')"
        >
          <view class="select">
            <view v-if="form.introduce" class="value ellipsis-row1"
              >{{ form.introduce }}
            </view>
            <view v-else class="placeholder">请输入</view>
            <image class="pushIcon" :src="pushIcon"></image>
          </view>
        </view>
      </view>
      <view style="height: 124px"></view>
    </view>

    <view class="page-bottom-but page-bottom-but-fixed" @click="submit"
      >保存
    </view>
  </view>
</template>

<script>
import { student, config } from '../../api/index'
import { upLoad } from '../../utils'
import navbar from '../../components/commen/navbar.vue'
import pickerSelectMulti2 from '../../components/commen/picker-select-multi2.vue'
import pickerSelect from '../../components/commen/picker-select.vue'
import pickerSelectCode from '../../components/commen/picker-select-code.vue'
import selectStageGrade from '../../components/commen/select-stage-grade.vue'
import dayjs from 'dayjs'
export default {
  components: {
    navbar,
    pickerSelectMulti2,
    pickerSelect,
    selectStageGrade,
    pickerSelectCode
  },
  data() {
    return {
      pushIcon:
        '/static/imgs/kaolaStudent/17208581114338a0b172085811143335824_push.png',
      form: {
        avatar: '',
        name: '',
        sex: '',
        birthday: '',
        stageGrade: [],
        subject_id: '',
        subject_id_name: '',
        teach_style_ids: '',
        teach_style_ids_name: '',
        introduce: ''
      },
      stageGradeName: []
    }
  },
  computed: {
    userinfo2() {
      return this.$store.state.kaolaStudent.userinfo2
    }
  },
  watch: {
    userinfo2() {
      this.form.teach_style_ids = this.userinfo2.teach_style_ids
      this.form.teach_style_ids_name = this.userinfo2.teach_style_ids_name
      this.form.introduce = this.userinfo2.introduce
    }
  },
  onLoad() {
    this.form.avatar = this.userinfo2.avatar
    this.form.name = this.userinfo2.name
    this.form.teach_style_ids = this.userinfo2.teach_style_ids
    this.form.teach_style_ids_name = this.userinfo2.teach_style_ids_name
    this.form.introduce = this.userinfo2.introduce
    this.form.sex = this.userinfo2.sex
    this.form.subject_id = this.userinfo2.subject_id
    this.form.subject_id_name = this.userinfo2.subject_id_name
    this.form.birthday = this.userinfo2.birthday
    if (this.userinfo2.grade_id != '0') {
      this.stageGradeName = [
        this.userinfo2.stage_id_name,
        this.userinfo2.grade_id_name
      ]
      this.form.stageGrade = [this.userinfo2.stage_id, this.userinfo2.grade_id]
    }
  },
  onShow() {},
  methods: {
    submit() {
      if (this.form.name.length == 0) {
        this.$xh.Toast('请输入姓名')
        return
      }
      if (this.form.name.length < 2) {
        this.$xh.Toast('请填写2个字以上的姓名')
        return
      }
      student
        .studentPost({
          ...this.form,
          stage_id: this.form.stageGrade[0],
          grade_id: this.form.stageGrade[1]
        })
        .then(res => {
          this.$xh.back()
          student.detailv2()
        })
      console.log(this.form)
    },
    previewImage(url) {
      wx.previewImage({
        current: url,
        urls: this.form.fileList
      })
    },
    chooseMedia() {
      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        maxDuration: 30,
        camera: 'back',
        success: res => {
          upLoad(res.tempFiles[0].tempFilePath)
            .then(url => {
              this.form.avatar = url
            })
            .catch(res => {
              this.$xh.Toast('上传文件失败！')
            })
        }
      })
    },
    // 删除图片
    deletePic(index) {
      this.form.fileList.splice(index, 1)
    }
  }
}
</script>
<style lang="scss">
.information-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  .form-item {
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(214, 219, 227, 0.35);
    .label {
      font-weight: 400;
      font-size: 16px;
      color: #222333;
    }
    .content {
      width: calc(100% - 100px);
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .select {
        display: flex;
        align-items: center;
        width: 100%;
        .pushIcon {
          width: 18px;
          height: 18px;
          margin-left: 4px;
        }
        .placeholder {
          font-weight: 500;
          font-size: 16px;
          color: rgba(34, 35, 51, 0.45);
        }
        .value {
          text-align: end;
          width: 100%;
          font-weight: 500;
          font-size: 16px;
          color: #222333;
        }
      }
      input {
        text-align: end;
      }

      .sex {
        display: flex;
        align-items: center;
        .ee {
          width: 115px;
          text-align: center;
          height: 44px;
          line-height: 44px;
          background: #ffffff;
          border-radius: 8px 8px 8px 8px;
          margin-left: 8px;
          position: relative;
          overflow: hidden;
          .sexselectIcon {
            display: none;
          }
        }
        .active {
          border: 1px solid #222333;
          .sexselectIcon {
            position: absolute;
            display: block;
            width: 16px;
            height: 16px;
            bottom: 0;
            right: 0;
            z-index: 1;
          }
        }
      }
    }
  }
  .scroll {
    padding: 0 16px;
    flex: 1;
    height: 500px;
    overflow-y: auto;
  }
}
.avatar-box {
  display: flex;
  padding: 16px 0;
  align-items: center;
  justify-content: center;
  .avatar {
    width: 80px;
    height: 80px;
    background: #999faa;
    border-radius: 50%;
    position: relative;
    .img {
      border-radius: 50%;
      width: 100%;
      height: 100%;
    }
    .icon {
      position: absolute;
      width: 26px;
      height: 26px;
      right: -2px;
      bottom: -2px;
    }
  }
}
</style>
