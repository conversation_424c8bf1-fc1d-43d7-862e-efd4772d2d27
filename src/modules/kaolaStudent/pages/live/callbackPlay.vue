<template>
  <view class="course-play">
    <view class="video">
      <video
        id="video"
        v-if="videoUrl"
        class="video-box"
        :src="videoUrl"
        controls
        autoplay
      ></video>
    </view>
    <view class="play-bottom">
      <view class="play-count">
        <u-icon name="eye" color="#999"></u-icon>
        <text class="count-class">{{ pv }}</text>
      </view>

      <view class="course-info">
        <view class="info-title">{{ name }}</view>

        <!-- <view class="teacher-wrap">
          <u-avatar :src="avatarUrl"></u-avatar>
          <text class="teacher-name">刘老师</text>
          <text>清华</text>
        </view> -->
        <view class="course-content">
          <view>内容说明:</view>
          <view class="content">{{ description }}</view>
        </view>
      </view>
    </view>
  </view>
</template>



<script>
import { square } from '../../api'

export default {
  name: 'coursePlay',
  data() {
    return {
      query: null,
      pv: 0,
      name: '',
      teacher_name: '',
      description: '',
      avatarUrl:
        'https://xy-kaola.oss-cn-beijing.aliyuncs.com/merchantid_385164383640140890/389806835466550453/resumes/1757125666/583169428993085165.noRMX8htnQK76a9f4109dd80c4a8210d740a1e773482.png',
      videoUrl: ''
    }
  },
  onLoad(query) {
    this.query = query
    this.getCallbackDetail()
  },
  methods: {
    customComplete(path) {
      if (path && path.startsWith('http')) {
        return path
      } else if (path) {
        return this.$xh.completepath(path)
      } else {
        return ''
      }
    },
    getCallbackDetail() {
      let params = {}

      if (this.query.id) {
        params.goods_id = this.query.id
      } else if (this.query.system_id) {
        params.system_id = this.query.system_id
      }
      square.getCallbackDetail(params).then(res => {
        console.log('录播详情： ', res)

        square.addPv({
          relation_resource_id: res.data.relation_resource_id
        })
        console.log('接口返回', res.data.path)
        this.videoUrl = this.$xh.completepath(res.data.path)

        console.log('拼接后', this.videoUrl)
        this.pv = res.data.pv
        this.description = res.data.description
        this.name = res.data.name
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.video-box {
  width: 100vw;
  height: 56.25vw;
}

.play-bottom {
  padding: 32rpx;

  .play-count {
    font-size: 28rpx;
    color: #999;
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  .count-class {
    margin-left: 16rpx;
  }

  .teacher-wrap {
    padding-top: 30rpx;
    font-size: 28rpx;
    color: #999;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .teacher-name {
      padding-right: 10rpx;
      margin-left: 8rpx;
    }
  }

  .info-title {
    margin-top: 30rpx;
    font-weight: 600;
    height: 48rpx;
    line-height: 48rpx;
    font-size: 34rpx;
    color: #333;
  }

  .course-content {
    font-size: 26rpx;
    padding-top: 32rpx;
    color: #666;

    .content {
      font-size: 26rpx;
      padding-top: 14rpx;
    }
  }
}
</style>
