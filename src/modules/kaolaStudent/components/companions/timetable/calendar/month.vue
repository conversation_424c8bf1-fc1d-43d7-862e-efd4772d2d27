<!-- 伴学 -预约 -课表 -->
<template>
  <swiper
    class="month-swiper"
    :style="{
      height: monthSwiperheight
    }"
    @change="swiperChange"
    circular
    :current="current"
  >
    <swiper-item v-for="(month, index) in monthList" :key="index">
      <view class="month-container">
        <view class="table-row">
          <view
            v-for="(day, i) in month.days"
            :key="i"
            class="day-cell"
            :class="{
              'other-month': !day.isCurrentMonth,
              'selected-day': isSelected(day.date, value),
              status1: isData(day.date)
            }"
            @click="selectDate(day.date)"
          >
            <view class="text">
              {{ day.date.getDate() }}
            </view>
          </view>
        </view>
      </view>
    </swiper-item>
  </swiper>
</template>

<script>
import dayjs from 'dayjs'

export default {
  props: {
    data: {
      type: Array,
      default: []
    },
    value: {
      type: String,
      defaul: ''
    },
    initDate: {
      type: String,
      defaul: ''
    }
  },
  components: {},
  data() {
    return {
      monthList: [
        {
          date: new Date()
        },
        {
          date: new Date()
        },
        {
          date: new Date()
        }
      ],
      page: 0,
      lastCurrent: 0,
      current: 0,
      currentDate: new Date(),
      title: ''
    }
  },
  created() {
    this.init(0)
  },
  computed: {
    monthSwiperheight() {
      const current = this.monthList[this.monthCurrent]
      if (current) {
        return `calc(4px + 50px * ${Math.floor(current.days.length / 7)})`
      } else {
        return 'calc(4px + 50px * 5 )'
      }
    }
  },
  methods: {
    last() {
      this.current--
      if (this.current < 0) {
        this.current = 2
      }
    },
    next() {
      this.current++
      if (this.current > 2) {
        this.current = 0
      }
    },
    swiperChange({ detail: { current } }) {
      let num = this.lastCurrent - current
      let direction = true
      if (num == -1 || num == 2) {
        this.page++
        direction = true
      } else {
        this.page--
        direction = false
      }
      if (current == 0 && direction) {
        this.init(0)
      }
      if (current == 2 && !direction) {
        this.init(-2)
      }
      this.change(current)
      this.lastCurrent = current
    },
    // 获取月数据
    getMonthData(date) {
      const year = date.getFullYear()
      const month = date.getMonth()
      const firstDay = new Date(year, month, 1)
      const lastDay = new Date(year, month + 1, 0)
      const days = []

      // 填充上月天数
      const startDay = firstDay.getDay() == 0 ? 6 : firstDay.getDay() - 1
      for (let i = 0; i < startDay; i++) {
        const d = new Date(year, month, -startDay + i + 1)
        days.push({
          date: d,
          isCurrentMonth: false
        })
      }

      // 填充本月天数
      for (let i = 1; i <= lastDay.getDate(); i++) {
        const d = new Date(year, month, i)
        days.push({
          date: d,
          isCurrentMonth: true
        })
      }

      // 填充下月天数
      const endDay = lastDay.getDay() - 1
      for (let i = 1; i < 7 - endDay; i++) {
        const d = new Date(year, month + 1, i)
        days.push({
          date: d,
          isCurrentMonth: false
        })
      }

      return { year, month, days }
    },
    init(val) {
      for (let i = 0; i < 3; ++i) {
        let dd = dayjs(this.initDate).toDate()
        dd.setMonth(dd.getMonth() + this.page + i + val)
        this.monthList[i] = this.getMonthData(dd)
      }
      this.change(0)
    },
    change(val) {
      const current = this.monthList[val]
      this.current = val
      let monthList = current.days.filter(e => e.isCurrentMonth)
      this.$emit('change', {
        start: dayjs(monthList[0].date).format('YYYY-MM-DD'),
        end: dayjs(monthList[monthList.length - 1].date).format('YYYY-MM-DD')
      })
    },
    // 选择日期
    selectDate(date) {
      this.$emit('select', dayjs(date).format('YYYY-MM-DD'))
      this.$emit('input', dayjs(date).format('YYYY-MM-DD'))
    },
    isData(d1) {
      return !!this.data.find(d2 => dayjs(d1).format('YYYY-MM-DD') == d2)
    },
    isSelected(d1, d2) {
      return dayjs(d1).format('YYYY-MM-DD') == dayjs(d2).format('YYYY-MM-DD')
    }
  }
}
</script>
<style scoped lang="scss">
.month-swiper {
  height: calc(36px * 5);
  .month-container {
    display: flex;
    flex-direction: column;
  }
}
.table-row {
  width: 100%;
  display: flex;
  padding-top: 4px;
  padding-bottom: 2px;
  flex-wrap: wrap;
  .day-cell {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    // flex: 1;
    width: calc(100% / 7);
    margin-bottom: 16px;

    .text {
      font-weight: 800;
      font-size: 14px;
      color: rgba(34, 35, 51, 0.85);
      width: 32px;
      height: 32px;
      // background: #dbf46b;
      border-radius: 8px 8px 8px 8px;
      line-height: 32px;
      text-align: center;
    }
  }
  .status1 {
    .text {
      position: relative;
      &::before {
        content: ' ';
        position: absolute;
        bottom: 0;
        left: calc(50%);
        transform: translateX(-50%);
        width: 7px;
        height: 4px;
        background: #9ab71b;
        border-radius: 8px 8px 3px 3px;
      }
    }
  }
  .selected-day {
    .text {
      background: #dbf46b;
      // border-radius: 50%;
    }
  }
  .other-month {
    .text {
      color: #ccc;
    }
  }
}
</style>
