<template>
  <div>
    <navbar class="navbar" title=" "></navbar>
    <view>
      <view class="price">
        <view class="name">支付金额</view>
        <view class="num">¥ {{ info.payable_amount }}</view>
      </view>
    </view>
    <view class="pay-type-box">
      <view
        class="item"
        v-for="item of payTypeList"
        @click="payType = item.type"
      >
        <image class="icon" :src="item.icon"></image>
        <view class="name">{{ item.name }}</view>
        <image
          v-if="payType == item.type"
          class="select"
          src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/5c2c174556120900434110_Frame%402x%20(1).png"
        ></image>
        <image
          v-else
          class="select"
          src="https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/3363174556122068819659_Frame%402x%20(2).png"
        ></image>
      </view>
    </view>
    <view class="page-bottom-but page-bottom-but-fixed pay-but" @click="pay()"
      >确认支付</view
    >
  </div>
</template>

<script>
import { appoint, account } from '../../../api'
import { app_id } from '../../../config'
import navbar from '../../../components/commen/navbar.vue'
export default {
  components: {
    navbar
  },
  data() {
    return {
      payType: '1',
      payTypeList: [
        {
          icon: 'https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/6cb417455610220111689_Frame%402x.png',
          name: '钱包支付',
          type: '1'
        },
        {
          icon: 'https://xy-kaola.oss-cn-beijing.aliyuncs.com/public/4919174556110221565314_Group%20265%402x.png',
          name: '微信支付',
          type: '2'
        }
      ],
      query: {},
      info: { payable_amount: '' }
    }
  },
  onLoad(query) {
    this.query = query
    this.detail(query.order_id)
  },
  methods: {
    detail(id) {
      appoint.order.DayiDetail({ order_id: id }).then(res => {
        this.info = res.data
        this.goods_id = res.data.goods[0].goods_id
      })
    },
    pay() {
      if (this.payType == '1') {
        this.payorder()
      }
      if (this.payType == '2') {
        this.wechatapplet()
      }
    },
    payorder() {
      wx.showModal({
        title: '提示',
        content: '确认使用钱包支付',
        success: res => {
          if (res.confirm) {
            uni.showLoading({
              title: '支付中...'
            })
            account
              .payorder({
                flow_id: this.query.flow_id
              })
              .then(res => {
                uni.hideLoading()
                this.$xh.redirect(
                  'kaolaStudent',
                  'pages/my/order/success?order_id=' + this.query.order_id
                )
              })
              .catch(() => {
                uni.hideLoading()
              })
          } else if (res.cancel) {
            console.log('用户点击取消')
          }
        }
      })
    },
    wechatapplet() {
      account
        .payParams({
          flow_id: this.query.flow_id,
          order_id: this.query.order_id,
          goods_ids: this.goods_id
        })
        .then(res => {
          const payParams = {
            appId: app_id,
            timeStamp: res.data.time_stamp,
            nonceStr: res.data.nonce_str,
            signType: res.data.sign_type,
            paySign: res.data.pay_sign,
            package: res.data.package,
            finance_body_id: res.data.finance_body_id
          }

          this.$xh.pay(
            payParams,
            () => {
              this.$xh.redirect(
                'kaolaStudent',
                'pages/my/order/success?order_id=' + this.query.order_id
              )
            },
            () => {
              this.$xh.Toast('支付失败！')
            }
          )
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.pay-type-box {
  margin: 0 12px;
  padding: 6px 0;
  background: #ffffff;
  border-radius: 12px 12px 12px 12px;
  .item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 18px 16px;
    .icon {
      width: 22px;
      height: 22px;
      margin-right: 12px;
    }
    .name {
      font-weight: 400;
      font-size: 16px;
      color: #222333;
      line-height: 20px;
    }
    .select {
      margin-left: auto;
      width: 16px;
      height: 16px;
    }
  }
}
.pay-but {
  background: #ff6111;
}
.price {
  text-align: center;
  padding-top: 32px;
  padding-bottom: 40px;
  .name {
    font-weight: 400;
    font-size: 14px;
    color: rgba(34, 35, 51, 0.85);
  }
  .num {
    font-weight: 500;
    font-size: 28px;
    color: #222333;
  }
}
</style>
