<template>
  <div>
    <view class="identify-text">{{ result }}</view>

    <button
      type="primary"
      size="default"
      @click="startLy"
      class="btn btn-start"
      v-if="!recording"
    >
      开始识别
    </button>
    <button
      type="primary"
      size="default"
      @click="endLy"
      class="btn btn-end"
      v-if="recording"
    >
      停止识别
    </button>
  </div>
</template>

<script>
const plugin = requirePlugin('QCloudAIVoice')
let resultText = ''
export default {
  data() {
    return {
      speechRecognizerManager: null,
      result: '',
      recording: false
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.speechRecognizerManager = plugin.speechRecognizerManager()

      // 开始识别
      this.speechRecognizerManager.OnRecognitionStart = res => {
        console.log('开始识别', res)
        this.setData({
          recording: true,
          result: ''
        })
      }
      // 一句话开始
      this.speechRecognizerManager.OnSentenceBegin = res => {
        console.log('一句话开始', res)
      }
      // 识别变化时
      this.speechRecognizerManager.OnRecognitionResultChange = res => {
        console.log('识别变化时', res)
        this.setData({
          result: `${resultText || ''}${res.result.voice_text_str}`
        })
      }
      // 一句话结束
      this.speechRecognizerManager.OnSentenceEnd = res => {
        console.log('一句话结束', res)
        resultText += res.result.voice_text_str
        this.setData({
          result: resultText
        })
      }
      // 识别结束
      this.speechRecognizerManager.OnRecognitionComplete = res => {
        console.log('识别结束', res)
        this.setData({
          recording: false
        })
      }
      // 识别错误
      this.speechRecognizerManager.OnError = res => {
        console.log(res)
        this.setData({
          recording: false
        })
      }
      this.speechRecognizerManager.OnRecorderStop = () => {
        console.log('超过录音时长')
      }
    },
    startLy: async function (e) {
      const self = this
      wx.getSetting({
        success(res) {
          if (!res.authSetting['scope.record']) {
            wx.authorize({
              scope: 'scope.record',
              success() {
                // 用户已经同意小程序使用录音功能，后续调用 record 接口不会弹窗询问
                self.startAsr()
              },
              fail() {
                wx.showToast({ title: '未获取录音权限', icon: 'none' })
                // console.log("fail auth")
              }
            })
          } else {
            self.startAsr()
            // console.log("record has been authed")
          }
        },
        fail(res) {
          // console.log("fail",res)
        }
      })
    },
    startAsr: function () {
      wx.showToast({
        title: '建立连接中',
        icon: 'none'
      })
      resultText = ''
      const params = {
        // 用户参数
        appid: 1327029879,
        secretid: 'AKIDJagaTJNKQTM4znTxPfpodHj074pOlzXu',
        secretkey: 'HMGyYwBHB0f9QXTDl7JKNSXWefgYFl4E',
        // 录音参数
        // duration: 100000,
        // frameSize: 1.28,  //单位:k
        // 实时识别接口参数
        engine_model_type: '16k_zh',
        // 以下为非必填参数，可跟据业务自行修改
        // hotword_id : '08003a00000000000000000000000000',
        needvad: 1,
        // filter_dirty: 1,
        // filter_modal: 2,
        filter_punc: 1,
        // convert_num_mode : 1,
        word_info: 2,
        vad_silence_time: 200
      }

      this.speechRecognizerManager.start(params)

      wx.vibrateShort()
    },
    endLy: function (e) {
      this.setData({
        recording: false
      })
      this.speechRecognizerManager.stop()
    },
    setData(obj) {
      for (let key of Object.keys(obj)) {
        this[key] = obj[key]
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
