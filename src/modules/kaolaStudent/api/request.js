import getHttp, { newRequest } from '@utlis/request'
import Base64 from '../../../utlis/base64'
import { noToastUrl, noCheckoutLogin, visitorMode } from './httpRequestConfig'
import store from '../store/index'
import $xh from '../../../utlis/index'
// 设置标识id
let cacheIdentId = {
  // id缓存
}

function setIdentify(header = {}) {
  // let path = $xh.getPath()
  let id = ''
  // if (cacheIdentId[path]) {
  //   id = cacheIdentId[path]
  // } else {
  //   store.state.menus.forEach(item => {
  //     if ('modules/xingyunapph5/' + item.link == path) {
  //       id = item.id
  //       // 设置缓存
  //       cacheIdentId[path] = id
  //     }
  //   })
  // }
  if (id) {
    let baseencode = Base64.encode(id)
    header['x-menu-identy'] = baseencode
  } else {
    let baseencode = Base64.encode('0')
    header['x-menu-identy'] = baseencode
  }
}

function setPlatForm(header = {}) {
  header['x-platform-id'] = Base64.encode('403573477228945388')
}

export default newRequest(
  (function () {
    // return 'https://xingyundev.jinyingjie.com'
    return process.env.VUE_APP_BASE_API
  })()
)({
  noToastUrl,
  noCheckoutLogin,
  normal_code: 100000, // 正常码
  handlerStatusCode: {
    // 特殊装态吗处理
    // uni.clearStorage()
    100002: function () {
      $xh.Toast('登录过期，请重新登录')
      store.mutations.updata(store.state)
      uni.clearStorage()
      // wx.switchTab({
      //   url: '/modules/kaolaStudent/pages/my/index'
      // })
    }
  },
  checkLogin(header = {}, url) {
    console.log('checkLogin', process.env.VUE_APP_MERCHANTID)
    header['x-merchant-id'] = Base64.encode(process.env.VUE_APP_MERCHANTID)
    header['x-brand-id'] = Base64.encode(process.env.VUE_APP_BRANDID)
    if (visitorMode.includes(url)) {
      return true
    }

    // return true
    // 校验是否登录
    if (store.state.token) {
      // 在header上加上相关东西
      header['x-token'] = store.state.token
      // 设置标识
      setIdentify(header)
      // 设置平台
      setPlatForm(header)
      return true
    } else {
      // wx.switchTab({
      //   url: '/modules/kaolaStudent/pages/my/index'
      // })
      return false
    }
  }
})
